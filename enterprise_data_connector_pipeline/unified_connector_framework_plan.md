# Revised Implementation Plan: Unified Connector Framework (Backend Focus)

## 1. Overview & Goals Recap

*   **Objective**: Develop a standardized backend framework for ingesting data from GDrive, Jira, Gmail, and GitHub into a central platform, processing it, and storing it in Vector and Knowledge Graph databases.
*   **Core Idea**: A modular connector architecture where each connector adheres to a common lifecycle and contract, managed via backend APIs.
*   **Key Outcomes**: Reusability, consistency, scalability, and simplified programmatic onboarding/management of the specified connectors.

## 2. Leveraging `enterprise_kg_minimal`

The `enterprise_kg_minimal` project provides a strong foundation, especially for the latter stages of the connector lifecycle (`chunk`, `analyze`, `store`). We should aim to:

*   **Adapt Core Logic**: Refactor components like `enterprise_kg_minimal/core/chunking_engine.py`, `enterprise_kg_minimal/core/graph_builder.py`, `enterprise_kg_minimal/core/prompt_generator.py`, and `enterprise_kg_minimal/core/coreference_resolver.py` to be more generic and usable by the `BaseConnector`.
*   **Storage Clients**: Utilize and extend the existing `enterprise_kg_minimal/storage/neo4j_client.py` and the Pinecone setup indicated in `enterprise_kg_minimal/.env` to support pluggable storage backends.
*   **Constants & Schemas**: Build upon `enterprise_kg_minimal/constants/` (e.g., `enterprise_kg_minimal/constants/entities.py`, `enterprise_kg_minimal/constants/relationships.py`, `enterprise_kg_minimal/constants/schemas.py`) for defining connector-specific schemas and ensuring consistency.
*   **LLM Integration**: The existing `enterprise_kg_minimal/llm/client.py` can be the basis for LLM interactions in the `analyze` stage.

## 3. Proposed Architecture (Backend Focused)

```mermaid
graph TD
    subgraph "Data Sources"
        DS1[GDrive API]
        DS2[Jira API]
        DS3[Gmail API]
        DS4[GitHub API]
    end

    subgraph "Unified Connector Framework (Backend)"
        UCF_CORE[Framework Core Orchestrator]
        RegBackendAPI[Connector Registration & Management API]

        subgraph "Connectors (Plug-ins)"
            C1[GDrive Connector]
            C2[Jira Connector]
            C3[Gmail Connector]
            C4[GitHub Connector]
        end

        C1 -- Implements --> BaseConnector
        C2 -- Implements --> BaseConnector
        C3 -- Implements --> BaseConnector
        C4 -- Implements --> BaseConnector

        BaseConnector["
            <b>BaseConnector Interface</b><br/>
            - fetch_data(config)<br/>
            - preprocess(raw_data)<br/>
            - chunk(preprocessed_data)<br/>
            - analyze(chunks)<br/>
            - store(analysis_output)
        "]

        UCF_CORE -- Manages/Executes --> C1
        UCF_CORE -- Manages/Executes --> C2
        UCF_CORE -- Manages/Executes --> C3
        UCF_CORE -- Manages/Executes --> C4

        RegBackendAPI -- Controls --> UCF_CORE

        subgraph "Shared Services & Libraries (from enterprise_kg_minimal)"
            ChunkingLib[Chunking Engine]
            AnalysisLib[Analysis Engine (LLM, Embeddings, NER, Coref)]
            StorageLib[Storage Adapters]
            SecretMgmt[Secret Management (e.g., Vault)]
            AsyncQueue[Async Task Queue (e.g., Celery/Redis)]
        end

        BaseConnector -- Uses --> ChunkingLib
        BaseConnector -- Uses --> AnalysisLib
        BaseConnector -- Uses --> StorageLib
    end

    subgraph "Data Storage (Pluggable)"
        VectorDB[Vector DB (Pinecone, Qdrant)]
        GraphDB[Knowledge Graph DB (Neo4j, Memgraph)]
    end

    StorageLib -- Writes to --> VectorDB
    StorageLib -- Writes to --> GraphDB

    DS1 --> C1
    DS2 --> C2
    DS3 --> C3
    DS4 --> C4

    style BaseConnector fill:#f9f,stroke:#333,stroke-width:2px
    style UCF_CORE fill:#ccf,stroke:#333,stroke-width:2px
    style RegBackendAPI fill:#d9ead3,stroke:#333,stroke-width:2px
```

## 4. Detailed Implementation Phases (Revised)

### Phase 1: Define Base Connector SDK and Lifecycle Interface (Duration: 1 week)

*   **Task 1.1: Define `BaseConnector` Abstract Class**
    *   Implement the Python abstract base class (`ABC`).
    *   Define clear input/output types for each method.
    *   Establish common utility functions or context objects.
*   **Task 1.2: Develop Core Processing Utilities (Leveraging `enterprise_kg_minimal`)**
    *   **Chunking Service**: Adapt `enterprise_kg_minimal/core/chunking_engine.py`.
    *   **Analysis Service**: Refactor LLM interaction, embedding generation, coreference resolution from `enterprise_kg_minimal`.
    *   **Storage Service**: Design abstract storage interface; implement initial adapters for Neo4j and a Vector DB.
*   **Task 1.3: Framework Orchestrator (Initial Version)**
    *   Develop a simple orchestrator to load and run a connector.
    *   Implement basic configuration management.
*   **Task 1.4: Secret Management Integration**
    *   Integrate with a secret vault or use environment variables initially.
*   **Task 1.5: Asynchronous Execution and Retry Framework (Proof of Concept)**
    *   Choose a task queue system (e.g., Celery).
    *   Design how lifecycle stages can be independent, retryable tasks.
*   **Task 1.6: Design Connector Registration System (Backend Focus)**
    *   Define metadata for connector registration.
    *   Design a persistent store for connector configurations.
*   **Deliverables**:
    *   `BaseConnector` abstract class definition.
    *   Core utility services (Chunking, Analysis, Storage).
    *   Basic framework orchestrator.
    *   Connector registration schema and database design.
    *   Documentation for the SDK and lifecycle.

### Phase 2: Develop Initial Connectors (GDrive & Jira) (Duration: 1 week)

*   **Task 2.1: Develop GDrive Connector**
    *   Create `GDriveConnector(BaseConnector)`.
    *   Implement `fetch_data()`: Use Google Drive API with **Service Account authentication**.
    *   Implement `preprocess()`, `chunk()`, `analyze()`, `store()` using shared services.
    *   Define GDrive-specific schemas.
    *   Add unit tests.
*   **Task 2.2: Develop Jira Connector**
    *   Create `JiraConnector(BaseConnector)`.
    *   Implement `fetch_data()`: Use Jira API.
    *   Implement `preprocess()`, `chunk()`, `analyze()`, `store()` using shared services.
    *   Define Jira-specific schemas.
    *   Add unit tests.
*   **Task 2.3: Refine Framework Orchestrator**
    *   Enhance orchestrator for GDrive and Jira configurations.
*   **Task 2.4: Iterate on Asynchronous Execution and Retries**
    *   Apply async/retry pattern to GDrive and Jira connector stages.
*   **Deliverables**:
    *   Functional GDrive (with Service Account auth) and Jira connectors.
    *   Unit tests for both connectors.
    *   Updated orchestrator and documentation.

### Phase 3: Develop Additional Connectors (Gmail & GitHub) & Backend Registration API (Duration: 1 week)

*   **Task 3.1: Develop Gmail Connector**
    *   Create `GmailConnector(BaseConnector)`.
    *   Implement `fetch_data()`: Use Gmail API with **Service Account authentication**.
    *   Implement `preprocess()` (parse emails, headers, attachments).
    *   Implement `chunk()`, `analyze()`, `store()` using shared services.
    *   Define Gmail-specific schemas.
    *   Add unit tests.
*   **Task 3.2: Develop GitHub Connector**
    *   Create `GitHubConnector(BaseConnector)`.
    *   Implement `fetch_data()`: Use GitHub API (issues, PRs, files, commits).
    *   Implement `preprocess()`.
    *   Implement `chunk()`, `analyze()`, `store()` using shared services.
    *   Define GitHub-specific schemas.
    *   Add unit tests.
*   **Task 3.3: Develop Registration Backend API**
    *   Create APIs (e.g., FastAPI/Flask) for:
        *   Registering connector types.
        *   Creating/configuring instances of the 4 connectors.
        *   Listing/managing connector instances.
        *   Triggering connector runs programmatically.
        *   Storing/retrieving configurations securely.
*   **Task 3.4: Integrate Backend API with Orchestrator**
    *   Ensure APIs can trigger registration and execution via the orchestrator.
*   **Deliverables**:
    *   Functional Gmail (with Service Account auth) and GitHub connectors with tests.
    *   Connector Registration & Management Backend API.
    *   Documentation for the backend API.

### Phase 4: Finalize Test Suite and Monitoring Integrations (Duration: 1 week)

*   **Task 4.1: Comprehensive Test Suite (Backend Focus)**
    *   Develop integration tests for the framework and the 4 connectors.
    *   Ensure 100% lifecycle coverage for GDrive, Jira, Gmail, GitHub.
    *   Implement end-to-end tests for these connectors.
*   **Task 4.2: Monitoring and Logging**
    *   Integrate structured logging.
    *   Set up centralized logging (e.g., ELK, Loki).
    *   Implement metrics for lifecycle stages.
    *   Expose metrics for monitoring (e.g., Prometheus).
    *   Develop dashboards.
*   **Task 4.3: Documentation Review and Finalization (Backend Focus)**
    *   Update developer documentation (SDK, architecture, backend API).
*   **Deliverables**:
    *   Comprehensive test suite (unit, integration, E2E for 4 connectors).
    *   Integrated monitoring and logging solution.
    *   Finalized backend-focused documentation.

## 5. Cross-Cutting Concerns

*   **Storage Agnosticism**: `StorageService` with well-defined interfaces; adapters for target DBs.
*   **Security**: Secure credential management; data encryption.
*   **Error Handling and Retries**: Configurable retry policies; dead-letter queues; clear error reporting.
*   **Configuration Management**: Centralized framework config; per-connector instance config via Registration Backend.
*   **Scalability**: Design orchestrator and async system for many connectors and high data volumes.

## 6. Technology Stack Considerations (Backend Focus)

*   **Core Framework/Connectors**: Python
*   **Async Task Queue**: Celery with Redis or RabbitMQ
*   **Registration & Management API**: FastAPI or Flask (Python)
*   **Databases**:
    *   Vector DBs: Pinecone, Qdrant
    *   Graph DBs: Neo4j, Memgraph
    *   Connector Registry: PostgreSQL, MongoDB
*   **Secret Management**: HashiCorp Vault, AWS Secrets Manager, Azure Key Vault, or environment variables (for dev)
*   **Monitoring**: Prometheus, Grafana
*   **Logging**: ELK Stack or Grafana Loki