# Decision Log

This file records architectural and implementation decisions using a list format.
2025-06-05 11:44:20 - Updated during memory bank population task.

*

## Decision (2025-06-05)

*   **Decision:** Populate the memory bank files (`productContext.md`, `activeContext.md`, `progress.md`, `decisionLog.md`, `systemPatterns.md`) by systematically extracting and summarizing information from the `enterprise_kg_minimal` project's documentation (READMEs) and code structure (module definitions).
*   **Rationale:** To ensure the memory bank files accurately reflect the `enterprise_kg_minimal` project's purpose, architecture, key features, and established patterns. This provides a reliable and up-to-date contextual foundation for any future work or understanding of the project.
*   **Implementation Details:**
    *   Information was sourced primarily from [`enterprise_kg_minimal/README.md`](enterprise_kg_minimal/README.md), [`enterprise_kg_minimal/search/README.md`](enterprise_kg_minimal/search/README.md), and insights derived from the `list_code_definition_names` output for the `core` and `search` modules.
    *   Each memory bank file was updated according to the specific sections outlined in the `memory-bank/update_plan.md` document.
    *   The process involved reading project files, analyzing code definitions, and then writing the summarized information into the respective memory bank files.
---
[2025-06-05 13:19:00] - **Decision**: Made relevance score calculation weights in `GraphRAG` configurable.
**Rationale**: To allow for easier tuning and experimentation with different scoring heuristics without direct code modification. This enhances flexibility for optimizing search relevance across various datasets and query types.
**Implementation Details**:
    - Added new float fields to `SearchQuery` in `enterprise_kg_minimal/search/search_schemas.py` for weights related to:
        - Initial entity query relevance boost.
        - Neighbor entity depth penalty and confidence boost (traditional).
        - Hybrid neighbor entity query relevance and depth penalty.
        - Query relevance components (overlap, semantic, type).
        - Keyword overlap components (Jaccard, coverage).
    - Updated methods in `enterprise_kg_minimal/search/graph_rag.py` (`_extract_entities_from_chunks`, `_find_neighboring_entities`, `_find_neighboring_entities_hybrid`, `_calculate_query_entity_relevance`, `_calculate_keyword_overlap`) to use these weights from the `SearchQuery` object instead of hardcoded values.
---
[2025-06-17 16:15:00] - **Decision**: Implemented `GDriveConnector` for the Unified Connector Framework.
*   **Rationale**: To enable data ingestion from Google Drive as per the project plan (Task 2.1).
*   **Implementation Details**:
    *   **Authentication**: Uses Google Service Account. The JSON key string is retrieved via `SecretService`.
        *   File: [`unified_connector_framework/connectors/gdrive_connector.py`](unified_connector_framework/connectors/gdrive_connector.py) (method: `_connect_to_gdrive`)
    *   **API Client**: Uses `google-api-python-client` (`googleapiclient.discovery.build`) and `google-auth` (`google.oauth2.service_account.Credentials`).
    *   **Data Fetching**: `fetch_data` method lists files from a specified folder ID and downloads their content. Raw bytes are stored for binary files.
        *   File: [`gdrive_connector.py`](unified_connector_framework/connectors/gdrive_connector.py) (method: `fetch_data`)
    *   **Text Extraction**: `preprocess` method handles text extraction:
        *   PDFs: Uses `pypdf.PdfReader`.
        *   DOCX: Uses `docx.Document`.
        *   Plain Text: Decodes raw bytes (UTF-8 with latin-1 fallback).
        *   File: [`gdrive_connector.py`](unified_connector_framework/connectors/gdrive_connector.py) (method: `preprocess`)
    *   **Service Integration**: `GDriveConnector` constructor accepts instances of `ChunkingService`, `AnalysisService`, and `StorageService`. The `chunk`, `analyze`, and `store` methods delegate to these injected services.
        *   File: [`gdrive_connector.py`](unified_connector_framework/connectors/gdrive_connector.py) (methods: `__init__`, `chunk`, `analyze`, `store`)
    *   **Health Check**: `health_check` method performs a live API call (`self.gdrive_service.about().get(fields="user").execute()`) to verify connectivity.
        *   File: [`gdrive_connector.py`](unified_connector_framework/connectors/gdrive_connector.py) (method: `health_check`)
    *   **Configuration**: Expects `folder_id` and `service_account_secret_name` in its configuration. `file_types_to_process` is also configurable.
    *   **Example Usage**: Updated in [`orchestrator.py`](unified_connector_framework/core/orchestrator.py) and [`gdrive_connector.py`](unified_connector_framework/connectors/gdrive_connector.py) to reflect new dependencies and instantiation.