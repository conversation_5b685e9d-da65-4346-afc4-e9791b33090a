# Active Context

This file tracks the project's current status, including recent changes, current goals, and open questions.
2025-06-05 11:43:56 - Updated during memory bank population task.

*

## Current Focus

*   **Unified Connector Framework Development**: Paused Task 2.2 (`JiraConnector` development) to update Memory Bank.
*   Updating Memory Bank files ([`progress.md`](memory-bank/progress.md), [`activeContext.md`](memory-bank/activeContext.md), [`decisionLog.md`](memory-bank/decisionLog.md)) with progress on `GDriveConnector`.

## Recent Changes

*   **2025-06-17:** Substantially completed Task 2.1: `GDriveConnector` implementation for the Unified Connector Framework.
    *   Defined `GDriveConnector` in [`unified_connector_framework/connectors/gdrive_connector.py`](unified_connector_framework/connectors/gdrive_connector.py).
    *   Implemented GDrive connection using Service Accounts (`_connect_to_gdrive`).
    *   Implemented data fetching (`fetch_data`) with API calls.
    *   Implemented preprocessing (`preprocess`) with text extraction for PDF, DOCX, TXT.
    *   Integrated `ChunkingService`, `AnalysisService`, `StorageService` for `chunk`, `analyze`, `store` methods.
    *   Implemented `health_check` with a live API call.
    *   Updated example usage in [`unified_connector_framework/core/orchestrator.py`](unified_connector_framework/core/orchestrator.py) and [`gdrive_connector.py`](unified_connector_framework/connectors/gdrive_connector.py).
*   **2025-06-17:** Created placeholder structure for `JiraConnector` in [`unified_connector_framework/connectors/jira_connector.py`](unified_connector_framework/connectors/jira_connector.py).
*   **2025-06-17:** Updated `__init__.py` files in `connectors` and `services` directories.
*   **2025-06-05:** Completed initial analysis of the `enterprise_kg_minimal` project.
*   **2025-06-05:** Reviewed and updated memory bank files based on `enterprise_kg_minimal`.

## Open Questions/Issues

*   Decision needed on specific Jira data to fetch (e.g., specific projects, issue types, fields, JQL).
*   Authentication method for Jira (API token preferred, need details like env var name).
---
[2025-06-05 13:19:30] - **Recent Changes**:
    - Modified `enterprise_kg_minimal/search/search_schemas.py` to add configurable weights for relevance scoring to the `SearchQuery` dataclass.
    - Updated `enterprise_kg_minimal/search/graph_rag.py` to utilize these new configurable weights in its relevance scoring logic, replacing previously hardcoded values. This affects how entities and relationships are scored based on query relevance, depth, confidence, and keyword overlap.
[2025-06-05 13:19:30] - **Current Focus**: Finalizing the implementation of configurable relevance score weights in the search module.
[2025-06-17 16:15:00] - **Recent Changes**: Substantially completed `GDriveConnector` (Task 2.1). Key methods implemented including API integration for connection, data fetching, text extraction (PDF, DOCX, TXT), and service delegation for chunk/analyze/store. Health check updated.
[2025-06-17 16:15:00] - **Current Focus**: Paused development of `JiraConnector` (Task 2.2) to update Memory Bank with `GDriveConnector` progress.