Creating a Knowledge Graph from CSV: Hybrid Pipeline

A robust approach to generating a knowledge graph (KG) from CSV files—especially when the schema is unknown or partially structured—is to use a hybrid, schema-inference-driven pipeline. This combines deterministic logic with LLM assistance only when needed.

🛠️ Recommended Hybrid Approach

1. Column Profiling & Type Inference

Inspect headers and sample values.

Classify each column as one of:

Entity identifier (e.g., emails, UUIDs, IDs)

Attribute (free-text, dates, numbers)

Potential foreign key (values that match identifiers in other columns)

Use light ML classification, regex patterns, and uniqueness/value-distribution heuristics.

2. Entity & Relationship Schema Suggestion

Based on profiling, suggest a schema:

High-uniqueness, identifier-like columns → node labels (e.g., :User, :Product)

Name/description/date/status fields → properties

Foreign key-like columns → relationships (e.g., :REPORTS_TO)

Represent schema as a JSON or YAML spec for review.

3. Deterministic Ingestion

For each row in the CSV:

MERGE node types using identifier columns.

SET properties on those nodes.

MERGE relationships using FK columns.

Example Cypher:

MERGE (u:User {id: row.user_id})
SET u.name = row.name, u.email = row.email
MERGE (m:User {id: row.manager_id})
MERGE (u)-[:REPORTS_TO]->(m)

4. LLM-Assisted Clean-Up (Optional)

For ambiguous columns, use LLM prompts like:

“Given these values in column X, is this best modeled as a new node type, a property on an existing node, or a relationship? Reply with JSON.”

Use this selectively on uncertain fields.

5. Vectorization of Rows (Optional)

For semantic search use-cases, concatenate relevant columns into a single text blob.

Embed and store in vector DB with metadata.

6. Review & Iterate

Present inferred schema to an admin for confirmation or correction.

Log low-confidence inferences for tuning heuristics.