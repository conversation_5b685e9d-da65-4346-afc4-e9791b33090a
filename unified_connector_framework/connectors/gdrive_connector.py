"""
GDrive Connector for the Unified Connector Framework.

This connector fetches data from Google Drive, processes it, and prepares it
for ingestion into Vector and Knowledge Graph databases.
It is designed to use Google Service Account for authentication.
"""

import logging
from typing import List, Dict, Any, Optional
import json
import io # For handling file downloads

from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# Text extraction libraries
from pypdf import PdfReader
from docx import Document

from ..core.base_connector import BaseConnector
from ..services.secret_service import SecretService # To fetch SA key details
from ..services.chunking_service import ChunkingService
from ..services.analysis_service import AnalysisService
from ..services.storage_service import StorageService

logger = logging.getLogger(__name__)

class GDriveConnector(BaseConnector):
    """
    Connects to Google Drive to fetch and process files.
    Uses Service Account for authentication.
    """

    def __init__(self,
                 connector_id: str,
                 config: Dict[str, Any],
                 secret_service: SecretService,
                 chunking_service: ChunkingService,
                 analysis_service: AnalysisService,
                 storage_service: StorageService
                 ):
        super().__init__(connector_id, config)
        self.secret_service = secret_service
        self.chunking_service = chunking_service
        self.analysis_service = analysis_service
        self.storage_service = storage_service
        
        self.folder_id = self.config.get("folder_id")
        self.file_types_to_process = self.config.get("file_types", [".pdf", ".docx", ".txt", ".md"]) # Example default
        self.service_account_secret_name = self.config.get("service_account_secret_name") # e.g., "gdrive_connector_sa_key"
        
        self.gdrive_service = None # To be initialized in _connect_to_gdrive

        if not self.folder_id:
            raise ValueError("GDriveConnector: 'folder_id' is required in config.")
        if not self.service_account_secret_name:
            raise ValueError("GDriveConnector: 'service_account_secret_name' for SA key is required in config.")

        self._connect_to_gdrive()

    def _connect_to_gdrive(self):
        """
        Initializes the Google Drive API service using Service Account credentials.
        """
        logger.info(f"[{self.connector_id}] Attempting to connect to Google Drive...")
        sa_key_json_str = self.secret_service.get_secret(self.service_account_secret_name)
        
        if not sa_key_json_str:
            logger.error(f"[{self.connector_id}] Service Account key not found via secret: {self.service_account_secret_name}")
            raise ConnectionError(f"GDrive Service Account key '{self.service_account_secret_name}' not found.")

        try:
            sa_key_info = json.loads(sa_key_json_str)
            credentials = Credentials.from_service_account_info(
                sa_key_info,
                scopes=['https://www.googleapis.com/auth/drive.readonly'] # Read-only access
            )
            self.gdrive_service = build('drive', 'v3', credentials=credentials, static_discovery=False)
            logger.info(f"[{self.connector_id}] Successfully initialized Google Drive service.")
        except json.JSONDecodeError as e:
            logger.error(f"[{self.connector_id}] Failed to parse Service Account JSON: {e}", exc_info=True)
            raise ConnectionError(f"Invalid Service Account JSON: {e}")
        except Exception as e:
            logger.error(f"[{self.connector_id}] Failed to initialize Google Drive service: {e}", exc_info=True)
            raise ConnectionError(f"Failed to connect to GDrive: {e}")

    def fetch_data(self) -> List[Dict[str, Any]]:
        """
        Fetches files from the specified GDrive folder.
        Each item in the list will be a dictionary containing file metadata and content.
        """
        if not self.gdrive_service:
            self._connect_to_gdrive() # Attempt to reconnect if service is not available

        logger.info(f"[{self.connector_id}] Fetching data from GDrive folder: {self.folder_id}")
        raw_files_data = []
        page_token = None
        
        try:
            while True:
                query = f"'{self.folder_id}' in parents and trashed = false"
                results = self.gdrive_service.files().list(
                    q=query,
                    spaces='drive',
                    fields='nextPageToken, files(id, name, mimeType, modifiedTime, fileExtension)',
                    pageToken=page_token
                ).execute()
                
                items = results.get('files', [])
                for item in items:
                    file_name = item.get('name', '')
                    file_extension = item.get('fileExtension', '').lower()
                    if not file_extension and '.' in file_name: # try to get from name if not present
                        file_extension = file_name.split('.')[-1].lower()

                    # Check if file type is in the list of types to process
                    # Ensure self.file_types_to_process stores extensions with a leading dot e.g. [".pdf", ".txt"]
                    if not any(f".{file_extension}" == ft.lower() for ft in self.file_types_to_process if file_name.lower().endswith(ft.lower())) \
                       and not any(item.get('mimeType') == ft for ft in self.file_types_to_process if not ft.startswith('.')): # also check for mime types if specified
                        logger.debug(f"[{self.connector_id}] Skipping file '{file_name}' (type: {item.get('mimeType')}, ext: {file_extension}) as it's not in file_types_to_process.")
                        continue

                    logger.info(f"[{self.connector_id}] Processing file: {file_name} (ID: {item['id']}, Type: {item.get('mimeType')})")
                    file_id = item['id']
                    file_content_str = None
                    raw_bytes = None

                    try:
                        # Request to get file content
                        request = self.gdrive_service.files().get_media(fileId=file_id)
                        fh = io.BytesIO()
                        downloader = request # googleapiclient.http.MediaIoBaseDownload(fh, request) # this is for resumable
                        
                        # For non-resumable, direct execute might work for smaller files or specific types
                        # For larger/binary, MediaIoBaseDownload is better but more complex for this initial step.
                        # Let's try direct execute and catch if it's not suitable.
                        file_content_bytes = request.execute()
                        raw_bytes = file_content_bytes # Store raw bytes for preprocess step

                        # Attempt to decode if it's a known text type, otherwise pass raw_bytes
                        if item.get('mimeType') in ['text/plain', 'text/markdown', 'text/csv', 'application/json']:
                            try:
                                file_content_str = file_content_bytes.decode('utf-8')
                                logger.debug(f"[{self.connector_id}] Decoded '{file_name}' as UTF-8 text.")
                            except UnicodeDecodeError:
                                logger.warning(f"[{self.connector_id}] Could not decode '{file_name}' as UTF-8. Passing raw bytes.")
                                file_content_str = "Error: Could not decode content as UTF-8." # Placeholder for preprocess
                        else:
                            # For binary files (PDF, DOCX, etc.), text extraction will be handled in preprocess
                            file_content_str = f"Binary content for {file_name}. Needs extraction in preprocess."
                            logger.debug(f"[{self.connector_id}] File '{file_name}' is binary. Content will be extracted in preprocess.")

                        raw_files_data.append({
                            "id": file_id,
                            "name": file_name,
                            "mime_type": item.get('mimeType'),
                            "modified_time": item.get('modifiedTime'),
                            "content": file_content_str, # This might be text or a placeholder for binary
                            "raw_bytes": raw_bytes, # Pass raw bytes for preprocessing stage
                            "source": "gdrive",
                            "connector_id": self.connector_id
                        })
                    except HttpError as error:
                        logger.error(f"[{self.connector_id}] An HTTP error occurred while fetching file '{file_name}' (ID: {file_id}): {error}", exc_info=True)
                    except Exception as e:
                        logger.error(f"[{self.connector_id}] Failed to download or process file '{file_name}' (ID: {file_id}): {e}", exc_info=True)
                
                page_token = results.get('nextPageToken', None)
                if not page_token:
                    break
        except HttpError as error:
            logger.error(f"[{self.connector_id}] An HTTP error occurred while listing files: {error}", exc_info=True)
            # Depending on policy, could raise or return partial data
        except Exception as e:
            logger.error(f"[{self.connector_id}] An unexpected error occurred during fetch_data: {e}", exc_info=True)
            # Depending on policy, could raise or return partial data

        logger.info(f"[{self.connector_id}] Fetched {len(raw_files_data)} files from GDrive folder '{self.folder_id}'.")
        return raw_files_data

    def preprocess(self, raw_data_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Preprocesses raw file data. For GDrive, this might involve:
        - Extracting text from PDFs, DOCX files.
        - Standardizing metadata.
        """
        logger.info(f"[{self.connector_id}] Preprocessing {len(raw_data_items)} GDrive items.")
        preprocessed_items = []
        for item in raw_data_items:
            text_content = ""
            mime_type = item.get("mime_type", "").lower()
            raw_bytes = item.get("raw_bytes")
            file_name = item.get("name", "Unknown File")

            try:
                if raw_bytes:
                    if mime_type == "application/pdf":
                        try:
                            pdf_reader = PdfReader(io.BytesIO(raw_bytes))
                            for page in pdf_reader.pages:
                                text_content += page.extract_text() or ""
                            logger.debug(f"[{self.connector_id}] Extracted text from PDF: {file_name}")
                        except Exception as e:
                            logger.error(f"[{self.connector_id}] Failed to extract text from PDF '{file_name}': {e}", exc_info=True)
                            text_content = f"Error extracting text from PDF: {file_name}. Error: {e}"
                    elif mime_type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                        try:
                            doc = Document(io.BytesIO(raw_bytes))
                            for para in doc.paragraphs:
                                text_content += para.text + "\n"
                            logger.debug(f"[{self.connector_id}] Extracted text from DOCX: {file_name}")
                        except Exception as e:
                            logger.error(f"[{self.connector_id}] Failed to extract text from DOCX '{file_name}': {e}", exc_info=True)
                            text_content = f"Error extracting text from DOCX: {file_name}. Error: {e}"
                    elif mime_type.startswith("text/"):
                        try:
                            # If content was already decoded in fetch_data, use it
                            if isinstance(item.get("content"), str) and not item["content"].startswith("Error: Could not decode content"):
                                text_content = item["content"]
                                logger.debug(f"[{self.connector_id}] Using pre-decoded text content for: {file_name}")
                            else:
                                text_content = raw_bytes.decode('utf-8')
                                logger.debug(f"[{self.connector_id}] Decoded raw_bytes as text for: {file_name}")
                        except UnicodeDecodeError as e:
                            logger.warning(f"[{self.connector_id}] UnicodeDecodeError for text file '{file_name}', trying latin-1: {e}")
                            try:
                                text_content = raw_bytes.decode('latin-1') # Fallback encoding
                            except Exception as e_latin1:
                                logger.error(f"[{self.connector_id}] Failed to decode text file '{file_name}' with UTF-8 and latin-1: {e_latin1}", exc_info=True)
                                text_content = f"Error decoding text file: {file_name}. Error: {e_latin1}"
                        except Exception as e:
                            logger.error(f"[{self.connector_id}] Failed to process text file '{file_name}': {e}", exc_info=True)
                            text_content = f"Error processing text file: {file_name}. Error: {e}"
                    else:
                        # For other types, content might be a placeholder or error from fetch_data
                        text_content = item.get("content", f"Unsupported mime_type for direct text extraction: {mime_type}")
                        logger.warning(f"[{self.connector_id}] No specific text extraction for mime_type '{mime_type}' for file '{file_name}'. Using content field: '{text_content[:100]}...'")
                elif isinstance(item.get("content"), str): # If no raw_bytes but content is string
                     text_content = item["content"]
                     logger.debug(f"[{self.connector_id}] Using string content directly for '{file_name}' as raw_bytes not present.")
                else:
                    logger.warning(f"[{self.connector_id}] No raw_bytes or suitable string content found for text extraction for file '{file_name}'. Mime type: {mime_type}")
                    text_content = f"No content available for extraction: {file_name}"

                preprocessed_items.append({
                    "document_id": f"gdrive_{item['id']}", # Ensure item['id'] is the GDrive file ID
                    "text_content": text_content.strip(),
                    "metadata": {
                        "source": "gdrive",
                        "original_filename": file_name,
                        "gdrive_file_id": item['id'],
                        "mime_type": mime_type,
                        "modified_time": item.get('modified_time'),
                        "connector_id": self.connector_id,
                        "original_connector_config": self.config # Optional: include connector config for context
                    }
                })
            except Exception as e_outer:
                logger.error(f"[{self.connector_id}] Outer error during preprocessing of item '{item.get('name', 'Unknown Item')}': {e_outer}", exc_info=True)
                # Add a placeholder item or skip, depending on desired error handling
                preprocessed_items.append({
                    "document_id": f"gdrive_error_{item.get('id', 'unknown_id')}",
                    "text_content": f"Failed to preprocess item: {item.get('name', 'Unknown Item')}. Error: {e_outer}",
                    "metadata": {
                        "source": "gdrive",
                        "original_filename": item.get('name', "Unknown File"),
                        "gdrive_file_id": item.get('id', "unknown_id"),
                        "mime_type": item.get("mime_type", "unknown"),
                        "error_in_preprocessing": True,
                        "connector_id": self.connector_id
                    }
                })

        logger.info(f"[{self.connector_id}] Preprocessed into {len(preprocessed_items)} items.")
        return preprocessed_items

    def chunk(self, preprocessed_data_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Chunks preprocessed GDrive data items. Delegates to ChunkingService.
        """
        logger.info(f"[{self.connector_id}] Chunking {len(preprocessed_data_items)} GDrive items using ChunkingService.")
        if not self.chunking_service:
            logger.error(f"[{self.connector_id}] ChunkingService not available.")
            # Optionally, raise an error or return empty list
            raise RuntimeError("ChunkingService not initialized for GDriveConnector.")
        
        try:
            # Assuming chunk_multiple_data_items is the method to use,
            # and it expects a list of items with 'text_content' and 'metadata'.
            # The preprocessed_data_items should fit this structure.
            all_chunks = self.chunking_service.chunk_multiple_data_items(preprocessed_data_items)
            logger.info(f"[{self.connector_id}] Created {len(all_chunks)} chunks via ChunkingService.")
            return all_chunks
        except Exception as e:
            logger.error(f"[{self.connector_id}] Error during chunking with ChunkingService: {e}", exc_info=True)
            # Depending on error handling strategy, re-raise or return empty/partial
            raise

    def analyze(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Analyzes GDrive chunks. Delegates to AnalysisService.
        """
        logger.info(f"[{self.connector_id}] Analyzing {len(chunks)} GDrive chunks using AnalysisService.")
        if not self.analysis_service:
            logger.error(f"[{self.connector_id}] AnalysisService not available.")
            raise RuntimeError("AnalysisService not initialized for GDriveConnector.")

        try:
            # Assuming analyze_multiple_chunks is the method to use.
            analysis_results = self.analysis_service.analyze_multiple_chunks(chunks)
            logger.info(f"[{self.connector_id}] Analyzed {len(analysis_results)} GDrive chunks via AnalysisService.")
            return analysis_results
        except Exception as e:
            logger.error(f"[{self.connector_id}] Error during analysis with AnalysisService: {e}", exc_info=True)
            raise

    def store(self, analysis_outputs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Stores analyzed GDrive data. Delegates to StorageService.
        """
        logger.info(f"[{self.connector_id}] Storing {len(analysis_outputs)} GDrive analysis outputs using StorageService.")
        if not self.storage_service:
            logger.error(f"[{self.connector_id}] StorageService not available.")
            raise RuntimeError("StorageService not initialized for GDriveConnector.")

        try:
            # Assuming store_data is the method to use.
            storage_report = self.storage_service.store_data(analysis_outputs)
            logger.info(f"[{self.connector_id}] Stored GDrive data via StorageService. Report: {storage_report}")
            return storage_report
        except Exception as e:
            logger.error(f"[{self.connector_id}] Error during storage with StorageService: {e}", exc_info=True)
            raise

    def health_check(self) -> Dict[str, Any]:
        logger.info(f"[{self.connector_id}] Performing GDrive health check...")
        try:
            if not self.gdrive_service:
                # Attempt to connect if not already connected.
                # This might happen if __init__ failed softly or was skipped.
                logger.info(f"[{self.connector_id}] GDrive service not initialized. Attempting to connect for health check.")
                self._connect_to_gdrive() # This will raise ConnectionError if it fails

            # Perform a lightweight API call to check connectivity
            user_info = self.gdrive_service.about().get(fields="user").execute()
            email_address = user_info.get("user", {}).get("emailAddress", "Unknown User")
            logger.info(f"[{self.connector_id}] GDrive health check successful. Connected as: {email_address}")
            return {
                "status": "HEALTHY",
                "message": f"GDrive API connection successful. Authenticated as {email_address}.",
                "details": {"user": email_address}
            }
        except ConnectionError as ce: # Catch connection errors from _connect_to_gdrive
            logger.error(f"[{self.connector_id}] GDrive health check failed during connection attempt: {ce}")
            return {"status": "UNHEALTHY", "message": f"Failed to connect to GDrive: {ce}"}
        except HttpError as he:
            logger.error(f"[{self.connector_id}] GDrive health check failed with HttpError: {he.resp.status} - {he._get_reason()}", exc_info=True)
            return {"status": "UNHEALTHY", "message": f"GDrive API error: {he.resp.status} - {he._get_reason()}"}
        except Exception as e:
            logger.error(f"[{self.connector_id}] GDrive health check failed with an unexpected error: {e}", exc_info=True)
            return {"status": "UNHEALTHY", "message": f"An unexpected error occurred: {str(e)}"}

# Example of how to use (conceptual, requires actual service instances and SA key)
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    logger.info("GDriveConnector Example (Conceptual)")

    # Dummy SecretService
    class DummySecretService(SecretService):
        def get_secret(self, secret_name: str) -> Optional[str]:
            if secret_name == "gdrive_sa_key_test":
                # Return a JSON string structure, not a real key for this example
                return """********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""
            return None

    dummy_secrets = DummySecretService()

    # Dummy Service Implementations for the example
    class DummyChunkingService(ChunkingService):
        def chunk_multiple_data_items(self, data_items: List[Dict[str, Any]], **kwargs) -> List[Dict[str, Any]]:
            logger.info("(DummyChunkingService - GDrive Example) Chunking...")
            all_chunks = []
            for i, item in enumerate(data_items):
                all_chunks.append({
                    "chunk_id": f"{item.get('document_id', f'doc{i}')}_chunk_0",
                    "document_id": item.get('document_id', f'doc{i}'),
                    "text": item.get('text_content', '')[:50], # Short text for example
                    "sequence_number": 0,
                    "metadata": item.get('metadata', {})
                })
            return all_chunks

    class DummyAnalysisService(AnalysisService):
        def analyze_multiple_chunks(self, chunks: List[Dict[str, Any]], **kwargs) -> List[Dict[str, Any]]:
            logger.info("(DummyAnalysisService - GDrive Example) Analyzing...")
            return [
                {
                    **chunk, # Keep original chunk data
                    "embedding": [0.5] * 10, # Dummy embedding
                    "entities": [{"name": "ExampleEntity", "type": "Dummy"}],
                    "relationships": []
                } for chunk in chunks
            ]

    class DummyStorageService(StorageService):
        def __init__(self, vector_adapter=None, graph_adapter=None):
            super().__init__(vector_adapter, graph_adapter) # Adapters not used in this dummy
            logger.info("(DummyStorageService - GDrive Example) Initialized.")

        def store_data(self, analysis_outputs: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
            logger.info(f"(DummyStorageService - GDrive Example) Storing {len(analysis_outputs)} items...")
            return {
                "vector_db_stored_count": len(analysis_outputs),
                "graph_db_nodes_created": sum(len(item.get("entities", [])) for item in analysis_outputs),
                "graph_db_rels_created": 0,
                "status": "simulated_success_gdrive_example"
            }

    dummy_chunking_svc = DummyChunkingService()
    dummy_analysis_svc = DummyAnalysisService()
    dummy_storage_svc = DummyStorageService()
    
    gdrive_config = {
        "folder_id": "your_gdrive_folder_id_here",
        "service_account_secret_name": "gdrive_sa_key_test", # Name of the secret in SecretService
        "file_types": [".txt", ".md"]
    }

    try:
        gdrive_connector = GDriveConnector(
            connector_id="gdrive_test_001",
            config=gdrive_config,
            secret_service=dummy_secrets,
            chunking_service=dummy_chunking_svc,
            analysis_service=dummy_analysis_svc,
            storage_service=dummy_storage_svc
        )
        
        logger.info(f"GDriveConnector instantiated: {gdrive_connector.connector_id}")
        health = gdrive_connector.health_check()
        logger.info(f"Health check: {health}")

        if health["status"] == "HEALTHY":
            raw_data = gdrive_connector.fetch_data()
            if raw_data:
                preprocessed = gdrive_connector.preprocess(raw_data)
                if preprocessed:
                    chunks = gdrive_connector.chunk(preprocessed)
                    if chunks:
                        analyzed = gdrive_connector.analyze(chunks)
                        if analyzed:
                            storage_report = gdrive_connector.store(analyzed)
                            logger.info(f"Storage report: {storage_report}")
        
        logger.info("GDriveConnector example run finished.")

    except Exception as e:
        logger.error(f"Error in GDriveConnector example: {e}", exc_info=True)