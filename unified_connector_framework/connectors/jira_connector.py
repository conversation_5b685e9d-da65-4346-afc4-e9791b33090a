"""
Jira Connector for the Unified Connector Framework.

This connector fetches data from Jira (e.g., issues, comments, projects),
processes it, and prepares it for ingestion into Vector and Knowledge Graph databases.
"""

import logging
from typing import List, Dict, Any, Optional

from ..core.base_connector import BaseConnector
from ..services.secret_service import SecretService # To fetch API token or credentials

# Placeholder for Jira API client library
# from jira import JIRA

logger = logging.getLogger(__name__)

class JiraConnector(BaseConnector):
    """
    Connects to Jira to fetch and process project data.
    Supports API token or username/password based authentication (via SecretService).
    """

    def __init__(self, 
                 connector_id: str, 
                 config: Dict[str, Any],
                 secret_service: SecretService
                 # Optional service injections if needed directly:
                 # chunking_service: ChunkingService,
                 # analysis_service: AnalysisService,
                 # storage_service: StorageService
                 ):
        super().__init__(connector_id, config)
        self.secret_service = secret_service
        
        self.jira_url = self.config.get("jira_url")
        self.jql_query = self.config.get("jql_query", "updated >= -7d") # Default: issues updated in last 7 days
        self.auth_method = self.config.get("auth_method", "api_token") # "api_token" or "basic_auth"
        self.auth_secret_name = self.config.get("auth_secret_name") # Name of secret holding credentials

        self.jira_client = None # To be initialized in _connect_to_jira

        if not self.jira_url:
            raise ValueError("JiraConnector: 'jira_url' is required in config.")
        if not self.auth_secret_name:
            raise ValueError("JiraConnector: 'auth_secret_name' for credentials is required in config.")

        self._connect_to_jira()

    def _connect_to_jira(self):
        """
        Initializes the Jira API client using configured credentials.
        """
        logger.info(f"[{self.connector_id}] Attempting to connect to Jira: {self.jira_url}")
        
        credentials_str = self.secret_service.get_secret(self.auth_secret_name)
        if not credentials_str:
            logger.error(f"[{self.connector_id}] Jira credentials not found via secret: {self.auth_secret_name}")
            raise ConnectionError(f"Jira credentials secret '{self.auth_secret_name}' not found.")

        try:
            # TODO: Implement actual Jira client initialization
            # options = {'server': self.jira_url}
            # if self.auth_method == "api_token":
            #     # Expecting "email:api_token" format in the secret
            #     email, api_token = credentials_str.split(':', 1)
            #     self.jira_client = JIRA(options, basic_auth=(email, api_token))
            # elif self.auth_method == "basic_auth":
            #     # Expecting "username:password" format in the secret
            #     username, password = credentials_str.split(':', 1)
            #     self.jira_client = JIRA(options, basic_auth=(username, password))
            # else:
            #     raise ValueError(f"Unsupported Jira auth_method: {self.auth_method}")
            
            logger.info(f"[{self.connector_id}] Successfully initialized Jira client (placeholder).")
            self.jira_client = "DUMMY_JIRA_CLIENT" # Placeholder
        except Exception as e:
            logger.error(f"[{self.connector_id}] Failed to initialize Jira client: {e}", exc_info=True)
            raise ConnectionError(f"Failed to connect to Jira: {e}")

    def fetch_data(self) -> List[Dict[str, Any]]:
        """
        Fetches issues from Jira based on the JQL query.
        Each item in the list will be a dictionary representing a Jira issue.
        """
        if not self.jira_client:
            self._connect_to_jira()

        logger.info(f"[{self.connector_id}] Fetching data from Jira using JQL: {self.jql_query}")
        raw_issues_data = []

        # TODO: Implement actual Jira API calls to search for issues
        # Example pseudo-code:
        # issues = self.jira_client.search_issues(self.jql_query, maxResults=100, expand='changelog,comments') # Adjust maxResults
        # for issue in issues:
        #   issue_data = {
        #       "id": issue.id,
        #       "key": issue.key,
        #       "summary": issue.fields.summary,
        #       "description": issue.fields.description,
        #       "status": issue.fields.status.name,
        #       "reporter": issue.fields.reporter.displayName if issue.fields.reporter else None,
        #       "assignee": issue.fields.assignee.displayName if issue.fields.assignee else None,
        #       "created": issue.fields.created,
        #       "updated": issue.fields.updated,
        #       "project": issue.fields.project.key,
        #       "issue_type": issue.fields.issuetype.name,
        #       "comments": [{"author": c.author.displayName, "body": c.body, "created": c.created} for c in issue.fields.comment.comments],
        #       # Add other relevant fields: labels, priority, components, attachments, links etc.
        #   }
        #   raw_issues_data.append(issue_data)

        # Placeholder implementation:
        logger.warning(f"[{self.connector_id}] fetch_data is using placeholder implementation.")
        raw_issues_data.extend([
            {"id": "1001", "key": "PROJ-123", "summary": "Login button not working", "description": "User cannot log in.", "status": "Open", "source": "jira"},
            {"id": "1002", "key": "PROJ-124", "summary": "Improve dashboard performance", "description": "Dashboard loads slowly.", "status": "In Progress", "source": "jira"}
        ])
        
        logger.info(f"[{self.connector_id}] Fetched {len(raw_issues_data)} issues from Jira.")
        return raw_issues_data

    def preprocess(self, raw_data_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Preprocesses raw Jira issue data.
        - Normalizes JSON structure.
        - Combines relevant fields into a main 'text_content' for chunking/analysis.
        """
        logger.info(f"[{self.connector_id}] Preprocessing {len(raw_data_items)} Jira items.")
        preprocessed_items = []
        for issue in raw_data_items:
            # Combine key text fields for analysis
            text_parts = [
                issue.get("summary", ""),
                issue.get("description", "")
            ]
            comments_text = "\n".join([c.get("body", "") for c in issue.get("comments", []) if c.get("body")])
            if comments_text:
                text_parts.append("Comments:\n" + comments_text)
            
            text_content = "\n\n".join(filter(None, text_parts))

            preprocessed_items.append({
                "document_id": f"jira_{issue['key']}", # Use issue key as document ID
                "text_content": text_content,
                "metadata": {
                    "source": "jira",
                    "jira_issue_id": issue['id'],
                    "jira_issue_key": issue['key'],
                    "jira_status": issue.get("status"),
                    "jira_summary": issue.get("summary"),
                    # Add other relevant Jira fields to metadata
                    "connector_id": self.connector_id
                }
            })
        logger.info(f"[{self.connector_id}] Preprocessed into {len(preprocessed_items)} items.")
        return preprocessed_items

    def chunk(self, preprocessed_data_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Chunks preprocessed Jira items. For Jira, often a single issue or comment
        can be treated as a chunk, or larger descriptions can be split.
        """
        logger.info(f"[{self.connector_id}] Chunking {len(preprocessed_data_items)} Jira items.")
        # For Jira, each preprocessed item (representing an issue with its text)
        # might be considered a single "chunk" unless text_content is very large.
        # Or, use a text splitter if text_content is long.
        # Placeholder: treat each item as one chunk.
        all_chunks = []
        for i, item in enumerate(preprocessed_data_items):
            all_chunks.append({
                "chunk_id": f"{item['document_id']}_chunk_0", # Or more sophisticated chunking
                "document_id": item['document_id'],
                "text": item['text_content'],
                "sequence_number": 0,
                "metadata": item['metadata']
            })
        logger.info(f"[{self.connector_id}] Created {len(all_chunks)} chunks for Jira items.")
        return all_chunks

    def analyze(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Analyzes Jira chunks. Delegates to AnalysisService.
        Focus on extracting users, projects, ticket relationships, sentiment from comments.
        """
        logger.info(f"[{self.connector_id}] Analyzing {len(chunks)} Jira chunks.")
        # TODO: Use the injected self.analysis_service or a globally available one.
        # Placeholder:
        analysis_results = []
        for chunk in chunks:
            analysis_results.append({
                "chunk_id": chunk["chunk_id"],
                "text": chunk["text"],
                "embedding": [0.02] * 128, # Dummy embedding
                "entities": [{"name": "JiraUser", "type": "Person"}, {"name": chunk["metadata"].get("jira_issue_key"), "type": "Issue"}],
                "relationships": [],
                "metadata": chunk["metadata"]
            })
        logger.info(f"[{self.connector_id}] Analyzed {len(analysis_results)} Jira chunks.")
        return analysis_results

    def store(self, analysis_outputs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Stores analyzed Jira data. Delegates to StorageService.
        """
        logger.info(f"[{self.connector_id}] Storing {len(analysis_outputs)} Jira analysis outputs.")
        # TODO: Use the injected self.storage_service or a globally available one.
        # Placeholder:
        vector_stored_count = sum(1 for item in analysis_outputs if "embedding" in item)
        graph_nodes = sum(len(item.get("entities",[])) for item in analysis_outputs)
        graph_rels = sum(len(item.get("relationships",[])) for item in analysis_outputs)
        
        logger.info(f"[{self.connector_id}] Simulated storage for Jira data.")
        return {
            "vector_db_stored_count": vector_stored_count,
            "graph_db_nodes_created": graph_nodes,
            "graph_db_rels_created": graph_rels,
            "status": "simulated_success"
        }

    def health_check(self) -> Dict[str, Any]:
        logger.info(f"[{self.connector_id}] Performing Jira health check...")
        try:
            # TODO: Implement a lightweight API call to Jira to check connectivity
            # e.g., self.jira_client.myself() or self.jira_client.server_info()
            if self.jira_client: # Placeholder check
                 logger.info(f"[{self.connector_id}] Jira health check successful (placeholder).")
                 return {"status": "HEALTHY", "message": "Jira API connection (placeholder) successful."}
            else:
                return {"status": "UNHEALTHY", "message": "Jira client not initialized."}
        except Exception as e:
            logger.error(f"[{self.connector_id}] Jira health check failed: {e}")
            return {"status": "UNHEALTHY", "message": str(e)}

# Example of how to use (conceptual, requires actual service instances and Jira credentials)
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    logger.info("JiraConnector Example (Conceptual)")

    class DummySecretService(SecretService):
        def get_secret(self, secret_name: str) -> Optional[str]:
            if secret_name == "jira_api_token_test":
                return "<EMAIL>:xxxxxxxxxxxxxxxxx" # email:token format
            return None
    
    dummy_secrets = DummySecretService()

    jira_config = {
        "jira_url": "https://your-domain.atlassian.net",
        "jql_query": "project = 'MyProjectKey' AND status = 'Open'",
        "auth_method": "api_token",
        "auth_secret_name": "jira_api_token_test"
    }

    try:
        jira_connector = JiraConnector(
            connector_id="jira_test_001",
            config=jira_config,
            secret_service=dummy_secrets
        )
        logger.info(f"JiraConnector instantiated: {jira_connector.connector_id}")
        health = jira_connector.health_check()
        logger.info(f"Health check: {health}")

        if health["status"] == "HEALTHY":
            raw_data = jira_connector.fetch_data()
            if raw_data:
                preprocessed = jira_connector.preprocess(raw_data)
                if preprocessed:
                    chunks = jira_connector.chunk(preprocessed)
                    if chunks:
                        analyzed = jira_connector.analyze(chunks)
                        if analyzed:
                            storage_report = jira_connector.store(analyzed)
                            logger.info(f"Storage report: {storage_report}")
        
        logger.info("JiraConnector example run finished.")

    except Exception as e:
        logger.error(f"Error in JiraConnector example: {e}", exc_info=True)