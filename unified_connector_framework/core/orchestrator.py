"""
Framework Orchestrator for the Unified Connector Framework.

This module is responsible for managing and executing the lifecycle
of connector instances.
"""

import logging
from typing import Dict, Any, List, Optional
import uuid
from datetime import datetime, timezone
from .base_connector import BaseConnector
from ..services.chunking_service import ChunkingService
from ..services.analysis_service import AnalysisService
from ..services.storage_service import StorageService, BaseStorageAdapter
from ..services.status_persistence_service import StatusPersistenceService

# Configure basic logging for the orchestrator
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConnectorOrchestrator:
    """
    Orchestrates the execution of a connector's lifecycle stages.
    """

    def __init__(self, 
                 chunking_service: ChunkingService,
                 analysis_service: AnalysisService,
                 storage_service: StorageService,
                 status_persistence_service: Optional[StatusPersistenceService] = None):
        """
        Initializes the ConnectorOrchestrator.

        Args:
            chunking_service: An instance of the ChunkingService.
            analysis_service: An instance of the AnalysisService.
            storage_service: An instance of the StorageService.
            status_persistence_service: An optional instance of StatusPersistenceService for logging.
        """
        self.chunking_service = chunking_service
        self.analysis_service = analysis_service
        self.storage_service = storage_service
        self.status_service = status_persistence_service

    def run_connector(self, connector: BaseConnector) -> Dict[str, Any]:
        """
        Runs a given connector instance through its full lifecycle.
        Logs stage statuses to StatusPersistenceService if provided.

        Args:
            connector: An instance of a class derived from BaseConnector.

        Returns:
            A dictionary containing the results and status of the execution.
        """
        run_id = uuid.uuid4().hex
        connector_type = connector.__class__.__name__
        logger.info(f"Starting execution for connector: {connector.connector_id}, run_id: {run_id}, type: {connector_type}")
        
        run_summary = {
            "run_id": run_id,
            "connector_id": connector.connector_id,
            "connector_type": connector_type,
            "status": "STARTED",
            "stages": {}
        }
        
        current_stage_name = "UNKNOWN"
        stage_log_id: Optional[int] = None

        try:
            # --- 1. Fetch Data ---
            current_stage_name = "fetch_data"
            stage_start_time = datetime.now(timezone.utc)
            if self.status_service:
                stage_log_id = self.status_service.log_stage_start(run_id, connector.connector_id, connector_type, current_stage_name, stage_start_time)
            
            logger.info(f"[{connector.connector_id}:{run_id}] Stage 1: Fetching data...")
            raw_data_items = connector.fetch_data()
            items_fetched_count = len(raw_data_items)
            stage_details = {"items_fetched": items_fetched_count}
            logger.info(f"[{connector.connector_id}:{run_id}] Fetched {items_fetched_count} raw items.")
            run_summary["stages"][current_stage_name] = {"status": "COMPLETED", **stage_details}
            if self.status_service and stage_log_id is not None:
                self.status_service.log_stage_complete(stage_log_id, datetime.now(timezone.utc), stage_details)

            if not raw_data_items:
                logger.info(f"[{connector.connector_id}:{run_id}] No data fetched. Ending run.")
                run_summary["status"] = "COMPLETED_NO_DATA"
                return run_summary

            # --- 2. Preprocess Data ---
            current_stage_name = "preprocess"
            stage_start_time = datetime.now(timezone.utc)
            if self.status_service:
                stage_log_id = self.status_service.log_stage_start(run_id, connector.connector_id, connector_type, current_stage_name, stage_start_time)

            logger.info(f"[{connector.connector_id}:{run_id}] Stage 2: Preprocessing data...")
            preprocessed_data_items = connector.preprocess(raw_data_items)
            items_preprocessed_count = len(preprocessed_data_items)
            stage_details = {"items_preprocessed": items_preprocessed_count, "source_item_count": items_fetched_count}
            logger.info(f"[{connector.connector_id}:{run_id}] Preprocessed into {items_preprocessed_count} items.")
            run_summary["stages"][current_stage_name] = {"status": "COMPLETED", **stage_details}
            if self.status_service and stage_log_id is not None:
                self.status_service.log_stage_complete(stage_log_id, datetime.now(timezone.utc), stage_details)

            if not preprocessed_data_items:
                logger.info(f"[{connector.connector_id}:{run_id}] No data after preprocessing. Ending run.")
                run_summary["status"] = "COMPLETED_NO_DATA_AFTER_PREPROCESS"
                return run_summary

            # --- 3. Chunk Data ---
            current_stage_name = "chunk"
            stage_start_time = datetime.now(timezone.utc)
            if self.status_service:
                stage_log_id = self.status_service.log_stage_start(run_id, connector.connector_id, connector_type, current_stage_name, stage_start_time)
            
            logger.info(f"[{connector.connector_id}:{run_id}] Stage 3: Chunking data...")
            chunks = connector.chunk(preprocessed_data_items)
            chunks_created_count = len(chunks)
            stage_details = {"chunks_created": chunks_created_count, "source_item_count": items_preprocessed_count}
            logger.info(f"[{connector.connector_id}:{run_id}] Chunked into {chunks_created_count} chunks.")
            run_summary["stages"][current_stage_name] = {"status": "COMPLETED", **stage_details}
            if self.status_service and stage_log_id is not None:
                self.status_service.log_stage_complete(stage_log_id, datetime.now(timezone.utc), stage_details)

            if not chunks:
                logger.info(f"[{connector.connector_id}:{run_id}] No chunks created. Ending run.")
                run_summary["status"] = "COMPLETED_NO_CHUNKS"
                return run_summary

            # --- 4. Analyze Chunks ---
            current_stage_name = "analyze"
            stage_start_time = datetime.now(timezone.utc)
            if self.status_service:
                stage_log_id = self.status_service.log_stage_start(run_id, connector.connector_id, connector_type, current_stage_name, stage_start_time)

            logger.info(f"[{connector.connector_id}:{run_id}] Stage 4: Analyzing chunks...")
            analysis_outputs = connector.analyze(chunks)
            analysis_outputs_count = len(analysis_outputs)
            stage_details = {"analysis_outputs_count": analysis_outputs_count, "source_chunk_count": chunks_created_count}
            logger.info(f"[{connector.connector_id}:{run_id}] Analyzed {analysis_outputs_count} chunks.")
            run_summary["stages"][current_stage_name] = {"status": "COMPLETED", **stage_details}
            if self.status_service and stage_log_id is not None:
                self.status_service.log_stage_complete(stage_log_id, datetime.now(timezone.utc), stage_details)
            
            if not analysis_outputs:
                logger.info(f"[{connector.connector_id}:{run_id}] No analysis output. Ending run.")
                run_summary["status"] = "COMPLETED_NO_ANALYSIS"
                return run_summary

            # --- 5. Store Data ---
            current_stage_name = "store"
            stage_start_time = datetime.now(timezone.utc)
            if self.status_service:
                stage_log_id = self.status_service.log_stage_start(run_id, connector.connector_id, connector_type, current_stage_name, stage_start_time)

            logger.info(f"[{connector.connector_id}:{run_id}] Stage 5: Storing data...")
            storage_results = connector.store(analysis_outputs) # This should return a dict
            stage_details = {
                "items_processed_for_storage": analysis_outputs_count,
                "vector_db_stored_count": storage_results.get("vector_db_stored_count", 0), # Example keys
                "graph_db_nodes_created": storage_results.get("graph_db_nodes_created", 0), # Example keys
                "raw_storage_results": storage_results # Store the full result from connector.store
            }
            logger.info(f"[{connector.connector_id}:{run_id}] Storage results: {storage_results}")
            run_summary["stages"][current_stage_name] = {"status": "COMPLETED", **stage_details}
            if self.status_service and stage_log_id is not None:
                self.status_service.log_stage_complete(stage_log_id, datetime.now(timezone.utc), stage_details)

            run_summary["status"] = "COMPLETED_SUCCESS"
            logger.info(f"[{connector.connector_id}:{run_id}] Execution completed successfully.")

        except Exception as e:
            error_time = datetime.now(timezone.utc)
            error_message = f"Error during connector execution for {connector.connector_id} (run_id: {run_id}) at stage {current_stage_name}: {e}"
            logger.error(error_message, exc_info=True)
            
            run_summary["status"] = "FAILED"
            run_summary["error"] = str(e)
            run_summary["failed_stage"] = current_stage_name
            
            if current_stage_name not in run_summary["stages"]: # If error happened before stage summary was added
                 run_summary["stages"][current_stage_name] = {"status": "FAILED", "error": str(e)}
            else: # If stage summary was added but then an error occurred (less likely with current flow but good practice)
                 run_summary["stages"][current_stage_name]["status"] = "FAILED"
                 run_summary["stages"][current_stage_name]["error"] = str(e)

            if self.status_service and stage_log_id is not None: # stage_log_id would be from the failing stage
                failure_details = run_summary["stages"].get(current_stage_name, {})
                self.status_service.log_stage_failure(stage_log_id, error_time, str(e), failure_details)
            
            # Mark subsequent stages as SKIPPED in run_summary
            stages_order = ["fetch_data", "preprocess", "chunk", "analyze", "store"]
            failing_stage_index = stages_order.index(current_stage_name) if current_stage_name in stages_order else -1
            if failing_stage_index != -1:
                for i in range(failing_stage_index + 1, len(stages_order)):
                    skipped_stage_name = stages_order[i]
                    if skipped_stage_name not in run_summary["stages"]:
                         run_summary["stages"][skipped_stage_name] = {"status": "SKIPPED", "reason": f"Execution failed at prior stage: {current_stage_name}"}
                         # Optionally log SKIPPED to DB if needed, but plan focuses on active stages
        
        return run_summary

# Example Usage (Conceptual - requires concrete connector and service implementations)
if __name__ == "__main__":
    # This is a conceptual example.
    # You would need to:
    # 1. Create concrete implementations of BaseConnector, and the services.
    # 2. Initialize the services and orchestrator.
    # 3. Create an instance of your concrete connector.
    # 4. Run it.

    logger.info("Orchestrator example starting (conceptual)...")

    # --- Dummy Service Implementations (Conceptual) ---
    # In a real application, these would be fully implemented services.
    # These are simplified for demonstration within the orchestrator example.

    class DummyChunkingService(ChunkingService):
        """A basic, non-functional chunking service for example purposes."""
        def chunk_data(self, data: Dict[str, Any], **kwargs) -> List[Dict[str, Any]]:
            logger.info(f"(DummyChunkingService) Chunking data item: {data.get('document_id', 'Unknown ID')}")
            return [{"chunk_id": f"{data.get('document_id', 'doc')}_chunk1", "text": "Sample chunk 1", "parent_document_id": data.get('document_id')}]

        def chunk_multiple_data_items(self, data_items: List[Dict[str, Any]], **kwargs) -> List[Dict[str, Any]]:
            logger.info(f"(DummyChunkingService) Chunking {len(data_items)} data items...")
            all_chunks = []
            for i, item in enumerate(data_items):
                all_chunks.extend([
                    {"chunk_id": f"item{i}_chunk{j}", "text": f"Text from item {i}, chunk {j}", "parent_document_id": item.get("document_id", f"item{i}")}
                    for j in range(1, 3) # Create 2 chunks per item
                ])
            return all_chunks

    class DummyAnalysisService(AnalysisService):
        """A basic, non-functional analysis service for example purposes."""
        def analyze_chunk(self, chunk: Dict[str, Any], **kwargs) -> Dict[str, Any]:
            logger.info(f"(DummyAnalysisService) Analyzing chunk: {chunk.get('chunk_id', 'Unknown Chunk')}")
            return {"chunk_id": chunk.get('chunk_id'), "embedding": [0.1, 0.2], "entities": ["dummy_entity"], "relationships": []}

        def analyze_multiple_chunks(self, chunks: List[Dict[str, Any]], **kwargs) -> List[Dict[str, Any]]:
            logger.info(f"(DummyAnalysisService) Analyzing {len(chunks)} chunks...")
            return [
                {"chunk_id": c.get("chunk_id"), "embedding": [0.1]*5, "entities": [f"entity_for_{c.get('chunk_id')}"]}
                for c in chunks
            ]

    class DummyStorageService(StorageService):
        """A basic, non-functional storage service for example purposes."""
        def __init__(self, vector_adapter=None, graph_adapter=None): # Adapters are optional for dummy
            super().__init__(vector_adapter, graph_adapter)
            logger.info("(DummyStorageService) Initialized.")

        def store_data(self, analysis_outputs: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
            logger.info(f"(DummyStorageService) Storing {len(analysis_outputs)} analysis outputs...")
            # Simulate storing in vector and graph DBs
            vector_results = {"stored_count": len(analysis_outputs), "ids": [ao.get("chunk_id") for ao in analysis_outputs]}
            graph_results = {"nodes_created": len(analysis_outputs), "relationships_created": 0}
            return {"vector_db": vector_results, "graph_db": graph_results}

    # --- Connector Imports ---
    # These would typically be at the top of the file, but are here for example clarity.
    from ..connectors.gdrive_connector import GDriveConnector
    from ..connectors.jira_connector import JiraConnector
    # Note: Ensure these connectors have basic placeholder implementations for this example to run.

    # --- Orchestrator Setup ---
    logger.info("Setting up dummy services and orchestrator for example run...")
    
    # Instantiate dummy services
    # In a real app, these would be configured and potentially shared.
    dummy_chunking_service = DummyChunkingService()
    dummy_analysis_service = DummyAnalysisService()
    dummy_storage_service = DummyStorageService() # No adapters needed for this dummy version
    
    # Instantiate StatusPersistenceService
    # NOTE: For this service to actually connect and log to PostgreSQL,
    # the following environment variables must be set:
    # UCF_PG_HOST, UCF_PG_PORT, UCF_PG_USER, UCF_PG_PASSWORD, UCF_PG_DBNAME
    logger.info("Initializing StatusPersistenceService for the example...")
    logger.info("Ensure PostgreSQL environment variables (UCF_PG_*) are set for DB logging.")
    status_persistence_service = StatusPersistenceService()
    if not status_persistence_service.db_pool:
        logger.warning("StatusPersistenceService DB pool not initialized. DB logging will be skipped in this example run.")
        # Set to None so orchestrator knows it's not available, or handle as per orchestrator's None check
        status_persistence_service = None


    # Instantiate the orchestrator with the dummy services
    orchestrator = ConnectorOrchestrator(
        chunking_service=dummy_chunking_service,
        analysis_service=dummy_analysis_service,
        storage_service=dummy_storage_service,
        status_persistence_service=status_persistence_service
    )
    logger.info("Orchestrator and services (including StatusPersistenceService if DB is configured) initialized.")

    # --- GDrive Connector Example ---
    logger.info("\n--- Running GDrive Connector Example (Conceptual) ---")
    gdrive_config = {
        "credentials_json_path": "path/to/your/gdrive_service_account.json", # Placeholder
        "target_folder_id": "your_gdrive_folder_id" # Placeholder
    }
    # Note: GDriveConnector needs to be implemented to handle these configs,
    # and its methods (fetch, preprocess etc.) need to be defined.
    # For this example, we assume it's a basic class inheriting BaseConnector.
    try:
        gdrive_connector = GDriveConnector(
            connector_id="gdrive_example_001",
            config=gdrive_config,
            # Services can be passed if the connector expects them,
            # or it can use services provided by the orchestrator if designed that way.
            # For this example, let's assume the orchestrator passes them if needed,
            # or the connector uses the orchestrator's services.
            # The current BaseConnector design doesn't take services in __init__.
            # The orchestrator's run_connector method uses the services it holds.
        )
        logger.info(f"GDriveConnector instance created: {gdrive_connector.connector_id}")
        
        # Simulate running the GDrive connector
        # The actual GDriveConnector methods would perform real operations.
        # Here, they will likely call the dummy services via the orchestrator's flow.
        gdrive_results = orchestrator.run_connector(gdrive_connector)
        
        logger.info(f"\nGDrive Connector Run Summary for {gdrive_connector.connector_id}:")
        import json
        logger.info(json.dumps(gdrive_results, indent=2))

    except Exception as e:
        logger.error(f"Error during GDrive connector example: {e}", exc_info=True)


    # --- Jira Connector Example ---
    logger.info("\n--- Running Jira Connector Example (Conceptual) ---")
    jira_config = {
        "server_url": "https://your-jira-instance.atlassian.net", # Placeholder
        "username": "your_jira_username", # Placeholder
        "api_token_env_var": "JIRA_API_TOKEN" # Placeholder for env var name
    }
    # Note: JiraConnector needs to be implemented.
    try:
        jira_connector = JiraConnector(
            connector_id="jira_example_001",
            config=jira_config
        )
        logger.info(f"JiraConnector instance created: {jira_connector.connector_id}")

        jira_results = orchestrator.run_connector(jira_connector)
        
        logger.info(f"\nJira Connector Run Summary for {jira_connector.connector_id}:")
        # import json # Already imported
        logger.info(json.dumps(jira_results, indent=2))

    except Exception as e:
        logger.error(f"Error during Jira connector example: {e}", exc_info=True)

    logger.info("\nOrchestrator example run finished.")