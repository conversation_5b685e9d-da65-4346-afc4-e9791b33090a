"""
Defines the BaseConnector abstract class for the Unified Connector Framework.
"""

from abc import ABC, abstractmethod
from typing import Any, List, Dict, Optional

# Placeholder for a potential configuration object/dataclass
# from ..config.connector_config import ConnectorConfig 

# Placeholder for a potential context object passed to methods
# class ConnectorContext:
#     def __init__(self, logger, secrets_manager, etc.):
#         self.logger = logger
#         self.secrets_manager = secrets_manager
#         # ... other shared resources

class BaseConnector(ABC):
    """
    Abstract Base Class for all connectors.

    Each connector must implement the five lifecycle stages:
    fetch_data, preprocess, chunk, analyze, and store.
    """

    def __init__(self, connector_id: str, config: Dict[str, Any]): # Later: config: ConnectorConfig
        """
        Initializes the connector.

        Args:
            connector_id: A unique identifier for this connector instance.
            config: Configuration specific to this connector instance.
        """
        self.connector_id = connector_id
        self.config = config
        # self.logger = # Initialize a logger, perhaps passed via context or a global factory
        # self.context = # Initialize ConnectorContext if used

    @abstractmethod
    def fetch_data(self) -> List[Any]:
        """
        Pulls raw data from the source system (e.g., files, API responses, webhook payloads).

        Returns:
            A list of raw data items (e.g., file contents, API response objects).
            The exact type of items in the list can vary per connector.
        """
        pass

    @abstractmethod
    def preprocess(self, raw_data_items: List[Any]) -> List[Dict[str, Any]]:
        """
        Normalizes the structure of raw data items.
        Examples: Parse JSON, extract text from documents, deduplicate records.

        Args:
            raw_data_items: A list of raw data items from fetch_data.

        Returns:
            A list of preprocessed data items, typically dictionaries with a consistent structure.
            Each dictionary should represent a single document or data unit.
            It's recommended to include metadata like source_id, content_type, etc.
        """
        pass

    @abstractmethod
    def chunk(self, preprocessed_data_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Divides preprocessed data into semantically meaningful units (chunks).
        Examples: Split documents into paragraphs, use individual tickets/comments as chunks.

        Args:
            preprocessed_data_items: A list of preprocessed data items.

        Returns:
            A list of chunks. Each chunk should be a dictionary containing the chunk text
            and any relevant metadata (e.g., original document ID, chunk sequence).
            Example chunk: {"chunk_id": "...", "document_id": "...", "text": "...", "metadata": {...}}
        """
        pass

    @abstractmethod
    def analyze(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extracts entities, relations, context, and generates embeddings for chunks.
        This stage typically involves interaction with LLMs and embedding models.

        Args:
            chunks: A list of chunks from the chunk stage.

        Returns:
            A list of analysis outputs. Each item corresponds to a chunk and contains
            extracted entities, relationships, embeddings, and other analytical data.
            Example: {"chunk_id": "...", "text": "...", "embedding": [...], 
                      "entities": [{"name": "...", "type": "..."}], 
                      "relationships": [{"source": "...", "target": "...", "type": "..."}]}
        """
        pass

    @abstractmethod
    def store(self, analysis_outputs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Stores the analyzed data (chunks, embeddings, entities, relations) into
        the configured Vector DB and Knowledge Graph DB.

        Args:
            analysis_outputs: A list of analysis outputs from the analyze stage.

        Returns:
            A summary of the storage operation, e.g.,
            {"vector_db_stored_count": N, "graph_db_nodes_created": M, "graph_db_rels_created": P}.
        """
        pass

    def get_status(self) -> Dict[str, Any]:
        """
        Optional: Returns the current status of the connector.
        This could include last run time, number of items processed, errors, etc.
        """
        return {"connector_id": self.connector_id, "status": "Not implemented"}

    def health_check(self) -> Dict[str, Any]:
        """
        Optional: Performs a health check of the connector, e.g., connectivity to the source system.
        """
        return {"connector_id": self.connector_id, "health": "Not implemented"}

# Example of how a specific connector might inherit:
# class MySpecificConnector(BaseConnector):
#     def __init__(self, connector_id: str, config: Dict[str, Any]):
#         super().__init__(connector_id, config)
#         # Connector-specific initialization
#
#     def fetch_data(self) -> List[Any]:
#         # Implementation for fetching data from MySource
#         return []
#
#     def preprocess(self, raw_data_items: List[Any]) -> List[Dict[str, Any]]:
#         # Implementation for preprocessing MySource data
#         return []
#
#     def chunk(self, preprocessed_data_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
#         # Implementation for chunking MySource data
#         return []
#
#     def analyze(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
#         # Implementation for analyzing MySource data
#         return []
#
#     def store(self, analysis_outputs: List[Dict[str, Any]]) -> Dict[str, Any]:
#         # Implementation for storing MySource data
#         return {}