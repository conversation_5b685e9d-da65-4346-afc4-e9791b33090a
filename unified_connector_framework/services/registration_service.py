"""
Connector Registration Service for the Unified Connector Framework.

This service manages the registration, configuration, and lifecycle
of connector instances. It will interact with a persistent store
for connector metadata and configurations.
"""

import logging
from typing import Dict, Any, Optional, List, Literal
from pydantic import BaseModel, Field, HttpUrl # Using Pydantic for data validation
import uuid # For generating unique IDs

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# --- Data Models for Connector Configuration and Metadata ---

class AuthConfig(BaseModel):
    """Authentication configuration for a connector."""
    method: Literal["oauth2_service_account", "api_key", "username_password", "token", "none"] = "none"
    credentials_secret_name: Optional[str] = None # Name of the secret in SecretService
    # Additional fields for specific auth methods can be added, e.g., token_url for OAuth

class PollingConfig(BaseModel):
    """Configuration for connectors that poll for data."""
    interval_seconds: int = Field(default=3600, gt=0) # e.g., every hour
    # last_polled_at: Optional[datetime] = None # To be managed by the service

class WebhookConfig(BaseModel):
    """Configuration for connectors that receive data via webhooks."""
    endpoint_url_suffix: str # e.g., "/webhooks/myconnector"
    # verification_token_secret_name: Optional[str] = None

class ConnectorSchedule(BaseModel):
    """Defines how a connector is triggered."""
    trigger_type: Literal["manual", "polling", "webhook", "event_driven"] = "manual"
    polling_config: Optional[PollingConfig] = None
    webhook_config: Optional[WebhookConfig] = None
    # event_config: Optional[Dict[str, Any]] = None # For event-driven connectors

class ConnectorMetadata(BaseModel):
    """
    Metadata defining a connector type (the template for a connector).
    This would be registered once per connector *type*.
    """
    connector_type_id: str = Field(default_factory=lambda: f"type_{uuid.uuid4().hex[:8]}")
    name: str # e.g., "GDrive Connector", "Jira Cloud Connector"
    description: Optional[str] = None
    version: str = "1.0.0"
    developer: Optional[str] = None
    # Python module and class to instantiate this connector type
    # e.g., "unified_connector_framework.connectors.gdrive_connector.GDriveConnector"
    module_path: str 
    class_name: str
    # JSON schema for instance-specific configuration fields
    # This helps validate 'config_values' in ConnectorInstance
    config_schema: Dict[str, Any] = Field(default_factory=dict) 
    supported_auth_methods: List[Literal["oauth2_service_account", "api_key", "username_password", "token", "none"]] = ["none"]
    default_schedule: ConnectorSchedule = Field(default_factory=ConnectorSchedule)


class ConnectorInstance(BaseModel):
    """
    Represents a configured and active instance of a specific connector type.
    """
    instance_id: str = Field(default_factory=lambda: f"instance_{uuid.uuid4().hex}")
    connector_type_id: str # Links to ConnectorMetadata.connector_type_id
    friendly_name: str # User-defined name for this instance, e.g., "My Team's GDrive"
    is_enabled: bool = True
    auth_config: AuthConfig
    # Instance-specific configuration values, validated against ConnectorMetadata.config_schema
    config_values: Dict[str, Any] = Field(default_factory=dict) 
    schedule: ConnectorSchedule
    # last_run_status: Optional[Literal["success", "failed", "running", "pending"]] = None
    # last_run_at: Optional[datetime] = None
    # created_at: datetime = Field(default_factory=datetime.utcnow)
    # updated_at: datetime = Field(default_factory=datetime.utcnow)


# --- Storage Interface (Abstract) ---

class BaseRegistrationStore(ABC):
    """Abstract base class for storing and retrieving connector metadata and instances."""
    @abstractmethod
    def add_connector_type(self, metadata: ConnectorMetadata) -> ConnectorMetadata:
        pass

    @abstractmethod
    def get_connector_type(self, connector_type_id: str) -> Optional[ConnectorMetadata]:
        pass

    @abstractmethod
    def list_connector_types(self) -> List[ConnectorMetadata]:
        pass
    
    @abstractmethod
    def update_connector_type(self, metadata: ConnectorMetadata) -> Optional[ConnectorMetadata]:
        pass

    @abstractmethod
    def delete_connector_type(self, connector_type_id: str) -> bool:
        pass

    @abstractmethod
    def add_connector_instance(self, instance: ConnectorInstance) -> ConnectorInstance:
        pass

    @abstractmethod
    def get_connector_instance(self, instance_id: str) -> Optional[ConnectorInstance]:
        pass

    @abstractmethod
    def list_connector_instances(self, connector_type_id: Optional[str] = None) -> List[ConnectorInstance]:
        pass

    @abstractmethod
    def update_connector_instance(self, instance: ConnectorInstance) -> Optional[ConnectorInstance]:
        pass

    @abstractmethod
    def delete_connector_instance(self, instance_id: str) -> bool:
        pass

# --- In-Memory Store (for PoC/Testing) ---

class InMemoryRegistrationStore(BaseRegistrationStore):
    """In-memory implementation of the registration store for PoC and testing."""
    def __init__(self):
        self._connector_types: Dict[str, ConnectorMetadata] = {}
        self._connector_instances: Dict[str, ConnectorInstance] = {}

    def add_connector_type(self, metadata: ConnectorMetadata) -> ConnectorMetadata:
        if metadata.connector_type_id in self._connector_types:
            raise ValueError(f"Connector type ID {metadata.connector_type_id} already exists.")
        self._connector_types[metadata.connector_type_id] = metadata
        return metadata

    def get_connector_type(self, connector_type_id: str) -> Optional[ConnectorMetadata]:
        return self._connector_types.get(connector_type_id)

    def list_connector_types(self) -> List[ConnectorMetadata]:
        return list(self._connector_types.values())
    
    def update_connector_type(self, metadata: ConnectorMetadata) -> Optional[ConnectorMetadata]:
        if metadata.connector_type_id not in self._connector_types:
            return None
        self._connector_types[metadata.connector_type_id] = metadata
        return metadata

    def delete_connector_type(self, connector_type_id: str) -> bool:
        if connector_type_id in self._connector_types:
            del self._connector_types[connector_type_id]
            # Also delete instances of this type? Or prevent deletion if instances exist?
            # For now, simple deletion.
            self._connector_instances = {
                k: v for k, v in self._connector_instances.items() if v.connector_type_id != connector_type_id
            }
            return True
        return False

    def add_connector_instance(self, instance: ConnectorInstance) -> ConnectorInstance:
        if instance.instance_id in self._connector_instances:
            raise ValueError(f"Connector instance ID {instance.instance_id} already exists.")
        if not self.get_connector_type(instance.connector_type_id):
            raise ValueError(f"Connector type ID {instance.connector_type_id} not found.")
        # TODO: Validate instance.config_values against schema from ConnectorMetadata
        self._connector_instances[instance.instance_id] = instance
        return instance

    def get_connector_instance(self, instance_id: str) -> Optional[ConnectorInstance]:
        return self._connector_instances.get(instance_id)

    def list_connector_instances(self, connector_type_id: Optional[str] = None) -> List[ConnectorInstance]:
        if connector_type_id:
            return [inst for inst in self._connector_instances.values() if inst.connector_type_id == connector_type_id]
        return list(self._connector_instances.values())

    def update_connector_instance(self, instance: ConnectorInstance) -> Optional[ConnectorInstance]:
        if instance.instance_id not in self._connector_instances:
            return None
        # TODO: Validate instance.config_values against schema
        self._connector_instances[instance.instance_id] = instance
        return instance

    def delete_connector_instance(self, instance_id: str) -> bool:
        if instance_id in self._connector_instances:
            del self._connector_instances[instance_id]
            return True
        return False

# --- Registration Service ---

class ConnectorRegistrationService:
    """
    Service layer for managing connector types and instances.
    """
    def __init__(self, store: BaseRegistrationStore):
        self.store = store
        # self.orchestrator = orchestrator # To trigger runs, etc.
        # self.secret_service = secret_service # To resolve credential secret names

    # --- Connector Type Management ---
    def register_connector_type(self, metadata_dict: Dict[str, Any]) -> ConnectorMetadata:
        metadata = ConnectorMetadata(**metadata_dict)
        logger.info(f"Registering new connector type: {metadata.name} (ID: {metadata.connector_type_id})")
        return self.store.add_connector_type(metadata)

    def get_connector_type_details(self, connector_type_id: str) -> Optional[ConnectorMetadata]:
        return self.store.get_connector_type(connector_type_id)

    def list_available_connector_types(self) -> List[ConnectorMetadata]:
        return self.store.list_connector_types()

    # --- Connector Instance Management ---
    def create_connector_instance(self, instance_data: Dict[str, Any]) -> ConnectorInstance:
        # instance_data should contain connector_type_id, friendly_name, auth_config, config_values, schedule
        instance = ConnectorInstance(**instance_data)
        logger.info(f"Creating new connector instance: {instance.friendly_name} (ID: {instance.instance_id}) of type {instance.connector_type_id}")
        # Here, you might resolve auth_config.credentials_secret_name using SecretService
        # and validate config_values against the schema in ConnectorMetadata
        return self.store.add_connector_instance(instance)

    def get_instance_details(self, instance_id: str) -> Optional[ConnectorInstance]:
        return self.store.get_connector_instance(instance_id)

    def list_all_instances(self, connector_type_id: Optional[str] = None) -> List[ConnectorInstance]:
        return self.store.list_connector_instances(connector_type_id)
    
    def update_instance_config(self, instance_id: str, updates: Dict[str, Any]) -> Optional[ConnectorInstance]:
        instance = self.store.get_connector_instance(instance_id)
        if not instance:
            return None
        
        updated_data = instance.model_dump()
        
        # Smart updating for nested models
        if 'auth_config' in updates:
            auth_data = updated_data.get('auth_config', {})
            auth_data.update(updates.pop('auth_config'))
            updated_data['auth_config'] = AuthConfig(**auth_data).model_dump()

        if 'schedule' in updates:
            schedule_data = updated_data.get('schedule', {})
            schedule_data.update(updates.pop('schedule'))
            updated_data['schedule'] = ConnectorSchedule(**schedule_data).model_dump()
            
        if 'config_values' in updates:
            config_vals_data = updated_data.get('config_values', {})
            config_vals_data.update(updates.pop('config_values'))
            updated_data['config_values'] = config_vals_data # Assuming simple dict update

        updated_data.update(updates) # Apply remaining top-level updates
        
        updated_instance = ConnectorInstance(**updated_data)
        return self.store.update_connector_instance(updated_instance)

    def enable_instance(self, instance_id: str) -> Optional[ConnectorInstance]:
        return self.update_instance_config(instance_id, {"is_enabled": True})

    def disable_instance(self, instance_id: str) -> Optional[ConnectorInstance]:
        return self.update_instance_config(instance_id, {"is_enabled": False})

    def delete_connector_instance_by_id(self, instance_id: str) -> bool:
        logger.info(f"Deleting connector instance: {instance_id}")
        return self.store.delete_connector_instance(instance_id)

    # TODO: Methods to trigger connector runs (manual, scheduled) via an orchestrator
    # def trigger_connector_run(self, instance_id: str) -> Dict[str, Any]:
    #     instance = self.get_instance_details(instance_id)
    #     if not instance or not instance.is_enabled:
    #         return {"status": "error", "message": "Instance not found or not enabled."}
    #     # Load connector class, instantiate, pass to orchestrator
    #     # connector_module = importlib.import_module(connector_type.module_path)
    #     # connector_class = getattr(connector_module, connector_type.class_name)
    #     # connector = connector_class(instance.instance_id, instance.config_values) # Needs resolved secrets
    #     # return self.orchestrator.run_connector(connector)
    #     return {"status": "pending_orchestrator_integration"}


# Example Usage (for testing purposes):
if __name__ == "__main__":
    print("Testing ConnectorRegistrationService with InMemoryStore...")
    
    store = InMemoryRegistrationStore()
    reg_service = ConnectorRegistrationService(store)

    # 1. Register a connector type (e.g., GDrive)
    gdrive_type_data = {
        "name": "Google Drive Connector",
        "description": "Connects to Google Drive to fetch files.",
        "module_path": "unified_connector_framework.connectors.gdrive.GDriveConnector", # Hypothetical
        "class_name": "GDriveConnector",
        "config_schema": {
            "type": "object",
            "properties": {
                "folder_id": {"type": "string", "description": "ID of the GDrive folder to scan"},
                "file_types": {"type": "array", "items": {"type": "string"}, "default": [".pdf", ".docx"]}
            },
            "required": ["folder_id"]
        },
        "supported_auth_methods": ["oauth2_service_account"],
        "default_schedule": {"trigger_type": "polling", "polling_config": {"interval_seconds": 7200}}
    }
    gdrive_type = reg_service.register_connector_type(gdrive_type_data)
    print(f"\nRegistered GDrive Connector Type: ID = {gdrive_type.connector_type_id}")

    # 2. Create an instance of the GDrive connector
    gdrive_instance_data = {
        "connector_type_id": gdrive_type.connector_type_id,
        "friendly_name": "My Marketing Team's GDrive",
        "auth_config": {
            "method": "oauth2_service_account",
            "credentials_secret_name": "gdrive_marketing_sa_key"
        },
        "config_values": {
            "folder_id": "abcdef12345folderid",
            "file_types": [".pdf", ".pptx", ".txt"]
        },
        "schedule": {"trigger_type": "manual"} # Override default schedule
    }
    gdrive_instance = reg_service.create_connector_instance(gdrive_instance_data)
    print(f"Created GDrive Instance: ID = {gdrive_instance.instance_id}, Name = {gdrive_instance.friendly_name}")

    # 3. List instances
    all_gdrive_instances = reg_service.list_all_instances(connector_type_id=gdrive_type.connector_type_id)
    print(f"\nFound {len(all_gdrive_instances)} GDrive instances:")
    for inst in all_gdrive_instances:
        print(f"  - {inst.friendly_name} (Enabled: {inst.is_enabled}, Auth: {inst.auth_config.method})")

    # 4. Update an instance
    updated_instance = reg_service.update_instance_config(
        gdrive_instance.instance_id, 
        {"is_enabled": False, "config_values": {"folder_id": "new_folder_xyz"}}
    )
    if updated_instance:
        print(f"\nUpdated instance '{updated_instance.friendly_name}': Enabled={updated_instance.is_enabled}, FolderID={updated_instance.config_values.get('folder_id')}")

    retrieved_instance = reg_service.get_instance_details(gdrive_instance.instance_id)
    if retrieved_instance:
         print(f"Retrieved after update: Enabled={retrieved_instance.is_enabled}, FolderID={retrieved_instance.config_values.get('folder_id')}")


    # Test listing all connector types
    types = reg_service.list_available_connector_types()
    print(f"\nAvailable connector types ({len(types)}):")
    for t in types:
        print(f"  - {t.name} (ID: {t.connector_type_id}, Module: {t.module_path}.{t.class_name})")