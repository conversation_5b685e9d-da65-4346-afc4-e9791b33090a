"""
Asynchronous Task Service for the Unified Connector Framework (Proof of Concept).

This service demonstrates how lifecycle stages could be run asynchronously
with retry logic. For a production system, a more robust task queue like
Celery with RabbitMQ/Redis would be recommended.
"""

import asyncio
import logging
from typing import Callable, Any, Dict, Coroutine, Optional
import time

# Example: aiohttp for async HTTP calls if a stage involves API calls
# import aiohttp 

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class AsyncTaskService:
    """
    Manages execution of tasks asynchronously with retry capabilities.
    This is a simplified PoC using asyncio.
    """

    def __init__(self, default_max_retries: int = 3, default_retry_delay_seconds: float = 5.0):
        """
        Initializes the AsyncTaskService.

        Args:
            default_max_retries: Default maximum number of retries for a task.
            default_retry_delay_seconds: Default delay in seconds between retries.
        """
        self.default_max_retries = default_max_retries
        self.default_retry_delay_seconds = default_retry_delay_seconds

    async def execute_stage_async(
        self,
        stage_func: Callable[..., Coroutine[Any, Any, Any]], # An async function
        stage_name: str,
        connector_id: str,
        *args: Any,
        max_retries: Optional[int] = None,
        retry_delay_seconds: Optional[float] = None,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """
        Executes a given stage function asynchronously with retry logic.

        Args:
            stage_func: The asynchronous function representing the lifecycle stage to execute.
            stage_name: Name of the stage (e.g., "fetch_data", "preprocess").
            connector_id: Identifier of the connector this stage belongs to.
            *args: Positional arguments to pass to the stage_func.
            max_retries: Maximum number of retries for this specific task.
            retry_delay_seconds: Delay in seconds between retries for this task.
            **kwargs: Keyword arguments to pass to the stage_func.

        Returns:
            A dictionary containing the result of the stage or an error.
        """
        retries = max_retries if max_retries is not None else self.default_max_retries
        delay = retry_delay_seconds if retry_delay_seconds is not None else self.default_retry_delay_seconds
        
        for attempt in range(retries + 1):
            try:
                logger.info(f"[{connector_id}] Attempt {attempt + 1}/{retries + 1} for stage '{stage_name}'...")
                result = await stage_func(*args, **kwargs)
                logger.info(f"[{connector_id}] Stage '{stage_name}' completed successfully on attempt {attempt + 1}.")
                return {"status": "SUCCESS", "stage": stage_name, "result": result, "attempts": attempt + 1}
            except Exception as e:
                logger.error(f"[{connector_id}] Stage '{stage_name}' failed on attempt {attempt + 1}: {e}")
                if attempt < retries:
                    logger.info(f"[{connector_id}] Retrying stage '{stage_name}' in {delay} seconds...")
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"[{connector_id}] Stage '{stage_name}' failed after {retries + 1} attempts.")
                    return {"status": "FAILED", "stage": stage_name, "error": str(e), "attempts": attempt + 1}
        return {"status": "FAILED", "stage": stage_name, "error": "Max retries reached, unknown state.", "attempts": retries + 1} # Should not be reached

# --- Example Usage (Proof of Concept) ---

# Dummy async stage functions
async def dummy_fetch_data_stage(config: Dict[str, Any]) -> Dict[str, Any]:
    api_url = config.get("api_url", "http://example.com/api/data")
    logger.info(f"Fetching data from {api_url} with params {config.get('params')}...")
    await asyncio.sleep(1) # Simulate network latency
    
    # Simulate occasional failure
    if time.time() % 10 < 3: # Fails ~30% of the time
        raise ConnectionError(f"Simulated network error connecting to {api_url}")
    
    return {"data_items": [{"id": 1, "content": "item1"}, {"id": 2, "content": "item2"}], "source": api_url}

async def dummy_preprocess_stage(raw_items: Dict[str, Any]) -> Dict[str, Any]:
    logger.info(f"Preprocessing {len(raw_items.get('data_items', []))} items...")
    await asyncio.sleep(0.5)
    processed = [f"processed_{item['content']}" for item in raw_items.get("data_items", [])]
    return {"processed_count": len(processed), "sample": processed[:1]}

async def run_connector_poc(task_service: AsyncTaskService, connector_id: str):
    logger.info(f"\n--- Running PoC for Connector: {connector_id} ---")
    
    # Stage 1: Fetch Data
    fetch_config = {"api_url": "http://mydataapi.com/data", "params": {"type": "urgent"}}
    fetch_result_info = await task_service.execute_stage_async(
        dummy_fetch_data_stage, "fetch_data", connector_id, fetch_config
    )
    logger.info(f"[{connector_id}] Fetch Result: {fetch_result_info}")

    if fetch_result_info["status"] == "SUCCESS":
        raw_data = fetch_result_info["result"]
        
        # Stage 2: Preprocess Data
        preprocess_result_info = await task_service.execute_stage_async(
            dummy_preprocess_stage, "preprocess", connector_id, raw_data, max_retries=1 # Example: override retries
        )
        logger.info(f"[{connector_id}] Preprocess Result: {preprocess_result_info}")
    else:
        logger.error(f"[{connector_id}] Skipping preprocess due to fetch_data failure.")

async def main_poc():
    task_service = AsyncTaskService(default_retry_delay_seconds=2) # Quick delay for PoC
    
    # Simulate running two connectors
    await asyncio.gather(
        run_connector_poc(task_service, "connector_A"),
        run_connector_poc(task_service, "connector_B")
    )

if __name__ == "__main__":
    print("Running Asynchronous Task Service PoC...")
    print("This demonstrates async execution of stages with retries.")
    print("A production system would use Celery/RabbitMQ or similar.")
    
    asyncio.run(main_poc())

    print("\nPoC Finished. Check logs for execution details.")