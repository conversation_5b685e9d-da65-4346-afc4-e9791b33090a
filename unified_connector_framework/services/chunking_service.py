"""
Chunking Service for the Unified Connector Framework.

This service will encapsulate various chunking strategies and provide
a consistent interface for connectors to split preprocessed data.
"""

from typing import List, Dict, Any, Literal

# Placeholder for chunking strategies from enterprise_kg_minimal or new ones
ChunkingStrategy = Literal["fixed_size", "sentence_based", "paragraph_based", "semantic_based", "hybrid"]

class ChunkingService:
    """
    Provides methods to chunk preprocessed data items.
    """

    def __init__(self, default_strategy: ChunkingStrategy = "hybrid", 
                 default_chunk_size: int = 1000, 
                 default_chunk_overlap: int = 200):
        """
        Initializes the ChunkingService.

        Args:
            default_strategy: The default chunking strategy to use.
            default_chunk_size: The default target size for chunks.
            default_chunk_overlap: The default overlap between chunks.
        """
        self.default_strategy = default_strategy
        self.default_chunk_size = default_chunk_size
        self.default_chunk_overlap = default_chunk_overlap
        # In a real implementation, we might load models or specific libraries here
        # based on the strategies supported.

    def chunk_data_item(self, 
                        data_item: Dict[str, Any], 
                        strategy: Optional[ChunkingStrategy] = None,
                        chunk_size: Optional[int] = None,
                        chunk_overlap: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Chunks a single preprocessed data item.

        Args:
            data_item: A dictionary representing a preprocessed data item.
                       Expected to have a 'text_content' field and 'metadata' field.
            strategy: The chunking strategy to use for this item.
            chunk_size: The target size for chunks for this item.
            chunk_overlap: The overlap between chunks for this item.

        Returns:
            A list of chunk dictionaries. Each chunk should include:
            - chunk_id: A unique identifier for the chunk.
            - document_id: Identifier of the original document/data item.
            - text: The text content of the chunk.
            - sequence_number: The order of the chunk within the document.
            - metadata: Any other relevant metadata.
        """
        current_strategy = strategy or self.default_strategy
        current_chunk_size = chunk_size or self.default_chunk_size
        current_chunk_overlap = chunk_overlap or self.default_chunk_overlap

        # TODO: Implement actual chunking logic based on 'current_strategy'.
        # This will involve adapting logic from enterprise_kg_minimal.core.chunking_engine.py
        # or other chunking libraries (e.g., Langchain text splitters).

        text_content = data_item.get("text_content", "")
        document_id = data_item.get("metadata", {}).get("document_id", "unknown_doc")
        
        # Placeholder logic:
        if not text_content:
            return []

        # Simple fixed-size chunking placeholder
        chunks = []
        start = 0
        chunk_idx = 0
        while start < len(text_content):
            end = start + current_chunk_size
            chunk_text = text_content[start:end]
            chunks.append({
                "chunk_id": f"{document_id}_chunk_{chunk_idx}",
                "document_id": document_id,
                "text": chunk_text,
                "sequence_number": chunk_idx,
                "metadata": {
                    "strategy_used": current_strategy,
                    "original_document_metadata": data_item.get("metadata", {})
                }
            })
            start += current_chunk_size - current_chunk_overlap # Apply overlap
            if start >= len(text_content) and end < len(text_content): # Ensure last part is captured
                 start = end - current_chunk_overlap
            chunk_idx +=1
            if end >= len(text_content): # break after capturing the last part
                break


        return chunks

    def chunk_multiple_data_items(self, 
                                  data_items: List[Dict[str, Any]],
                                  strategy: Optional[ChunkingStrategy] = None,
                                  chunk_size: Optional[int] = None,
                                  chunk_overlap: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Chunks multiple preprocessed data items.

        Args:
            data_items: A list of preprocessed data items.
            strategy: The chunking strategy to apply to all items.
            chunk_size: The target chunk size for all items.
            chunk_overlap: The chunk overlap for all items.

        Returns:
            A flat list of all chunks from all data items.
        """
        all_chunks = []
        for item in data_items:
            all_chunks.extend(self.chunk_data_item(item, strategy, chunk_size, chunk_overlap))
        return all_chunks

# Example Usage (for testing purposes):
if __name__ == "__main__":
    service = ChunkingService()
    sample_item = {
        "text_content": "This is a sample document that is long enough to be split into several chunks. " * 5,
        "metadata": {"document_id": "doc123", "source": "test_source"}
    }
    
    chunks = service.chunk_data_item(sample_item, chunk_size=50, chunk_overlap=10)
    print(f"Generated {len(chunks)} chunks:")
    for i, chunk in enumerate(chunks):
        print(f"  Chunk {i+1}: ID='{chunk['chunk_id']}', Text='{chunk['text'][:30]}...'")

    sample_items = [sample_item, {"text_content": "Another short document.", "metadata": {"document_id": "doc456"}}]
    all_doc_chunks = service.chunk_multiple_data_items(sample_items, chunk_size=60, chunk_overlap=5)
    print(f"\nGenerated {len(all_doc_chunks)} chunks from multiple items.")