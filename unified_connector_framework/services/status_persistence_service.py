"""
Service for persisting connector run stage statuses to a PostgreSQL database.
"""
import logging
import os
import psycopg2
from psycopg2 import pool
from psycopg2 import extras # For RealDictCursor
from datetime import datetime, timezone
from typing import Dict, Optional, Any

logger = logging.getLogger(__name__)

class StatusPersistenceService:
    """
    Handles the connection and operations for logging connector stage statuses
    to a PostgreSQL database.
    """
    def __init__(self):
        """
        Initializes the service and sets up the database connection pool.
        Connection parameters are read from environment variables:
        - UCF_PG_HOST
        - UCF_PG_PORT (defaults to 5432)
        - UCF_PG_USER
        - UCF_PG_PASSWORD
        - UCF_PG_DBNAME
        - UCF_PG_MIN_CONN (defaults to 1)
        - UCF_PG_MAX_CONN (defaults to 5)
        """
        self.db_pool = None
        try:
            min_conn = int(os.environ.get("UCF_PG_MIN_CONN", 1))
            max_conn = int(os.environ.get("UCF_PG_MAX_CONN", 5))

            self.db_pool = psycopg2.pool.SimpleConnectionPool(
                min_conn,
                max_conn,
                host=os.environ.get("UCF_PG_HOST"),
                port=os.environ.get("UCF_PG_PORT", "5432"),
                user=os.environ.get("UCF_PG_USER"),
                password=os.environ.get("UCF_PG_PASSWORD"),
                dbname=os.environ.get("UCF_PG_DBNAME")
            )
            logger.info("StatusPersistenceService: Database connection pool created successfully.")
            # Test connection
            conn = self._get_connection()
            if conn:
                logger.info("StatusPersistenceService: Successfully connected to PostgreSQL.")
                self._release_connection(conn, success=True)
            else:
                logger.error("StatusPersistenceService: Failed to get initial test connection from pool.")
                # self.db_pool will remain None or the pool object if partially successful
                # Further operations will fail if pool is not usable.
                # Consider raising an error here if initial connection is critical.
                # For now, allowing it to proceed and fail on actual use.

        except (psycopg2.Error, KeyError, ValueError) as e:
            logger.error(f"StatusPersistenceService: Error initializing database connection pool: {e}", exc_info=True)
            # Ensure db_pool is None if initialization fails critically
            self.db_pool = None
            # Depending on application's startup strategy, this might be a critical error.
            # For now, logging the error. Subsequent calls will handle db_pool being None.

    def _get_connection(self):
        """Gets a connection from the pool."""
        if not self.db_pool:
            logger.error("StatusPersistenceService: Connection pool is not available.")
            return None
        try:
            return self.db_pool.getconn()
        except psycopg2.Error as e:
            logger.error(f"StatusPersistenceService: Error getting connection from pool: {e}", exc_info=True)
            return None

    def _release_connection(self, conn, success: bool = True):
        """Releases a connection back to the pool, committing or rolling back."""
        if not self.db_pool or not conn:
            return
        try:
            if success:
                conn.commit()
            else:
                conn.rollback()
        except psycopg2.Error as e:
            logger.error(f"StatusPersistenceService: Error during commit/rollback: {e}", exc_info=True)
            # If commit/rollback fails, still try to putconn
        finally:
            try:
                self.db_pool.putconn(conn)
            except psycopg2.Error as e:
                logger.error(f"StatusPersistenceService: Error releasing connection to pool: {e}", exc_info=True)

    def log_stage_start(self, run_id: str, connector_id: str, connector_type: str, 
                        stage_name: str, start_time: datetime) -> Optional[int]:
        """
        Logs the start of a connector stage.
        Returns the log_id of the newly created record, or None if failed.
        """
        if not self.db_pool:
            logger.error("StatusPersistenceService: Cannot log stage start, pool not available.")
            return None

        sql = """
            INSERT INTO connector_run_stage_logs 
            (run_id, connector_id, connector_type, stage_name, status, start_time)
            VALUES (%s, %s, %s, %s, %s, %s)
            RETURNING log_id;
        """
        conn = None
        log_id = None
        try:
            conn = self._get_connection()
            if not conn: return None

            with conn.cursor() as cur:
                cur.execute(sql, (run_id, connector_id, connector_type, stage_name, 
                                   'STARTED', start_time.astimezone(timezone.utc)))
                log_id_tuple = cur.fetchone()
                if log_id_tuple:
                    log_id = log_id_tuple[0]
            self._release_connection(conn, success=True)
            logger.debug(f"Logged stage start: run_id={run_id}, stage={stage_name}, log_id={log_id}")
            return log_id
        except psycopg2.Error as e:
            logger.error(f"StatusPersistenceService: DB error logging stage start for run_id {run_id}, stage {stage_name}: {e}", exc_info=True)
            if conn: self._release_connection(conn, success=False)
            return None
        except Exception as e: # Catch any other unexpected errors
            logger.error(f"StatusPersistenceService: Unexpected error logging stage start for run_id {run_id}, stage {stage_name}: {e}", exc_info=True)
            if conn: self._release_connection(conn, success=False)
            return None


    def log_stage_complete(self, log_id: int, end_time: datetime, details: Optional[Dict] = None):
        """
        Updates a stage log to 'COMPLETED'.
        """
        if not self.db_pool:
            logger.error(f"StatusPersistenceService: Cannot log stage complete for log_id {log_id}, pool not available.")
            return

        sql = """
            UPDATE connector_run_stage_logs
            SET status = %s, end_time = %s, duration_ms = EXTRACT(EPOCH FROM (%s - start_time)) * 1000, details = %s
            WHERE log_id = %s;
        """
        conn = None
        try:
            conn = self._get_connection()
            if not conn: return

            details_json = psycopg2.extras.Json(details) if details else None
            with conn.cursor() as cur:
                cur.execute(sql, ('COMPLETED', end_time.astimezone(timezone.utc), 
                                   end_time.astimezone(timezone.utc), details_json, log_id))
            self._release_connection(conn, success=True)
            logger.debug(f"Logged stage complete: log_id={log_id}")
        except psycopg2.Error as e:
            logger.error(f"StatusPersistenceService: DB error logging stage complete for log_id {log_id}: {e}", exc_info=True)
            if conn: self._release_connection(conn, success=False)
        except Exception as e:
            logger.error(f"StatusPersistenceService: Unexpected error logging stage complete for log_id {log_id}: {e}", exc_info=True)
            if conn: self._release_connection(conn, success=False)

    def log_stage_failure(self, log_id: int, end_time: datetime, error_message: str, details: Optional[Dict] = None):
        """
        Updates a stage log to 'FAILED'.
        """
        if not self.db_pool:
            logger.error(f"StatusPersistenceService: Cannot log stage failure for log_id {log_id}, pool not available.")
            return

        sql = """
            UPDATE connector_run_stage_logs
            SET status = %s, end_time = %s, duration_ms = EXTRACT(EPOCH FROM (%s - start_time)) * 1000, error_message = %s, details = %s
            WHERE log_id = %s;
        """
        conn = None
        try:
            conn = self._get_connection()
            if not conn: return

            details_json = psycopg2.extras.Json(details) if details else None
            with conn.cursor() as cur:
                cur.execute(sql, ('FAILED', end_time.astimezone(timezone.utc), 
                                   end_time.astimezone(timezone.utc), error_message, details_json, log_id))
            self._release_connection(conn, success=True)
            logger.debug(f"Logged stage failure: log_id={log_id}")
        except psycopg2.Error as e:
            logger.error(f"StatusPersistenceService: DB error logging stage failure for log_id {log_id}: {e}", exc_info=True)
            if conn: self._release_connection(conn, success=False)
        except Exception as e:
            logger.error(f"StatusPersistenceService: Unexpected error logging stage failure for log_id {log_id}: {e}", exc_info=True)
            if conn: self._release_connection(conn, success=False)

    def close_pool(self):
        """Closes the database connection pool."""
        if self.db_pool:
            try:
                self.db_pool.closeall()
                logger.info("StatusPersistenceService: Database connection pool closed.")
            except Exception as e: # Broad exception as pool.closeall() might not specify all errors
                logger.error(f"StatusPersistenceService: Error closing database connection pool: {e}", exc_info=True)
            self.db_pool = None

    def __del__(self):
        """Ensure pool is closed when service instance is garbage collected."""
        self.close_pool()

# Example Usage (for testing this service directly, requires DB setup and env vars)
if __name__ == '__main__':
    # Configure basic logging for the example
    logging.basicConfig(level=logging.DEBUG,
                        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    logger.info("Attempting to initialize StatusPersistenceService for direct testing...")
    
    # Ensure environment variables are set for this test:
    # UCF_PG_HOST, UCF_PG_PORT, UCF_PG_USER, UCF_PG_PASSWORD, UCF_PG_DBNAME
    # Example:
    # os.environ["UCF_PG_HOST"] = "localhost"
    # os.environ["UCF_PG_USER"] = "youruser"
    # os.environ["UCF_PG_PASSWORD"] = "yourpass"
    # os.environ["UCF_PG_DBNAME"] = "yourdb"
    # os.environ["UCF_PG_PORT"] = "5432"


    status_service = StatusPersistenceService()

    if not status_service.db_pool:
        logger.error("Failed to initialize StatusPersistenceService. Exiting example.")
    else:
        logger.info("StatusPersistenceService initialized. Running example operations...")
        run_id_example = f"test_run_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        connector_id_example = "test_connector_001"
        connector_type_example = "TestConnector"
        
        # Test log_stage_start
        stage_name_fetch = "fetch_data"
        start_time_fetch = datetime.now(timezone.utc)
        fetch_log_id = status_service.log_stage_start(run_id_example, connector_id_example, connector_type_example, stage_name_fetch, start_time_fetch)
        
        if fetch_log_id:
            logger.info(f"Stage '{stage_name_fetch}' started, log_id: {fetch_log_id}")
            # Simulate work
            import time
            time.sleep(0.1) # 100ms
            end_time_fetch = datetime.now(timezone.utc)
            details_fetch = {"items_fetched": 100, "source_path": "/test/path"}
            status_service.log_stage_complete(fetch_log_id, end_time_fetch, details_fetch)
            logger.info(f"Stage '{stage_name_fetch}' completed.")
        else:
            logger.error(f"Failed to log start for stage '{stage_name_fetch}'.")

        # Test log_stage_failure
        stage_name_preprocess = "preprocess"
        start_time_preprocess = datetime.now(timezone.utc)
        preprocess_log_id = status_service.log_stage_start(run_id_example, connector_id_example, connector_type_example, stage_name_preprocess, start_time_preprocess)

        if preprocess_log_id:
            logger.info(f"Stage '{stage_name_preprocess}' started, log_id: {preprocess_log_id}")
            # Simulate work and failure
            time.sleep(0.05) # 50ms
            end_time_preprocess = datetime.now(timezone.utc)
            error_msg_preprocess = "Simulated error: File not found during preprocessing."
            details_preprocess_fail = {"file_processed": "non_existent_file.txt"}
            status_service.log_stage_failure(preprocess_log_id, end_time_preprocess, error_msg_preprocess, details_preprocess_fail)
            logger.info(f"Stage '{stage_name_preprocess}' failed.")
        else:
            logger.error(f"Failed to log start for stage '{stage_name_preprocess}'.")

        # Test another successful stage
        stage_name_chunk = "chunk"
        start_time_chunk = datetime.now(timezone.utc)
        chunk_log_id = status_service.log_stage_start(run_id_example, connector_id_example, connector_type_example, stage_name_chunk, start_time_chunk)
        if chunk_log_id:
            logger.info(f"Stage '{stage_name_chunk}' started, log_id: {chunk_log_id}")
            time.sleep(0.2) # 200ms
            end_time_chunk = datetime.now(timezone.utc)
            details_chunk = {"items_processed": 100, "chunks_created": 500}
            status_service.log_stage_complete(chunk_log_id, end_time_chunk, details_chunk)
            logger.info(f"Stage '{stage_name_chunk}' completed.")
        else:
            logger.error(f"Failed to log start for stage '{stage_name_chunk}'.")

        logger.info("Example operations finished. Closing pool...")
        status_service.close_pool()
        logger.info("Pool closed.")