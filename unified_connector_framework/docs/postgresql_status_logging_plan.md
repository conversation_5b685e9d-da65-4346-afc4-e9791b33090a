# Architectural Plan: PostgreSQL for Connector Run Stage Status Logging

**Date:** 2025-06-17

**Status:** Approved

## 1. Goal

Persistently store the status (STARTED, COMPLETED, FAILED), timings, and relevant metadata for each stage (`fetch_data`, `preprocess`, `chunk`, `analyze`, `store`) of every connector execution run in a PostgreSQL database. This enhances observability, persistence, and diagnosability of connector operations.

## 2. Key Components & Changes

*   **New PostgreSQL Table:** `connector_run_stage_logs` for storing individual stage event details.
*   **New Service Module:** [`unified_connector_framework/services/status_persistence_service.py`](unified_connector_framework/services/status_persistence_service.py) to encapsulate database interaction logic.
*   **Modified Core Module:** [`unified_connector_framework/core/orchestrator.py`](unified_connector_framework/core/orchestrator.py) to integrate with the `StatusPersistenceService`.
*   **Configuration:** PostgreSQL connection details will be provided via environment variables.
*   **Dependencies:** A PostgreSQL client library for Python (e.g., `psycopg2-binary`).

## 3. Detailed Design

### A. PostgreSQL Table Schema (`connector_run_stage_logs`)

```sql
CREATE TABLE connector_run_stage_logs (
    log_id SERIAL PRIMARY KEY,          -- Unique ID for each log entry for a stage instance
    run_id VARCHAR(255) NOT NULL,       -- Unique ID for a single connector execution run (e.g., UUID)
    connector_id VARCHAR(255) NOT NULL, -- Identifier of the connector instance (user-defined)
    connector_type VARCHAR(255),        -- Class name of the connector (e.g., GDriveConnector)
    stage_name VARCHAR(100) NOT NULL,   -- e.g., 'fetch_data', 'preprocess', 'chunk', 'analyze', 'store'
    status VARCHAR(50) NOT NULL,        -- e.g., 'STARTED', 'COMPLETED', 'FAILED', 'SKIPPED'
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    duration_ms INTEGER,                -- Duration in milliseconds
    details JSONB,                      -- Stage-specific results/counts (e.g., items_fetched, chunks_created)
    error_message TEXT,                 -- Error message if status is 'FAILED'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP -- Timestamp of when this log entry was first created (e.g., stage start)
);

-- Optional: Indexes for querying
CREATE INDEX idx_run_id ON connector_run_stage_logs (run_id);
CREATE INDEX idx_connector_id_stage ON connector_run_stage_logs (connector_id, stage_name, start_time);
CREATE INDEX idx_status ON connector_run_stage_logs (status);
CREATE INDEX idx_start_time ON connector_run_stage_logs (start_time);
```

**Notes on Fields:**
*   `run_id`: Generated by the orchestrator at the start of each `run_connector` call.
*   `details`: Will initially capture item counts relevant to each stage (e.g., `items_fetched`, `items_processed`, `chunks_created`, `analysis_outputs_count`, `items_stored_vector`, `nodes_created_graph`).

### B. `StatusPersistenceService` Design

**File:** [`unified_connector_framework/services/status_persistence_service.py`](unified_connector_framework/services/status_persistence_service.py)

**Class:** `StatusPersistenceService`

*   **`__init__(self)`:**
    *   Retrieves database connection parameters from environment variables (e.g., `PG_HOST`, `PG_PORT`, `PG_USER`, `PG_PASSWORD`, `PG_DBNAME`).
    *   Initializes a database connection pool (e.g., using `psycopg2.pool.SimpleConnectionPool`).
    *   Handles potential errors during pool initialization.
*   **`_get_connection(self)`:** Helper to get a connection from the pool.
*   **`_release_connection(self, conn, success: bool)`:** Helper to release a connection, performing commit or rollback based on `success`.
*   **`log_stage_start(self, run_id: str, connector_id: str, connector_type: str, stage_name: str, start_time: datetime) -> Optional[int]`:**
    *   Inserts a new record into `connector_run_stage_logs` with `status = 'STARTED'`.
    *   Returns the `log_id` (primary key) of the newly inserted record.
    *   Returns `None` if the database operation fails (error will be logged).
*   **`log_stage_complete(self, log_id: int, end_time: datetime, details: Optional[Dict] = None)`:**
    *   Updates the existing stage log record (identified by `log_id`) to `status = 'COMPLETED'`.
    *   Sets `end_time`, calculates and sets `duration_ms`.
    *   Updates `details` with the provided information.
    *   Logs DB errors and continues if the operation fails.
*   **`log_stage_failure(self, log_id: int, end_time: datetime, error_message: str, details: Optional[Dict] = None)`:**
    *   Updates the existing stage log record (identified by `log_id`) to `status = 'FAILED'`.
    *   Sets `end_time`, calculates and sets `duration_ms`.
    *   Sets `error_message`.
    *   Updates `details` if provided.
    *   Logs DB errors and continues if the operation fails.
*   **`close_pool(self)`:** Method to close the database connection pool gracefully.

**DB Error Handling within Service:**
If a database operation fails, the `StatusPersistenceService` will log the error (e.g., to console/standard logger). It will not raise an exception that stops the orchestrator, allowing connector processing to continue as per the approved error handling strategy.

### C. `ConnectorOrchestrator` Modifications

**File:** [`unified_connector_framework/core/orchestrator.py`](unified_connector_framework/core/orchestrator.py)

*   **`__init__`:**
    *   Accept an instance of `StatusPersistenceService`.
    *   Store this instance (e.g., `self.status_service`).
*   **`run_connector(self, connector: BaseConnector)`:**
    *   Generate a unique `run_id` (e.g., `uuid.uuid4().hex`).
    *   Get `connector_type` (e.g., `connector.__class__.__name__`).
    *   For each stage (`fetch_data`, `preprocess`, `chunk`, `analyze`, `store`):
        1.  Record `stage_start_time = datetime.now(timezone.utc)`.
        2.  Call `stage_log_id = self.status_service.log_stage_start(run_id, connector.connector_id, connector_type, "stage_name", stage_start_time)`.
        3.  Wrap the execution of the connector's stage method (e.g., `connector.fetch_data()`) in a `try...except` block.
        4.  Record `stage_end_time = datetime.now(timezone.utc)`.
        5.  Prepare `stage_details` dictionary (e.g., for `fetch_data`, `{"items_fetched": len(raw_data_items)}`).
        6.  If `stage_log_id` is not `None`:
            *   If successful: Call `self.status_service.log_stage_complete(stage_log_id, stage_end_time, stage_details)`.
            *   If exception occurs: Call `self.status_service.log_stage_failure(stage_log_id, stage_end_time, str(e), stage_details_on_failure)`.
        7.  The existing in-memory `run_summary` can still be built and returned. The orchestrator continues processing subsequent stages even if DB logging for a prior stage failed (as per DB error handling decision).

### D. Database Configuration

*   PostgreSQL connection parameters will be sourced from environment variables:
    *   `UCF_PG_HOST`
    *   `UCF_PG_PORT` (default: 5432)
    *   `UCF_PG_USER`
    *   `UCF_PG_PASSWORD`
    *   `UCF_PG_DBNAME`
*   The `StatusPersistenceService` will be responsible for reading these variables.

### E. Mermaid Diagram (Sequence of Operations)

```mermaid
sequenceDiagram
    participant UserApp as User Application
    participant Orchestrator
    participant Connector
    participant StatusPersistenceService as SPS
    participant PostgreSQL_DB as DB

    UserApp->>Orchestrator: run_connector(Connector_Instance)
    Orchestrator->>Orchestrator: Generate run_id (e.g., UUID)
    Orchestrator->>Orchestrator: connector_type = Connector_Instance.__class__.__name__
    
    %% Fetch Stage
    Orchestrator->>Orchestrator: stage_name = "fetch_data", start_time_fetch = now()
    Orchestrator->>SPS: log_stage_start(run_id, connector_id, connector_type, "fetch_data", start_time_fetch)
    SPS->>DB: INSERT new stage_log (status: STARTED) for fetch_data
    DB-->>SPS: stage_log_id_fetch (or None if DB error)
    SPS-->>Orchestrator: stage_log_id_fetch

    alt stage_log_id_fetch is not None
        Orchestrator->>Connector: fetch_data()
        Connector-->>Orchestrator: raw_data_items
        Orchestrator->>Orchestrator: end_time_fetch = now(), details_fetch = {"items_fetched": len(raw_data_items)}
        Orchestrator->>SPS: log_stage_complete(stage_log_id_fetch, end_time_fetch, details_fetch)
        SPS->>DB: UPDATE stage_log for fetch_data (status: COMPLETED)
    else DB Error during log_stage_start
        Orchestrator->>Orchestrator: (Log SPS error, continue processing stage)
        Orchestrator->>Connector: fetch_data()
        Connector-->>Orchestrator: raw_data_items
        Orchestrator->>Orchestrator: (Stage completed, but start not logged to DB)
    end

    %% Preprocess Stage (Illustrating Failure)
    Orchestrator->>Orchestrator: stage_name = "preprocess", start_time_preprocess = now()
    Orchestrator->>SPS: log_stage_start(run_id, connector_id, connector_type, "preprocess", start_time_preprocess)
    SPS->>DB: INSERT new stage_log (status: STARTED) for preprocess
    DB-->>SPS: stage_log_id_preprocess
    SPS-->>Orchestrator: stage_log_id_preprocess

    alt stage_log_id_preprocess is not None
        Orchestrator->>Connector: preprocess(raw_data_items)
        Note over Connector: Raises Exception!
        Connector-->>Orchestrator: Exception e_preprocess
        Orchestrator->>Orchestrator: end_time_preprocess = now(), error_msg_preprocess = str(e_preprocess)
        Orchestrator->>SPS: log_stage_failure(stage_log_id_preprocess, end_time_preprocess, error_msg_preprocess)
        SPS->>DB: UPDATE stage_log for preprocess (status: FAILED)
    end
            
    Note over Orchestrator, SPS: Similar sequence for chunk, analyze, store stages.
            
    Orchestrator-->>UserApp: run_summary (in-memory, reflects overall success/failure)
```

## 4. Impacted Files

*   **New:**
    *   [`unified_connector_framework/services/status_persistence_service.py`](unified_connector_framework/services/status_persistence_service.py)
    *   [`unified_connector_framework/docs/postgresql_status_logging_plan.md`](unified_connector_framework/docs/postgresql_status_logging_plan.md) (this file)
*   **Potentially New:**
    *   A SQL schema file (e.g., `database_schema/connector_status_schema.sql`) if managed outside this plan.
*   **Modified:**
    *   [`unified_connector_framework/core/orchestrator.py`](unified_connector_framework/core/orchestrator.py)
    *   [`unified_connector_framework/services/__init__.py`](unified_connector_framework/services/__init__.py) (to export `StatusPersistenceService`)
    *   Project `requirements.txt` (or similar) to add `psycopg2-binary`.
    *   Example usage in `orchestrator.py`'s `if __name__ == "__main__":` block to demonstrate setup with the new service.

## 5. Pre-requisites for Implementation

*   Access to a running PostgreSQL database instance for development and testing.
*   The `psycopg2-binary` library (or chosen alternative) available in the Python environment.
*   Environment variables for DB connection details set up in the development environment.

## 6. Next Steps (Post-Approval of this Plan)

1.  Switch to "Code" mode.
2.  Implement the `StatusPersistenceService`.
3.  Modify the `ConnectorOrchestrator` to integrate the service.
4.  Add `psycopg2-binary` to project dependencies.
5.  Update example usage in `orchestrator.py`.
6.  (Optional but Recommended) Write unit/integration tests.