#!/usr/bin/env python3
"""
Analyze Entity Duplication Issues

This script analyzes the current graph to identify entity duplication issues
where the same person appears as different entity types (Person, Manager, Executive, etc.)
"""

import os
import sys
from dotenv import load_dotenv

# Add the parent directory to Python path to enable proper imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from enterprise_kg_minimal.storage.neo4j_client import Neo4jClient, Neo4jConnection

# Load environment variables
load_dotenv()

def analyze_entity_duplication():
    """Analyze entity duplication in the current graph."""
    try:
        neo4j_conn = Neo4jConnection(
            uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            user=os.getenv("NEO4J_USER", "neo4j"),
            password=os.getenv("NEO4J_PASSWORD", "password"),
            database=os.getenv("NEO4J_DATABASE")
        )
        neo4j_client = Neo4jClient(neo4j_conn)
        
        print("🔍 Analyzing Entity Duplication Issues")
        print("=" * 50)
        
        # Find all entities with names (potential people)
        name_query = """
        MATCH (n)
        WHERE n.name IS NOT NULL
        RETURN labels(n) as entity_types, n.name as name, count(*) as count
        ORDER BY n.name, labels(n)
        """
        name_results = neo4j_client.execute_query(name_query)
        
        print("All entities with names:")
        current_name = None
        name_groups = {}
        
        for record in name_results:
            name = record['name']
            entity_type = record['entity_types'][0] if record['entity_types'] else 'Unknown'
            
            if name not in name_groups:
                name_groups[name] = []
            name_groups[name].append(entity_type)
            
            print(f"  {entity_type}: {name}")
        
        print("\n" + "=" * 50)
        print("Potential Duplicates (same name, different types):")
        
        duplicates_found = False
        for name, types in name_groups.items():
            if len(types) > 1:
                duplicates_found = True
                print(f"  {name}: {', '.join(types)}")
        
        if not duplicates_found:
            print("  No obvious duplicates found based on names.")
        
        # Check for similar names that might be the same person
        print("\n" + "=" * 50)
        print("Checking for REPORTS_TO relationships and connectivity:")
        
        reports_query = """
        MATCH (a)-[r:REPORTS_TO]->(b)
        RETURN labels(a)[0] as subordinate_type, a.name as subordinate_name,
               labels(b)[0] as manager_type, b.name as manager_name
        ORDER BY b.name, a.name
        """
        reports_results = neo4j_client.execute_query(reports_query)
        
        if reports_results:
            print("Current REPORTS_TO relationships:")
            for record in reports_results:
                print(f"  {record['subordinate_type']} '{record['subordinate_name']}' reports to {record['manager_type']} '{record['manager_name']}'")
        else:
            print("  No REPORTS_TO relationships found!")
        
        # Check connected components
        print("\n" + "=" * 50)
        print("Checking connected components in the graph:")
        
        components_query = """
        MATCH (n)
        WHERE n.name IS NOT NULL
        WITH n
        CALL {
            WITH n
            MATCH path = (n)-[*]-(connected)
            WHERE connected.name IS NOT NULL
            RETURN collect(DISTINCT connected.name) as component
        }
        RETURN DISTINCT component, size(component) as component_size
        ORDER BY component_size DESC
        """
        
        try:
            components_results = neo4j_client.execute_query(components_query)
            
            print("Connected components (groups of connected entities):")
            for i, record in enumerate(components_results):
                component = record['component']
                size = record['component_size']
                print(f"  Component {i+1} (size {size}): {', '.join(component[:10])}{'...' if len(component) > 10 else ''}")
                
        except Exception as e:
            print(f"  Error analyzing components: {e}")
            
            # Fallback: simpler connectivity check
            simple_connectivity_query = """
            MATCH (p1)-[r]-(p2)
            WHERE p1.name IS NOT NULL AND p2.name IS NOT NULL
            RETURN p1.name as person1, type(r) as relationship, p2.name as person2
            LIMIT 20
            """
            simple_results = neo4j_client.execute_query(simple_connectivity_query)
            
            print("Sample connections between named entities:")
            for record in simple_results:
                print(f"  {record['person1']} --{record['relationship']}--> {record['person2']}")
        
        # Check for John Smith specifically
        print("\n" + "=" * 50)
        print("Analyzing John Smith specifically:")
        
        john_query = """
        MATCH (n)
        WHERE n.name CONTAINS 'John Smith' OR n.name CONTAINS 'John'
        RETURN labels(n)[0] as type, n.name as name, n
        """
        john_results = neo4j_client.execute_query(john_query)
        
        if john_results:
            print("John Smith entities found:")
            for record in john_results:
                print(f"  {record['type']}: {record['name']}")
                
            # Check John's relationships
            john_rel_query = """
            MATCH (john)-[r]-(other)
            WHERE john.name CONTAINS 'John'
            RETURN labels(john)[0] as john_type, john.name as john_name,
                   type(r) as relationship, 
                   labels(other)[0] as other_type, other.name as other_name
            """
            john_rel_results = neo4j_client.execute_query(john_rel_query)
            
            print("John Smith's relationships:")
            for record in john_rel_results:
                print(f"  {record['john_type']} '{record['john_name']}' --{record['relationship']}--> {record['other_type']} '{record['other_name']}'")
        else:
            print("  No John Smith entities found!")
        
        neo4j_client.close()
        
    except Exception as e:
        print(f"Error analyzing graph: {e}")

def suggest_fixes():
    """Suggest ways to fix the entity duplication issues."""
    print("\n" + "🔧 Suggested Fixes:")
    print("=" * 30)
    
    print("1. **Entity Consolidation Post-Processing:**")
    print("   - After LLM extraction, merge entities with same names")
    print("   - Combine properties from different entity types")
    print("   - Preserve all relationships")
    
    print("\n2. **Improved LLM Prompting:**")
    print("   - Use more specific entity type instructions")
    print("   - Provide examples of consistent entity typing")
    print("   - Add coreference resolution instructions")
    
    print("\n3. **Schema Normalization:**")
    print("   - Use 'Person' as the primary label for all people")
    print("   - Add role/position as properties instead of labels")
    print("   - Create consistent naming conventions")
    
    print("\n4. **Graph Post-Processing Rules:**")
    print("   - Merge nodes with identical names")
    print("   - Standardize relationship types")
    print("   - Validate organizational hierarchy")

if __name__ == "__main__":
    analyze_entity_duplication()
    suggest_fixes()
