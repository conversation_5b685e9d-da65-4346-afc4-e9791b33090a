# CSV Processing Guide

This guide explains how to use the CSV processing capabilities of the enterprise_kg_minimal system.

## Overview

The CSV processing feature implements a hybrid approach that combines deterministic pattern recognition with LLM assistance to create knowledge graphs from structured CSV data.

### Key Features

- **Automatic CSV Detection**: Detects CSV content based on file extension and content patterns
- **Schema Inference**: Analyzes column headers and data to infer entity types and relationships
- **Hybrid Processing**: Uses deterministic rules for clear patterns and LLM for ambiguous data
- **Template Matching**: Matches against predefined schemas for common CSV types
- **Flexible Chunking**: Supports row-based and batch-based chunking strategies

## Quick Start

### Basic Usage

```python
from enterprise_kg_minimal import process_document

# Your CSV content
csv_content = """employee_id,name,department,manager_id
E001,<PERSON>,<PERSON>,E005
E002,<PERSON>,<PERSON>,E006
E005,<PERSON>,<PERSON>,
E006,<PERSON>,Marketing,"""

# Process the CSV
result = process_document(
    file_id="employees.csv",
    file_content=csv_content,
    neo4j_uri="bolt://localhost:7687",
    neo4j_user="neo4j",
    neo4j_password="password",
    llm_provider="requesty",
    llm_model="anthropic/claude-3-5-sonnet-20241022"
)

if result["success"]:
    print(f"Created {result['total_entities']} entities")
    print(f"Created {result['total_relationships']} relationships")
```

### Running the Demo

```bash
cd enterprise_kg_minimal
python csv_demo.py
```

### Running Comprehensive Tests

```bash
cd enterprise_kg_minimal
python test_csv_processing.py
```

## How It Works

### 1. CSV Detection

The system automatically detects CSV content by:
- Checking file extensions (`.csv`)
- Analyzing content structure for delimiter patterns
- Validating consistent field counts across rows

### 2. Schema Inference

The CSV analyzer performs:
- **Column Profiling**: Analyzes each column for data types and patterns
- **Entity Type Inference**: Determines likely entity types from column names
- **Relationship Detection**: Identifies foreign key columns and relationship patterns
- **Template Matching**: Matches against predefined schemas (employee, project, task data)

### 3. Chunking Strategies

Two CSV-specific chunking strategies are available:

#### Row-Based Chunking (`csv_row_based`)
- Each chunk contains one or a few rows
- Better for real-time processing
- Maintains fine-grained context

#### Batch-Based Chunking (`csv_batch_based`)
- Each chunk contains multiple rows (default: 50)
- Better for bulk processing
- More efficient for large datasets

### 4. Entity Extraction

The system uses a hybrid approach:

#### Deterministic Extraction
- Uses column headers and data patterns
- Applies predefined relationship mappings
- Fast and reliable for clear patterns

#### LLM-Assisted Extraction
- Handles ambiguous or complex data
- Uses CSV-specific prompts
- Provides fallback for unclear patterns

## Configuration

### Environment Variables

```bash
# Required
REQUESTY_API_KEY="your-api-key"
NEO4J_URI="bolt://localhost:7687"
NEO4J_USER="neo4j"
NEO4J_PASSWORD="your-password"

# Optional
LLM_PROVIDER="requesty"
LLM_MODEL="anthropic/claude-3-5-sonnet-20241022"
```

### Processing Options

```python
result = process_document(
    file_id="data.csv",
    file_content=csv_content,
    content_type="csv",  # Force CSV processing
    chunking_strategy="csv_batch_based",  # or "csv_row_based"
    chunk_size=50,  # Number of rows per chunk
    # ... other parameters
)
```

## Supported CSV Formats

### Employee Data
```csv
employee_id,name,email,department,manager_id,position
E001,John Smith,<EMAIL>,Engineering,E005,Developer
```

### Project Data
```csv
project_id,name,description,manager_id,client_id,status
P001,AI System,Computer vision project,E005,C001,Active
```

### Task Data
```csv
task_id,title,assignee_id,project_id,status,priority
T001,Setup Environment,E001,P001,Complete,High
```

## Schema Inference Results

The system provides detailed schema analysis:

```python
if result["success"] and "schema_inference" in result:
    schema = result["schema_inference"]
    print(f"Primary Entity: {schema['primary_entity_type']}")
    print(f"Confidence: {schema['confidence']}")
    print(f"Identifiers: {schema['identifier_columns']}")
    print(f"Relationships: {schema['relationship_columns']}")
```

## Graph Structure

The CSV processor creates the following graph structure:

```
DataSource (CSV File)
├── Chunk (Row Groups)
│   ├── EXTRACTED_FROM → Entity
│   └── EXTRACTED_FROM → Entity
└── CONTAINS → Chunk

Entities are connected via relationships:
Person → MANAGES → Person
Person → WORKS_IN → Department
Task → ASSIGNED_TO → Person
Project → MANAGED_BY → Person
```

## Common Patterns

### Hierarchical Relationships
- `manager_id` → `MANAGED_BY` relationship
- `department_id` → `BELONGS_TO` relationship
- `team_id` → `MEMBER_OF` relationship

### Assignment Relationships
- `assignee_id` → `ASSIGNED_TO` relationship
- `owner_id` → `OWNED_BY` relationship
- `responsible_id` → `RESPONSIBLE_FOR` relationship

### Project Relationships
- `project_id` → `BELONGS_TO_PROJECT` relationship
- `client_id` → `CLIENT_OF` relationship

## Troubleshooting

### Common Issues

1. **CSV Not Detected**
   - Ensure file has `.csv` extension or force with `content_type="csv"`
   - Check that CSV has consistent delimiter usage

2. **Low Schema Confidence**
   - Review column naming conventions
   - Ensure identifier columns have unique values
   - Check for clear relationship patterns

3. **Missing Relationships**
   - Verify foreign key column naming (use `_id`, `_key` suffixes)
   - Check that referenced entities exist in the data

4. **LLM Extraction Failures**
   - Verify API key is correct
   - Check network connectivity
   - Review LLM provider settings

### Debug Mode

Enable detailed logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Performance Tips

1. **Use Batch Chunking** for large datasets
2. **Optimize Chunk Size** based on your data (20-100 rows typically work well)
3. **Pre-clean Data** to remove empty rows and standardize formats
4. **Use Deterministic Mode** when possible by following naming conventions

## Integration with Existing Workflows

CSV processing integrates seamlessly with the existing document processing pipeline:

```python
# Mixed content processing
documents = [
    ("report.pdf", pdf_content),
    ("employees.csv", csv_content),
    ("feedback.txt", text_content)
]

for file_id, content in documents:
    result = process_document(file_id, content)  # Auto-detects content type
```

## Next Steps

- Explore the generated graph in Neo4j Browser
- Use the hybrid search engine for querying
- Integrate with your existing data pipelines
- Customize entity types and relationships in the constants folder
