#!/usr/bin/env python3
"""
Cluster-Aware Search Demo

This script demonstrates how to identify and use coreference resolution
clusters for improved search capabilities in the enterprise knowledge graph.
"""

import os
import sys
from dotenv import load_dotenv

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enterprise_kg_minimal.storage.neo4j_client import Neo4jClient, Neo4jConnection
from enterprise_kg_minimal.utils.cluster_analyzer import ClusterAnalyzer
from enterprise_kg_minimal.utils.cluster_search import ClusterAwareSearch


def main():
    """Main demo function."""
    print("🔍 Cluster-Aware Search Demo")
    print("=" * 60)
    
    # Load environment variables
    load_dotenv('enterprise_kg_minimal/.env')
    
    # Initialize Neo4j connection
    try:
        connection = Neo4jConnection(
            uri=os.getenv('NEO4J_URI'),
            user=os.getenv('NEO4J_USER'),
            password=os.getenv('NEO4J_PASSWORD'),
            database=os.getenv('NEO4J_DATABASE', 'neo4j')
        )
        client = Neo4jClient(connection)
        
        # Initialize cluster tools
        analyzer = ClusterAnalyzer(client)
        search_engine = ClusterAwareSearch(client)
        
        print("✅ Connected to Neo4j successfully")
        
    except Exception as e:
        print(f"❌ Failed to connect to Neo4j: {e}")
        return 1
    
    try:
        # Demo 1: Find cross-chunk entities (evidence of coreference resolution)
        print("\n🔗 Demo 1: Cross-Chunk Entities (Coreference Evidence)")
        print("-" * 50)
        
        cross_chunk_entities = analyzer.find_cross_chunk_entities(limit=10)
        
        if cross_chunk_entities:
            print(f"Found {len(cross_chunk_entities)} entities appearing across multiple chunks:")
            for entity in cross_chunk_entities:
                print(f"  📍 {entity['entity_name']} ({entity['entity_type']})")
                print(f"     Appears in {entity['chunk_count']} chunks")
                print(f"     Files: {set(entity['file_ids'])}")
                print()
        else:
            print("No cross-chunk entities found.")
        
        # Demo 2: Find potential name clusters
        print("\n👥 Demo 2: Potential Name Clusters")
        print("-" * 50)
        
        person_clusters = analyzer.find_potential_name_clusters(entity_type="Person")
        
        if person_clusters:
            print(f"Found {len(person_clusters)} potential person name clusters:")
            for cluster in person_clusters:
                if cluster['cluster_size'] > 1:
                    print(f"  👤 Cluster: {cluster['potential_cluster']}")
                    print(f"     Size: {cluster['cluster_size']} entities")
                    print()
        else:
            print("No person name clusters found.")
        
        # Demo 3: Cluster-aware search
        print("\n🔍 Demo 3: Cluster-Aware Search")
        print("-" * 50)
        
        # Search for common names that might have variations
        search_queries = ["John", "Sarah", "Project", "CRM", "AI"]
        
        for query in search_queries:
            print(f"\nSearching for: '{query}'")
            results = search_engine.search_entity_with_clusters(query, limit=5)
            
            if results['total_results'] > 0:
                print(f"  Found {results['total_results']} results:")
                for result in results['results']:
                    match_info = f"({result['match_type']}"
                    if 'matched_alias' in result:
                        match_info += f", alias: {result['matched_alias']}"
                    match_info += f", confidence: {result['confidence_score']:.1f})"
                    
                    print(f"    • {result['entity_name']} ({result['entity_type']}) {match_info}")
                
                print(f"  Strategy: {results['search_strategy']}")
            else:
                print("  No results found.")
        
        # Demo 4: Find related entities
        print("\n🌐 Demo 4: Related Entity Discovery")
        print("-" * 50)
        
        # Try to find an entity to explore relationships
        sample_query = """
        MATCH (e:Entity)
        WHERE e.entity_type = 'Person'
        RETURN e.name as name
        LIMIT 1
        """
        sample_results = client.execute_query(sample_query)
        
        if sample_results:
            sample_entity = sample_results[0]['name']
            print(f"Exploring relationships for: {sample_entity}")
            
            related = search_engine.find_related_entities(sample_entity, max_depth=2)
            
            if related['total_related'] > 0:
                print(f"  Found {related['total_related']} related entities:")
                for distance, entities in related['related_entities'].items():
                    print(f"    Distance {distance}:")
                    for entity in entities[:3]:  # Show first 3
                        rel_types = " → ".join(entity['relationship_types'])
                        print(f"      • {entity['entity_name']} ({entity['entity_type']}) via {rel_types}")
            else:
                print("  No related entities found.")
        
        # Demo 5: Search by relationship type
        print("\n🔗 Demo 5: Relationship-Based Search")
        print("-" * 50)
        
        # Find common relationship types
        rel_query = """
        MATCH ()-[r]->()
        WHERE type(r) NOT IN ['CONTAINS', 'EXTRACTED_FROM']
        RETURN type(r) as rel_type, count(r) as count
        ORDER BY count DESC
        LIMIT 3
        """
        rel_results = client.execute_query(rel_query)
        
        if rel_results:
            for rel_record in rel_results:
                rel_type = rel_record['rel_type']
                count = rel_record['count']
                
                print(f"\nRelationship type: {rel_type} ({count} instances)")
                
                rel_search = search_engine.search_by_relationship(rel_type, limit=3)
                for rel in rel_search:
                    print(f"  • {rel['source_entity']} --{rel['relationship_type']}--> {rel['target_entity']}")
        
        # Demo 6: Generate search aliases
        print("\n🏷️  Demo 6: Search Alias Generation")
        print("-" * 50)
        
        # Get some sample entities to generate aliases for
        sample_entities_query = """
        MATCH (e:Entity)
        WHERE e.entity_type IN ['Person', 'Company', 'System']
        RETURN e.name as name, e.entity_type as type
        LIMIT 5
        """
        sample_entities = client.execute_query(sample_entities_query)
        
        for entity in sample_entities:
            aliases = analyzer.generate_search_aliases(entity['name'], entity['type'])
            if len(aliases) > 1:  # Only show if there are additional aliases
                print(f"  {entity['name']} ({entity['type']}):")
                print(f"    Aliases: {', '.join(aliases[1:])}")  # Skip original name
        
        print("\n🎉 Demo completed successfully!")
        print("\n💡 Key Takeaways:")
        print("   • Cross-chunk entities indicate successful coreference resolution")
        print("   • Cluster-aware search improves recall by finding name variations")
        print("   • Relationship-based search reveals entity connections")
        print("   • Search aliases help users find entities with different names")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return 1
    
    finally:
        client.close()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
