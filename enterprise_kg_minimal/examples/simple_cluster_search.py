#!/usr/bin/env python3
"""
Simple Cluster Search Example

A practical example showing how to search for entities using
coreference resolution clusters in your Neo4j knowledge graph.
"""

import os
import sys
from dotenv import load_dotenv

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from storage.neo4j_client import Neo4jClient, Neo4jConnection


def search_entities_with_clusters(client, search_term, entity_type=None):
    """
    Search for entities using cluster-aware techniques.
    
    Args:
        client: Neo4j client
        search_term: Term to search for
        entity_type: Optional entity type filter
        
    Returns:
        Dictionary with search results
    """
    print(f"\n🔍 Searching for: '{search_term}'" + (f" (type: {entity_type})" if entity_type else ""))
    print("-" * 50)
    
    results = {
        'direct_matches': [],
        'cross_chunk_matches': [],
        'related_matches': []
    }
    
    # 1. Direct matches
    direct_query = """
    MATCH (e:Entity)
    WHERE e.name CONTAINS $search_term
    """ + ("AND e.entity_type = $entity_type" if entity_type else "") + """
    RETURN e.name as name, e.entity_type as type
    ORDER BY e.name
    LIMIT 10
    """
    
    params = {"search_term": search_term}
    if entity_type:
        params["entity_type"] = entity_type
    
    try:
        direct_results = client.execute_query(direct_query, params)
        results['direct_matches'] = [dict(r) for r in direct_results]
        
        if results['direct_matches']:
            print(f"📍 Direct matches ({len(results['direct_matches'])}):")
            for match in results['direct_matches']:
                print(f"  • {match['name']} ({match['type']})")
        
        # 2. Cross-chunk matches (evidence of coreference resolution)
        cross_chunk_query = """
        MATCH (c:Chunk)-[:EXTRACTED_FROM]->(e:Entity)
        WHERE e.name CONTAINS $search_term
        """ + ("AND e.entity_type = $entity_type" if entity_type else "") + """
        WITH e, count(DISTINCT c) as chunk_count
        WHERE chunk_count > 1
        RETURN e.name as name, e.entity_type as type, chunk_count
        ORDER BY chunk_count DESC, e.name
        LIMIT 5
        """
        
        cross_chunk_results = client.execute_query(cross_chunk_query, params)
        results['cross_chunk_matches'] = [dict(r) for r in cross_chunk_results]
        
        if results['cross_chunk_matches']:
            print(f"\n🔗 Cross-chunk entities ({len(results['cross_chunk_matches'])}):")
            for match in results['cross_chunk_matches']:
                print(f"  • {match['name']} ({match['type']}) - appears in {match['chunk_count']} chunks")
        
        # 3. Related entities
        if results['direct_matches']:
            # Use first direct match to find related entities
            first_match = results['direct_matches'][0]['name']
            
            related_query = """
            MATCH (e:Entity {name: $entity_name})-[r]-(related:Entity)
            WHERE related.name <> $entity_name
            RETURN DISTINCT related.name as name, related.entity_type as type, type(r) as relationship
            ORDER BY related.name
            LIMIT 5
            """
            
            related_results = client.execute_query(related_query, {"entity_name": first_match})
            results['related_matches'] = [dict(r) for r in related_results]
            
            if results['related_matches']:
                print(f"\n🌐 Related to '{first_match}' ({len(results['related_matches'])}):")
                for match in results['related_matches']:
                    print(f"  • {match['name']} ({match['type']}) via {match['relationship']}")
    
    except Exception as e:
        print(f"❌ Search error: {e}")
    
    return results


def show_cluster_statistics(client):
    """Show statistics about potential clusters in the graph."""
    print("\n📊 Cluster Statistics")
    print("=" * 50)
    
    try:
        # Entity type distribution
        type_query = """
        MATCH (e:Entity)
        RETURN e.entity_type as type, count(e) as count
        ORDER BY count DESC
        LIMIT 10
        """
        
        type_results = client.execute_query(type_query)
        print("\nEntity types:")
        for record in type_results:
            print(f"  📋 {record['type']}: {record['count']} entities")
        
        # Cross-chunk entities (evidence of coreference resolution)
        cluster_query = """
        MATCH (c:Chunk)-[:EXTRACTED_FROM]->(e:Entity)
        WITH e, count(DISTINCT c) as chunk_count
        WHERE chunk_count > 1
        RETURN count(e) as cross_chunk_entities, avg(chunk_count) as avg_chunks
        """
        
        cluster_results = client.execute_query(cluster_query)
        if cluster_results:
            stats = cluster_results[0]
            print(f"\n🔗 Cross-chunk entities: {stats['cross_chunk_entities']}")
            print(f"📈 Average chunks per cross-chunk entity: {stats['avg_chunks']:.1f}")
        
        # Relationship types
        rel_query = """
        MATCH ()-[r]->()
        WHERE type(r) <> 'CONTAINS' AND type(r) <> 'EXTRACTED_FROM'
        RETURN type(r) as rel_type, count(r) as count
        ORDER BY count DESC
        LIMIT 5
        """
        
        rel_results = client.execute_query(rel_query)
        if rel_results:
            print("\nTop relationship types:")
            for record in rel_results:
                print(f"  🔗 {record['rel_type']}: {record['count']} instances")
    
    except Exception as e:
        print(f"❌ Statistics error: {e}")


def main():
    """Main function to demonstrate cluster search."""
    print("🔍 Simple Cluster Search Demo")
    print("=" * 60)
    
    # Load environment variables
    load_dotenv('.env')
    
    # Connect to Neo4j
    try:
        connection = Neo4jConnection(
            uri=os.getenv('NEO4J_URI'),
            user=os.getenv('NEO4J_USER'),
            password=os.getenv('NEO4J_PASSWORD'),
            database=os.getenv('NEO4J_DATABASE', 'neo4j')
        )
        client = Neo4jClient(connection)
        print("✅ Connected to Neo4j")
        
    except Exception as e:
        print(f"❌ Failed to connect to Neo4j: {e}")
        return 1
    
    try:
        # Show cluster statistics
        show_cluster_statistics(client)
        
        # Example searches
        search_terms = [
            ("John", "Person"),
            ("Sarah", "Person"), 
            ("Project", None),
            ("System", None),
            ("AI", None)
        ]
        
        print("\n🔍 Example Searches")
        print("=" * 50)
        
        for term, entity_type in search_terms:
            results = search_entities_with_clusters(client, term, entity_type)
            
            # Show summary
            total_results = (len(results['direct_matches']) + 
                           len(results['cross_chunk_matches']) + 
                           len(results['related_matches']))
            
            if total_results == 0:
                print(f"  No results found for '{term}'")
        
        print("\n💡 How to Use These Results:")
        print("  • Direct matches: Entities containing your search term")
        print("  • Cross-chunk entities: High-confidence clusters (appear in multiple chunks)")
        print("  • Related entities: Connected through relationships")
        print("  • Use cross-chunk entities for the most reliable search results")
        
        print("\n🎯 Next Steps:")
        print("  • Try searching for names you saw in the test documents")
        print("  • Look for entities that appear in multiple chunks")
        print("  • Explore relationships between entities")
        print("  • Use the cluster information to improve your application's search")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return 1
    
    finally:
        client.close()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
