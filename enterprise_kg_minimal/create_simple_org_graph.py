#!/usr/bin/env python3
"""
Create Simple Organizational Graph

This script creates a clean, simple organizational hierarchy graph
that's perfect for visual presentations. No complex Jira cross-connections.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the parent directory to Python path to enable proper imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from enterprise_kg_minimal.core.document_processor import process_document
from enterprise_kg_minimal.storage.neo4j_client import Neo4jClient, Neo4jConnection

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def clear_existing_graph():
    """Clear the existing Neo4j graph to start fresh."""
    try:
        neo4j_conn = Neo4jConnection(
            uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            user=os.getenv("NEO4J_USER", "neo4j"),
            password=os.getenv("NEO4J_PASSWORD", "password"),
            database=os.getenv("NEO4J_DATABASE")
        )
        neo4j_client = Neo4jClient(neo4j_conn)
        
        logger.info("Clearing existing graph data...")
        
        # Delete all nodes and relationships
        neo4j_client.execute_query("MATCH (n) DETACH DELETE n")
        
        logger.info("Graph cleared successfully")
        neo4j_client.close()
        
    except Exception as e:
        logger.error(f"Failed to clear graph: {e}")
        raise


def process_organization_document():
    """Process the simple organizational structure document."""
    
    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Process organizational structure document
    logger.info("Processing organizational structure document...")
    org_file_path = os.path.join(script_dir, "documents", "simple_organization_structure.txt")
    
    with open(org_file_path, 'r') as f:
        org_content = f.read()
    
    result = process_document(
        file_id="techcorp_organization_structure",
        file_content=org_content,
        neo4j_uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        neo4j_user=os.getenv("NEO4J_USER", "neo4j"),
        neo4j_password=os.getenv("NEO4J_PASSWORD", "password"),
        neo4j_database=os.getenv("NEO4J_DATABASE"),
        llm_provider=os.getenv("LLM_PROVIDER", "requesty"),
        llm_model=os.getenv("LLM_MODEL", "anthropic/claude-3-5-sonnet-20241022"),
        llm_api_key=os.getenv("REQUESTY_API_KEY"),
        chunking_strategy="fixed_size",
        chunk_size=600,
        chunk_overlap=50,
        content_type="organizational",
        enable_coreference_resolution=True
    )
    
    logger.info(f"Organization document processing result: {result}")
    return result


def analyze_simple_graph():
    """Analyze the created simple graph."""
    try:
        neo4j_conn = Neo4jConnection(
            uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            user=os.getenv("NEO4J_USER", "neo4j"),
            password=os.getenv("NEO4J_PASSWORD", "password"),
            database=os.getenv("NEO4J_DATABASE")
        )
        neo4j_client = Neo4jClient(neo4j_conn)
        
        logger.info("Analyzing simple organizational graph...")
        
        # Get node counts by type
        node_query = """
        MATCH (n)
        RETURN labels(n)[0] as node_type, count(n) as count
        ORDER BY count DESC
        """
        node_results = neo4j_client.execute_query(node_query)
        
        logger.info("Node counts by type:")
        for record in node_results:
            logger.info(f"  {record['node_type']}: {record['count']}")
        
        # Get relationship counts by type
        rel_query = """
        MATCH ()-[r]->()
        RETURN type(r) as relationship_type, count(r) as count
        ORDER BY count DESC
        """
        rel_results = neo4j_client.execute_query(rel_query)
        
        logger.info("Relationship counts by type:")
        for record in rel_results:
            logger.info(f"  {record['relationship_type']}: {record['count']}")
        
        # Get organizational hierarchy
        hierarchy_query = """
        MATCH (p:Person)
        RETURN p.name as person_name
        ORDER BY p.name
        """
        hierarchy_results = neo4j_client.execute_query(hierarchy_query)
        
        logger.info("People in the organization:")
        for record in hierarchy_results:
            logger.info(f"  {record['person_name']}")
        
        # Get departments
        dept_query = """
        MATCH (d:Department)
        RETURN d.name as dept_name
        """
        dept_results = neo4j_client.execute_query(dept_query)
        
        logger.info("Departments:")
        for record in dept_results:
            logger.info(f"  {record['dept_name']}")
        
        # Get reporting relationships
        reporting_query = """
        MATCH (p1:Person)-[r:reports_to]->(p2:Person)
        RETURN p1.name as subordinate, p2.name as manager
        ORDER BY p2.name, p1.name
        """
        reporting_results = neo4j_client.execute_query(reporting_query)
        
        logger.info("Reporting relationships:")
        for record in reporting_results:
            logger.info(f"  {record['subordinate']} reports to {record['manager']}")
        
        # Get project assignments
        project_query = """
        MATCH (p:Person)-[r:leads|contributes_to|manages]->(proj:Project)
        RETURN p.name as person, type(r) as relationship, proj.name as project
        ORDER BY proj.name, p.name
        """
        project_results = neo4j_client.execute_query(project_query)
        
        logger.info("Project relationships:")
        for record in project_results:
            logger.info(f"  {record['person']} {record['relationship']} {record['project']}")
        
        neo4j_client.close()
        
    except Exception as e:
        logger.error(f"Failed to analyze graph: {e}")


def main():
    """Main function to create the simple organizational graph."""
    
    print("🏢 Creating Simple Organizational Graph")
    print("=" * 40)
    
    try:
        # Step 1: Clear existing graph
        print("\n🧹 Step 1: Clearing existing graph...")
        clear_existing_graph()
        
        # Step 2: Process organizational document
        print("\n📄 Step 2: Processing organizational structure...")
        result = process_organization_document()
        
        # Step 3: Analyze the created graph
        print("\n🔍 Step 3: Analyzing organizational graph...")
        analyze_simple_graph()
        
        print("\n✅ Simple organizational graph created successfully!")
        print("\nThe graph now contains:")
        print("- Clean organizational hierarchy")
        print("- Department structures")
        print("- Reporting relationships")
        print("- Project assignments")
        print("- Technology usage")
        print("- Office locations")
        print("\n🎯 Perfect for visual presentations!")
        
        # Provide some example queries for the presentation
        print("\n💡 Example queries for demos:")
        print("1. 'Who reports to Michael Chen?'")
        print("2. 'What projects is John Smith working on?'")
        print("3. 'Who works in the AI Research Department?'")
        print("4. 'What technologies does Alex Rodriguez use?'")
        print("5. 'Show me the organizational hierarchy'")
        
    except Exception as e:
        logger.error(f"Failed to create simple organizational graph: {e}")
        print(f"\n❌ Error: {e}")
        print("\nPlease check your .env file and ensure Neo4j is running.")


if __name__ == "__main__":
    main()
