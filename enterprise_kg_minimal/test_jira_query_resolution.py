#!/usr/bin/env python3
"""
Comprehensive Test Script for Jira Query Resolution

This script tests the complete end-to-end flow:
1. Creates dummy Jira data and processes it into knowledge graph
2. Simulates vector database results with dummy embeddings
3. Tests query resolution for "What is UserA's pending task for Project A for today?"
4. Shows step-by-step how the system finds and processes data

Run with: PYTHONPATH=. python3 enterprise_kg_minimal/test_jira_query_resolution.py
"""

import json
import logging
import os
from datetime import datetime, date, timedelta
from typing import Dict, Any, List
from dotenv import load_dotenv
from enterprise_kg_minimal import process_document
from enterprise_kg_minimal.search import (
    create_hybrid_search_engine,
    SearchStrategy,
    VectorMetadata
)

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def create_comprehensive_jira_data() -> str:
    """
    Create comprehensive Jira data that will generate the entities and relationships
    needed to answer our test query.
    """
    today = date.today().strftime("%Y-%m-%d")
    tomorrow = (date.today() + timedelta(days=1)).strftime("%Y-%m-%d")
    yesterday = (date.today() - timedelta(days=1)).strftime("%Y-%m-%d")
    
    jira_content = f"""
PROJECT ALPHA (PROJ-A) - JIRA DATA EXPORT
Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

=== PROJECT INFORMATION ===
Project Key: PROJ-A
Project Name: Project Alpha
Description: Strategic AI initiative for customer analytics platform
Project Lead: Alice Johnson (UserA)
Status: Active

=== TEAM MEMBERS ===
- Alice Johnson (UserA) - Senior Developer - <EMAIL>
- Bob Smith (UserB) - UI Designer - <EMAIL>  
- Carol Davis (UserC) - Product Manager - <EMAIL>
- David Wilson (UserD) - QA Engineer - <EMAIL>

=== CURRENT SPRINT ===
Sprint: Sprint-2024-01
Start Date: 2024-01-15
End Date: 2024-01-29
Status: Active

=== ISSUES AND TASKS ===

Issue PROJ-A-123: Implement user authentication system
Type: Task
Summary: Create secure login system with multi-factor authentication
Status: In Progress
Priority: High
Assigned to: UserA
Reporter: Carol Davis (UserC)
Created: 2024-01-15T09:00:00Z
Due Date: {today}
Sprint: Sprint-2024-01
Story Points: 8
Labels: security, authentication, backend
Description: Implement OAuth 2.0 authentication with MFA support. This task is critical for the security framework and must be completed today.

Comments:
- UserA (2024-01-16): Started working on OAuth integration, making good progress
- UserC (2024-01-17): Please prioritize this for today's deadline

Issue PROJ-A-124: Design user dashboard mockups  
Type: Story
Summary: Create wireframes and mockups for the main user dashboard
Status: To Do
Priority: Medium
Assigned to: UserB
Reporter: Carol Davis (UserC)
Created: 2024-01-16T14:00:00Z
Due Date: {tomorrow}
Sprint: Sprint-2024-01
Story Points: 5
Labels: design, ui, frontend
Description: Design comprehensive dashboard mockups with user analytics widgets

Issue PROJ-A-125: Database performance optimization
Type: Task
Summary: Optimize database queries for better performance
Status: Done
Priority: High
Assigned to: UserA
Reporter: David Wilson (UserD)
Created: 2024-01-10T09:00:00Z
Due Date: {yesterday}
Resolved: 2024-01-17T16:30:00Z
Sprint: Sprint-2024-01
Story Points: 13
Labels: performance, database, backend
Description: Completed database optimization resulting in 40% performance improvement

Issue PROJ-A-126: Setup CI/CD pipeline
Type: Task
Summary: Configure automated deployment pipeline
Status: In Progress
Priority: Medium
Assigned to: UserD
Reporter: Alice Johnson (UserA)
Created: 2024-01-18T10:00:00Z
Due Date: {tomorrow}
Sprint: Sprint-2024-01
Story Points: 5
Labels: devops, automation
Description: Setup Jenkins pipeline for automated testing and deployment

Issue PROJ-A-127: User feedback collection feature
Type: Story
Summary: Implement feedback collection system
Status: To Do
Priority: Low
Assigned to: UserA
Reporter: Carol Davis (UserC)
Created: 2024-01-19T11:00:00Z
Due Date: 2024-01-25
Sprint: Sprint-2024-02
Story Points: 8
Labels: feedback, user-experience
Description: Create system for collecting and analyzing user feedback

=== EPIC INFORMATION ===
Epic PROJ-A-E1: Authentication & Security Framework
Description: Complete security implementation for the platform
Stories: PROJ-A-123, PROJ-A-130, PROJ-A-131
Status: In Progress
Owner: UserA

Epic PROJ-A-E2: User Experience Enhancement
Description: Improve overall user experience and interface
Stories: PROJ-A-124, PROJ-A-127, PROJ-A-132
Status: Planning
Owner: UserB

=== DEPENDENCIES ===
PROJ-A-124 depends on PROJ-A-123 (authentication must be ready for dashboard)
PROJ-A-126 blocks PROJ-A-127 (CI/CD needed before feedback feature)

=== WORKLOG ENTRIES ===
UserA logged 4 hours on PROJ-A-123 on {today}
UserA logged 6 hours on PROJ-A-125 on {yesterday}
UserB logged 3 hours on PROJ-A-124 on {today}
UserD logged 2 hours on PROJ-A-126 on {today}
"""
    
    return jira_content


def create_dummy_vector_metadata(chunk_indices: List[str]) -> List[VectorMetadata]:
    """
    Create dummy vector metadata that simulates Pinecone search results.
    This represents what you'd get from semantic similarity search.
    """
    today = date.today().strftime("%Y-%m-%d")
    
    vector_metadata = []
    
    for i, chunk_id in enumerate(chunk_indices):
        if "jira" in chunk_id:
            # High similarity for Jira-related chunks
            similarity_score = 0.92 - (i * 0.05)
            chunk_text = f"""
            Issue PROJ-A-123: Implement user authentication system
            Status: In Progress, Priority: High, Assigned to: UserA
            Due Date: {today}, Sprint: Sprint-2024-01
            Description: Create secure login system with multi-factor authentication
            UserA logged 4 hours on this task today.
            """
            entity_mentions = ["UserA", "PROJ-A-123", "Project Alpha", "authentication", "In Progress"]
            topic_tags = ["jira", "task", "authentication", "security", "pending"]
            
        elif "project" in chunk_id:
            # Medium similarity for project-related chunks
            similarity_score = 0.78 - (i * 0.03)
            chunk_text = f"""
            Project Alpha (PROJ-A) Status Report
            Team: Alice Johnson (UserA), Bob Smith (UserB), Carol Davis (UserC)
            Current Sprint: Sprint-2024-01
            Active tasks assigned to UserA: authentication system implementation
            """
            entity_mentions = ["Project Alpha", "UserA", "Sprint-2024-01"]
            topic_tags = ["project", "team", "status", "sprint"]
            
        else:
            # Lower similarity for other chunks
            similarity_score = 0.65 - (i * 0.02)
            chunk_text = f"""
            General team handbook information about Project Alpha.
            Team members include UserA, UserB, UserC, and UserD.
            Project focuses on AI analytics platform development.
            """
            entity_mentions = ["Project Alpha", "UserA", "team"]
            topic_tags = ["handbook", "team", "general"]
        
        metadata = VectorMetadata(
            chunk_id=chunk_id,
            file_id=chunk_id.split("_chunk_")[0],
            chunk_text=chunk_text.strip(),
            similarity_score=similarity_score,
            metadata={
                "project": "Project Alpha",
                "content_type": "jira" if "jira" in chunk_id else "project",
                "created_date": today,
                "assignee": "UserA" if "UserA" in chunk_text else None
            },
            content_length=len(chunk_text),
            entity_mentions=entity_mentions,
            topic_tags=topic_tags
        )
        
        vector_metadata.append(metadata)
    
    return vector_metadata


def test_knowledge_graph_creation():
    """
    Test Step 1: Create knowledge graph from Jira data
    """
    print("🔄 STEP 1: Creating Knowledge Graph from Jira Data")
    print("=" * 60)
    
    # Create comprehensive Jira data
    jira_content = create_comprehensive_jira_data()
    
    print(f"📄 Sample of Jira content ({len(jira_content)} characters):")
    print(jira_content[:400] + "...\n")
    
    # Process into knowledge graph
    try:
        result = process_document(
            file_id="jira_project_alpha_comprehensive",
            file_content=jira_content,
            neo4j_uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            neo4j_user=os.getenv("NEO4J_USER", "neo4j"),
            neo4j_password=os.getenv("NEO4J_PASSWORD", "password"),
            neo4j_database=os.getenv("NEO4J_DATABASE"),
            llm_provider=os.getenv("LLM_PROVIDER", "openai"),
            llm_model=os.getenv("LLM_MODEL", "gpt-4o"),
            llm_api_key=os.getenv("REQUESTY_API_KEY"),
            content_type="project",  # Use project type for better entity extraction
            chunking_strategy="hybrid",
            chunk_size=1000,
            chunk_overlap=150,
            enable_coreference_resolution=True
        )
        
        if result.get('success'):
            print("✅ Knowledge Graph Creation Results:")
            print(f"   📊 Chunks created: {result.get('chunks_created', 0)}")
            print(f"   🏷️  Total entities: {result.get('total_entities', 0)}")
            print(f"   🔗 Total relationships: {result.get('total_relationships', 0)}")
            print(f"   📁 File node created: {result.get('file_node_created', False)}")
            print(f"   🔗 Contains relationships: {result.get('contains_relationships_created', 0)}")
            
            # Show chunk information
            if 'chunk_results' in result:
                print(f"\n   📦 Chunk Details:")
                for i, chunk_result in enumerate(result['chunk_results'][:3]):  # Show first 3
                    print(f"      Chunk {i+1}: {chunk_result.get('entities_extracted', 0)} entities, "
                          f"{chunk_result.get('relationships_extracted', 0)} relationships")
            
            return True, result
        else:
            print(f"❌ Knowledge graph creation failed: {result.get('error')}")
            return False, result
            
    except Exception as e:
        print(f"❌ Error creating knowledge graph: {e}")
        import traceback
        traceback.print_exc()
        return False, None


def test_query_resolution(kg_result):
    """
    Test Step 2: Query resolution with dummy vector data
    """
    print("\n🔍 STEP 2: Testing Query Resolution")
    print("=" * 60)
    
    # Define our test query
    test_query = "What is UserA's pending task for Project A for today?"
    print(f"🎯 Test Query: '{test_query}'")
    
    # Use actual chunk indices from the knowledge graph creation
    if kg_result and 'chunk_results' in kg_result:
        chunk_indices = [
            chunk_result.get('chunk_id', f"jira_project_alpha_comprehensive_chunk_{i}")
            for i, chunk_result in enumerate(kg_result['chunk_results'][:3])
        ]
    else:
        # Fallback to pattern-based chunk indices
        chunk_indices = [
            "jira_project_alpha_comprehensive_chunk_0_3971632e",
            "jira_project_alpha_comprehensive_chunk_1_c876c555",
            "jira_project_alpha_comprehensive_chunk_2_e316e9d5"
        ]
    
    # Create dummy vector metadata
    vector_metadata = create_dummy_vector_metadata(chunk_indices)
    
    print(f"\n📊 Simulated Vector Search Results:")
    for i, metadata in enumerate(vector_metadata):
        print(f"   {i+1}. {metadata.chunk_id} (similarity: {metadata.similarity_score:.3f})")
        print(f"      Entities: {', '.join(metadata.entity_mentions[:3])}...")
        print(f"      Topics: {', '.join(metadata.topic_tags)}")
    
    # Create search engine and execute query
    print(f"\n🔄 Executing Hybrid Search...")
    
    try:
        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            neo4j_user=os.getenv("NEO4J_USER", "neo4j"),
            neo4j_password=os.getenv("NEO4J_PASSWORD", "password"),
            neo4j_database=os.getenv("NEO4J_DATABASE"),
            enable_intelligent_routing=True
        )
        
        # Execute search with Jira-specific filters (using actual relationship types from Neo4j)
        search_result = search_engine.search(
            chunk_indices=chunk_indices,
            query_text=test_query,
            strategy=SearchStrategy.ENTITY_CENTRIC,
            entity_types={"Person", "Task", "Issue", "Project", "Status", "Epic"},
            relationship_types={
                "ASSIGNED_TO", "WORKS_FOR", "LEADS", "CREATED_BY",
                "WORKS_ON", "OWNS", "EXTRACTED_FROM"
            },
            max_results=20,
            expansion_depth=2,
            boost_high_importance=True,
            vector_metadata=vector_metadata
        )
        
        return True, search_result
        
    except Exception as e:
        print(f"❌ Error during query resolution: {e}")
        import traceback
        traceback.print_exc()
        return False, None
    
    finally:
        if 'search_engine' in locals():
            search_engine.neo4j_client.close()


def analyze_search_results(search_result):
    """
    Test Step 3: Analyze and display search results
    """
    print("\n📈 STEP 3: Search Results Analysis")
    print("=" * 60)
    
    if not search_result:
        print("❌ No search results to analyze")
        return
    
    # Overall metrics
    print(f"✅ Search Execution Results:")
    print(f"   📊 Total results found: {search_result.total_results}")
    print(f"   ⏱️  Processing time: {search_result.processing_time_ms:.2f}ms")
    print(f"   🎯 Strategy used: {search_result.strategy_used.value}")
    print(f"   📏 Coverage score: {search_result.coverage_score:.3f}")
    print(f"   🔗 Coherence score: {search_result.coherence_score:.3f}")
    print(f"   🎯 Relevance score: {search_result.relevance_score:.3f}")
    
    # Entity analysis
    entities = search_result.graph_context.entities
    print(f"\n🏷️  Entities Found ({len(entities)}):")
    
    # Group entities by type
    entity_groups = {}
    for entity in entities:
        entity_type = entity.entity_type
        if entity_type not in entity_groups:
            entity_groups[entity_type] = []
        entity_groups[entity_type].append(entity.name)
    
    for entity_type, names in entity_groups.items():
        print(f"   {entity_type}: {', '.join(names[:5])}" + 
              (f" (+{len(names)-5} more)" if len(names) > 5 else ""))
    
    # Relationship analysis
    relationships = search_result.graph_context.relationships
    print(f"\n🔗 Relationships Found ({len(relationships)}):")
    
    # Show most relevant relationships
    relevant_relationships = [
        rel for rel in relationships
        if any(keyword in rel.source_entity.lower() or keyword in rel.target_entity.lower()
               for keyword in ['usera', 'proj-a', 'project a', 'authentication'])
    ]

    for i, rel in enumerate(relevant_relationships[:8]):  # Show top 8
        print(f"   {i+1}. {rel.source_entity} --{rel.relationship_type}--> {rel.target_entity}")
        if hasattr(rel, 'confidence_score'):
            print(f"      (confidence: {rel.confidence_score:.3f})")
    
    # Answer synthesis
    print(f"\n🎯 Query Answer Synthesis:")
    print(f"   Query: 'What is UserA's pending task for Project A for today?'")
    
    # Look for UserA tasks
    usera_tasks = []
    for rel in relationships:
        if (rel.relationship_type == "ASSIGNED_TO" and
            "usera" in rel.target_entity.lower() and
            any(task_word in rel.source_entity.lower() for task_word in ['proj-a', 'task', 'issue'])):
            usera_tasks.append(rel.source_entity)
    
    if usera_tasks:
        print(f"   ✅ Found UserA's tasks: {', '.join(usera_tasks[:3])}")
        
        # Check for pending status and today's date
        pending_tasks = []
        for task in usera_tasks:
            for rel in relationships:
                if (rel.source_entity == task and rel.relationship_type == "HAS_STATUS" and
                    any(status in rel.target_entity.lower() for status in ['in progress', 'to do', 'pending'])):
                    pending_tasks.append((task, rel.target_entity))
        
        if pending_tasks:
            print(f"   ✅ Pending tasks found: {len(pending_tasks)}")
            for task, status in pending_tasks[:2]:
                print(f"      - {task}: {status}")
        else:
            print(f"   ⚠️  No pending status found for UserA's tasks")
    else:
        print(f"   ⚠️  No tasks assigned to UserA found")
    
    # Debug information
    if search_result.debug_info:
        print(f"\n🔧 Debug Information:")
        for key, value in search_result.debug_info.items():
            if isinstance(value, (int, float, str)):
                print(f"   {key}: {value}")


def main():
    """
    Main test function that runs the complete end-to-end test
    """
    print("🚀 Comprehensive Jira Query Resolution Test")
    print("=" * 80)
    print("Testing: 'What is UserA's pending task for Project A for today?'")
    print("=" * 80)
    
    # Step 1: Create knowledge graph
    kg_success, kg_result = test_knowledge_graph_creation()
    
    if not kg_success:
        print("\n❌ Test failed at knowledge graph creation step")
        return
    
    # Step 2: Test query resolution
    query_success, search_result = test_query_resolution(kg_result)
    
    if not query_success:
        print("\n❌ Test failed at query resolution step")
        return
    
    # Step 3: Analyze results
    analyze_search_results(search_result)
    
    # Final summary
    print(f"\n🎉 TEST SUMMARY")
    print("=" * 40)
    print("✅ Knowledge graph creation: SUCCESS")
    print("✅ Query resolution: SUCCESS") 
    print("✅ Results analysis: COMPLETE")
    
    if search_result and search_result.relevance_score > 0.7:
        print(f"🎯 Overall test result: PASSED (relevance: {search_result.relevance_score:.3f})")
    else:
        print(f"⚠️  Overall test result: PARTIAL (low relevance)")
    
    print(f"\n💡 Key Findings:")
    print(f"   • System can process Jira data into knowledge graph")
    print(f"   • Hybrid search engine can resolve complex queries")
    print(f"   • Entity and relationship extraction works for task management")
    print(f"   • Query routing and strategy selection is functional")


if __name__ == "__main__":
    main()
