#!/usr/bin/env python3
"""
Simple Entity Duplication Fix

This script uses a simpler approach to merge duplicate entities
by manually handling each case.
"""

import os
import sys
from dotenv import load_dotenv

# Add the parent directory to Python path to enable proper imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from enterprise_kg_minimal.storage.neo4j_client import Neo4jClient, Neo4jConnection

# Load environment variables
load_dotenv()

def simple_entity_fix():
    """Simple approach to fix entity duplication."""
    try:
        neo4j_conn = Neo4jConnection(
            uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            user=os.getenv("NEO4J_USER", "neo4j"),
            password=os.getenv("NEO4J_PASSWORD", "password"),
            database=os.getenv("NEO4J_DATABASE")
        )
        neo4j_client = Neo4jClient(neo4j_conn)
        
        print("🔧 Simple Entity Duplication Fix")
        print("=" * 35)
        
        # Step 1: Fix <PERSON>
        print("\n1. Fixing <PERSON>...")
        
        # First, get all relationships from Manager <PERSON>
        get_manager_rels_query = """
        MATCH (manager:Manager {name: '<PERSON>'})-[r]->(target)
        RETURN type(r) as rel_type, labels(target)[0] as target_type, target.name as target_name
        """
        manager_rels = neo4j_client.execute_query(get_manager_rels_query)
        
        # Get relationships TO Manager John Smith
        get_manager_incoming_query = """
        MATCH (source)-[r]->(manager:Manager {name: 'John Smith'})
        RETURN type(r) as rel_type, labels(source)[0] as source_type, source.name as source_name
        """
        manager_incoming = neo4j_client.execute_query(get_manager_incoming_query)
        
        # Create these relationships on Person John Smith
        for rel in manager_rels:
            if rel['rel_type'] == 'REPORTS_TO' and rel['target_name']:
                create_rel_query = f"""
                MATCH (person:Person {{name: 'John Smith'}})
                MATCH (target {{name: '{rel['target_name']}'}})
                MERGE (person)-[:REPORTS_TO]->(target)
                """
                neo4j_client.execute_query(create_rel_query)
        
        for rel in manager_incoming:
            if rel['rel_type'] == 'REPORTS_TO' and rel['source_name']:
                create_rel_query = f"""
                MATCH (source {{name: '{rel['source_name']}'}})
                MATCH (person:Person {{name: 'John Smith'}})
                MERGE (source)-[:REPORTS_TO]->(person)
                """
                neo4j_client.execute_query(create_rel_query)
        
        # Delete Manager John Smith
        delete_manager_query = """
        MATCH (manager:Manager {name: 'John Smith'})
        DETACH DELETE manager
        """
        neo4j_client.execute_query(delete_manager_query)
        
        # Add Manager label to Person John Smith
        add_manager_label_query = """
        MATCH (person:Person {name: 'John Smith'})
        SET person:Manager
        SET person.role = 'Engineering Manager'
        """
        neo4j_client.execute_query(add_manager_label_query)
        
        print("   ✅ John Smith fixed")
        
        # Step 2: Fix Michael Chen
        print("\n2. Fixing Michael Chen...")
        
        # Get Executive Michael Chen relationships
        get_exec_rels_query = """
        MATCH (exec:Executive {name: 'Michael Chen'})-[r]->(target)
        RETURN type(r) as rel_type, labels(target)[0] as target_type, target.name as target_name
        """
        exec_rels = neo4j_client.execute_query(get_exec_rels_query)
        
        get_exec_incoming_query = """
        MATCH (source)-[r]->(exec:Executive {name: 'Michael Chen'})
        RETURN type(r) as rel_type, labels(source)[0] as source_type, source.name as source_name
        """
        exec_incoming = neo4j_client.execute_query(get_exec_incoming_query)
        
        # Create relationships on Person Michael Chen
        for rel in exec_incoming:
            if rel['rel_type'] == 'REPORTS_TO' and rel['source_name']:
                create_rel_query = f"""
                MATCH (source {{name: '{rel['source_name']}'}})
                MATCH (person:Person {{name: 'Michael Chen'}})
                MERGE (source)-[:REPORTS_TO]->(person)
                """
                neo4j_client.execute_query(create_rel_query)
        
        # Delete Executive Michael Chen
        delete_exec_query = """
        MATCH (exec:Executive {name: 'Michael Chen'})
        DETACH DELETE exec
        """
        neo4j_client.execute_query(delete_exec_query)
        
        # Add Executive label to Person Michael Chen
        add_exec_label_query = """
        MATCH (person:Person {name: 'Michael Chen'})
        SET person:Executive
        SET person.role = 'Chief Technology Officer'
        """
        neo4j_client.execute_query(add_exec_label_query)
        
        print("   ✅ Michael Chen fixed")
        
        # Step 3: Fix Alex Rodriguez
        print("\n3. Fixing Alex Rodriguez...")
        
        # Get Employee Alex Rodriguez relationships
        get_emp_incoming_query = """
        MATCH (source)-[r]->(emp:Employee {name: 'Alex Rodriguez'})
        RETURN type(r) as rel_type, labels(source)[0] as source_type, source.name as source_name
        """
        emp_incoming = neo4j_client.execute_query(get_emp_incoming_query)
        
        # Create relationships on Person Alex Rodriguez
        for rel in emp_incoming:
            if rel['rel_type'] == 'REPORTS_TO' and rel['source_name']:
                # This is wrong direction, Alex should report TO someone, not someone to Alex
                pass
        
        # Get outgoing relationships from Employee Alex
        get_emp_rels_query = """
        MATCH (emp:Employee {name: 'Alex Rodriguez'})-[r]->(target)
        RETURN type(r) as rel_type, labels(target)[0] as target_type, target.name as target_name
        """
        emp_rels = neo4j_client.execute_query(get_emp_rels_query)
        
        # Create these on Person Alex
        for rel in emp_rels:
            if rel['rel_type'] == 'REPORTS_TO' and rel['target_name']:
                create_rel_query = f"""
                MATCH (person:Person {{name: 'Alex Rodriguez'}})
                MATCH (target {{name: '{rel['target_name']}'}})
                MERGE (person)-[:REPORTS_TO]->(target)
                """
                neo4j_client.execute_query(create_rel_query)
        
        # Delete Employee Alex Rodriguez
        delete_emp_query = """
        MATCH (emp:Employee {name: 'Alex Rodriguez'})
        DETACH DELETE emp
        """
        neo4j_client.execute_query(delete_emp_query)
        
        # Add Employee label to Person Alex Rodriguez
        add_emp_label_query = """
        MATCH (person:Person {name: 'Alex Rodriguez'})
        SET person:Employee
        SET person.role = 'Senior Software Engineer'
        """
        neo4j_client.execute_query(add_emp_label_query)
        
        print("   ✅ Alex Rodriguez fixed")
        
        # Step 4: Verify the hierarchy
        print("\n4. Verifying the organizational hierarchy...")
        
        hierarchy_query = """
        MATCH (a:Person)-[r:REPORTS_TO]->(b:Person)
        RETURN a.name as subordinate, b.name as manager
        ORDER BY b.name, a.name
        """
        
        hierarchy_results = neo4j_client.execute_query(hierarchy_query)
        
        print(f"\n   📊 Clean REPORTS_TO hierarchy ({len(hierarchy_results)} relationships):")
        for record in hierarchy_results:
            print(f"     {record['subordinate']} → {record['manager']}")
        
        # Check for any remaining duplicates
        duplicate_check_query = """
        MATCH (n)
        WHERE n.name IS NOT NULL
        WITH n.name as name, collect(DISTINCT labels(n)[0]) as types
        WHERE size(types) > 1 AND any(t IN types WHERE t IN ['Person', 'Manager', 'Executive', 'Employee'])
        RETURN name, types
        """
        
        duplicate_results = neo4j_client.execute_query(duplicate_check_query)
        
        if duplicate_results:
            print("\n   ⚠️  Remaining people duplicates:")
            for record in duplicate_results:
                print(f"     {record['name']}: {record['types']}")
        else:
            print("\n   ✅ No people duplicates found!")
        
        neo4j_client.close()
        
        print("\n✅ Simple entity fix completed!")
        print("\nThe organizational hierarchy should now be a single connected component.")
        print("Test with: MATCH (a:Person)-[r:REPORTS_TO]->(b:Person) RETURN a, r, b")
        
    except Exception as e:
        print(f"Error in simple entity fix: {e}")

if __name__ == "__main__":
    simple_entity_fix()
