#!/usr/bin/env python3
"""
CSV Processing Test and Demo

This script demonstrates the CSV processing capabilities of the enterprise_kg_minimal system.
It tests CSV analysis, schema inference, chunking, and graph building with sample data.
"""

import os
import sys
import logging
from typing import Dict, Any

# Add the parent directory to the path to import enterprise_kg_minimal
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enterprise_kg_minimal.core.document_processor import process_document
from enterprise_kg_minimal.core.csv_analyzer import CSVAnalyzer
from enterprise_kg_minimal.core.csv_schema_engine import CSVSchemaEngine
from enterprise_kg_minimal.storage.neo4j_client import Neo4jClient, Neo4jConnection

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Sample CSV data for testing
SAMPLE_EMPLOYEE_CSV = """employee_id,name,email,department,manager_id,position,salary,hire_date
E001,<PERSON>,<EMAIL>,Engineering,E005,Software Engineer,75000,2023-01-15
<PERSON>002,<PERSON>,<EMAIL>,Marketing,E006,Marketing Specialist,65000,2023-02-01
E003,Mike Davis,<EMAIL>,Engineering,E005,Senior Developer,85000,2022-11-10
E004,Lisa Chen,<EMAIL>,HR,E007,HR Coordinator,60000,2023-03-01
E005,David Wilson,<EMAIL>,Engineering,,Engineering Manager,95000,2022-08-15
E006,Emma Brown,<EMAIL>,Marketing,,Marketing Manager,90000,2022-09-01
E007,James Taylor,<EMAIL>,HR,,HR Manager,88000,2022-07-20"""

SAMPLE_PROJECT_CSV = """project_id,name,description,status,manager_id,client_id,start_date,end_date,budget
P001,AI Vision System,Computer vision system for quality control,Active,E005,C001,2023-01-01,2023-12-31,500000
P002,Marketing Campaign,Q2 digital marketing campaign,Planning,E006,C002,2023-04-01,2023-06-30,150000
P003,HR Portal,Employee self-service portal,Active,E007,Internal,2023-02-15,2023-08-15,200000
P004,Mobile App,Customer mobile application,Completed,E005,C001,2022-06-01,2023-01-31,300000"""

SAMPLE_TASK_CSV = """task_id,title,description,status,priority,assignee_id,project_id,created_date,due_date
T001,Setup Development Environment,Configure development tools and environment,Completed,Medium,E001,P001,2023-01-15,2023-01-20
T002,Design Database Schema,Create database design for the system,In Progress,High,E003,P001,2023-01-20,2023-02-15
T003,Create Marketing Materials,Design brochures and digital assets,Planning,Low,E002,P002,2023-03-01,2023-03-15
T004,User Interface Design,Design user interface mockups,Active,High,E001,P003,2023-02-20,2023-03-10
T005,API Development,Develop REST API endpoints,In Progress,High,E003,P001,2023-02-01,2023-03-01"""


def load_config() -> Dict[str, Any]:
    """Load configuration from environment variables."""
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        logger.warning("python-dotenv not installed, using environment variables directly")
    
    config = {
        "neo4j_uri": os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        "neo4j_user": os.getenv("NEO4J_USER", "neo4j"),
        "neo4j_password": os.getenv("NEO4J_PASSWORD", "password"),
        "llm_provider": os.getenv("LLM_PROVIDER", "requesty"),
        "llm_model": os.getenv("LLM_MODEL", "anthropic/claude-3-5-sonnet-20241022"),
        "llm_api_key": os.getenv("REQUESTY_API_KEY")
    }
    
    # Validate required configuration
    if not config["llm_api_key"]:
        raise ValueError("LLM API key not found in environment variables")
    
    return config


def clear_existing_data(config: Dict[str, Any]) -> bool:
    """Clear existing test data from Neo4j."""
    try:
        logger.info("Clearing existing test data from Neo4j...")
        
        neo4j_conn = Neo4jConnection(
            uri=config["neo4j_uri"],
            user=config["neo4j_user"],
            password=config["neo4j_password"]
        )
        neo4j_client = Neo4jClient(neo4j_conn)
        
        # Delete test data (be careful with this in production!)
        queries = [
            "MATCH (n) WHERE n.id STARTS WITH 'test_' DETACH DELETE n",
            "MATCH (n:DataSource) WHERE n.id IN ['employee_data.csv', 'project_data.csv', 'task_data.csv'] DETACH DELETE n"
        ]
        
        for query in queries:
            neo4j_client.execute_query(query)
        
        neo4j_client.close()
        logger.info("✅ Existing test data cleared")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to clear existing data: {e}")
        return False


def test_csv_analysis(csv_content: str, file_name: str) -> Dict[str, Any]:
    """Test CSV analysis and schema inference."""
    logger.info(f"\n🔍 Testing CSV Analysis for {file_name}")
    
    try:
        # Test CSV analyzer
        analyzer = CSVAnalyzer()
        schema_inference = analyzer.analyze_csv_content(csv_content, file_name)
        
        # Test schema engine
        schema_engine = CSVSchemaEngine()
        enhanced_schema = schema_engine.suggest_schema(schema_inference)
        
        # Generate summary
        summary = schema_engine.generate_schema_summary(enhanced_schema)
        
        logger.info(f"📊 Analysis Results for {file_name}:")
        logger.info(f"   Primary Entity: {summary['primary_entity']}")
        logger.info(f"   Confidence: {summary['confidence']}")
        logger.info(f"   Columns: {summary['total_columns']}")
        logger.info(f"   Identifiers: {summary['identifier_columns']}")
        logger.info(f"   Relationships: {summary['relationship_columns']}")
        logger.info(f"   Suggested Relations: {len(summary['suggested_relationships'])}")
        
        return {
            "success": True,
            "schema_inference": enhanced_schema,
            "summary": summary
        }
        
    except Exception as e:
        logger.error(f"❌ CSV analysis failed for {file_name}: {e}")
        return {"success": False, "error": str(e)}


def test_csv_processing(csv_content: str, file_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
    """Test complete CSV processing pipeline."""
    logger.info(f"\n🚀 Testing CSV Processing for {file_id}")
    
    try:
        result = process_document(
            file_id=file_id,
            file_content=csv_content,
            neo4j_uri=config["neo4j_uri"],
            neo4j_user=config["neo4j_user"],
            neo4j_password=config["neo4j_password"],
            llm_provider=config["llm_provider"],
            llm_model=config["llm_model"],
            llm_api_key=config["llm_api_key"],
            content_type="csv"  # Force CSV processing
        )
        
        if result["success"]:
            logger.info(f"✅ CSV Processing successful for {file_id}")
            logger.info(f"   Content Type: {result['content_type']}")
            logger.info(f"   Chunks Created: {result['chunks_created']}")
            logger.info(f"   Entities: {result['total_entities']}")
            logger.info(f"   Relationships: {result['total_relationships']}")
            
            if 'schema_inference' in result:
                schema = result['schema_inference']
                logger.info(f"   Primary Entity: {schema['primary_entity_type']}")
                logger.info(f"   Schema Confidence: {schema['confidence']:.2f}")
        else:
            logger.error(f"❌ CSV Processing failed for {file_id}: {result.get('error', 'Unknown error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ CSV processing failed for {file_id}: {e}")
        return {"success": False, "error": str(e)}


def validate_graph_creation(config: Dict[str, Any]) -> Dict[str, Any]:
    """Validate that the graph was created correctly."""
    logger.info("\n🔍 Validating Graph Creation")
    
    try:
        neo4j_conn = Neo4jConnection(
            uri=config["neo4j_uri"],
            user=config["neo4j_user"],
            password=config["neo4j_password"]
        )
        neo4j_client = Neo4jClient(neo4j_conn)
        
        # Count nodes by type
        node_counts = {}
        entity_types = ["Person", "Project", "Task", "Department", "DataSource", "Chunk"]
        
        for entity_type in entity_types:
            query = f"MATCH (n:{entity_type}) RETURN count(n) as count"
            result = neo4j_client.execute_query(query)
            count = result[0]["count"] if result else 0
            node_counts[entity_type] = count
        
        # Count relationships
        rel_query = "MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count"
        rel_results = neo4j_client.execute_query(rel_query)
        relationship_counts = {r["rel_type"]: r["count"] for r in rel_results}
        
        # Sample some entities
        sample_query = "MATCH (p:Person) RETURN p.name as name LIMIT 5"
        sample_people = neo4j_client.execute_query(sample_query)
        
        neo4j_client.close()
        
        logger.info("📈 Graph Validation Results:")
        logger.info("   Node Counts:")
        for entity_type, count in node_counts.items():
            if count > 0:
                logger.info(f"     {entity_type}: {count}")
        
        logger.info("   Relationship Counts:")
        for rel_type, count in relationship_counts.items():
            logger.info(f"     {rel_type}: {count}")
        
        if sample_people:
            logger.info("   Sample People:")
            for person in sample_people:
                logger.info(f"     - {person['name']}")
        
        return {
            "success": True,
            "node_counts": node_counts,
            "relationship_counts": relationship_counts,
            "total_nodes": sum(node_counts.values()),
            "total_relationships": sum(relationship_counts.values())
        }
        
    except Exception as e:
        logger.error(f"❌ Graph validation failed: {e}")
        return {"success": False, "error": str(e)}


def main():
    """Main test function."""
    logger.info("🚀 Starting CSV Processing Test and Demo")
    
    try:
        # Load configuration
        config = load_config()
        logger.info("✅ Configuration loaded")
        
        # Clear existing data
        if not clear_existing_data(config):
            logger.warning("⚠️ Failed to clear existing data, continuing anyway...")
        
        # Test data sets
        test_datasets = [
            ("employee_data.csv", SAMPLE_EMPLOYEE_CSV),
            ("project_data.csv", SAMPLE_PROJECT_CSV),
            ("task_data.csv", SAMPLE_TASK_CSV)
        ]
        
        analysis_results = []
        processing_results = []
        
        # Test each dataset
        for file_id, csv_content in test_datasets:
            # Test analysis
            analysis_result = test_csv_analysis(csv_content, file_id)
            analysis_results.append((file_id, analysis_result))
            
            # Test processing
            processing_result = test_csv_processing(csv_content, file_id, config)
            processing_results.append((file_id, processing_result))
        
        # Validate graph creation
        validation_result = validate_graph_creation(config)
        
        # Summary
        logger.info("\n📋 Test Summary")
        logger.info("=" * 50)
        
        successful_analyses = sum(1 for _, result in analysis_results if result["success"])
        successful_processing = sum(1 for _, result in processing_results if result["success"])
        
        logger.info(f"CSV Analysis: {successful_analyses}/{len(analysis_results)} successful")
        logger.info(f"CSV Processing: {successful_processing}/{len(processing_results)} successful")
        logger.info(f"Graph Validation: {'✅ Passed' if validation_result['success'] else '❌ Failed'}")
        
        if validation_result["success"]:
            logger.info(f"Total Nodes Created: {validation_result['total_nodes']}")
            logger.info(f"Total Relationships Created: {validation_result['total_relationships']}")
        
        # Overall success
        overall_success = (
            successful_analyses == len(analysis_results) and
            successful_processing == len(processing_results) and
            validation_result["success"]
        )
        
        if overall_success:
            logger.info("\n🎉 All tests passed! CSV processing is working correctly.")
        else:
            logger.error("\n❌ Some tests failed. Please check the logs above.")
        
        return overall_success
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
