"""
CSV Processing Constants for Enterprise KG

This module defines constants, patterns, and configurations specifically
for processing CSV files in the enterprise knowledge graph system.
"""

from enum import Enum
from typing import Dict, List, Set, Tuple, Any
import re


class CSVColumnType(Enum):
    """
    Classification of CSV column types for entity extraction.
    """
    ENTITY_IDENTIFIER = "entity_identifier"  # Primary keys, IDs, emails, unique identifiers
    ENTITY_ATTRIBUTE = "entity_attribute"    # Properties, descriptions, dates, numbers
    FOREIGN_KEY = "foreign_key"              # References to other entities
    RELATIONSHIP_INDICATOR = "relationship_indicator"  # Columns that indicate relationships
    METADATA = "metadata"                    # Created dates, modified dates, etc.
    UNKNOWN = "unknown"                      # Unclassified columns


class CSVEntityInferenceStrategy(Enum):
    """
    Strategies for inferring entity types from CSV data.
    """
    HEADER_BASED = "header_based"            # Infer from column headers
    VALUE_BASED = "value_based"              # Infer from data values
    PATTERN_BASED = "pattern_based"          # Infer from data patterns
    HYBRID = "hybrid"                        # Combine multiple strategies
    LLM_ASSISTED = "llm_assisted"           # Use LLM for ambiguous cases


# Column header patterns that indicate entity identifiers
ENTITY_IDENTIFIER_PATTERNS = {
    "Person": [
        r".*user.*id.*", r".*employee.*id.*", r".*person.*id.*", r".*staff.*id.*",
        r".*email.*", r".*username.*", r".*login.*", r".*user.*name.*",
        r".*employee.*number.*", r".*badge.*", r".*emp.*id.*"
    ],
    "Project": [
        r".*project.*id.*", r".*project.*key.*", r".*project.*code.*",
        r".*initiative.*id.*", r".*program.*id.*", r".*campaign.*id.*"
    ],
    "Company": [
        r".*company.*id.*", r".*org.*id.*", r".*organization.*id.*",
        r".*client.*id.*", r".*vendor.*id.*", r".*partner.*id.*"
    ],
    "Department": [
        r".*dept.*id.*", r".*department.*id.*", r".*division.*id.*",
        r".*team.*id.*", r".*unit.*id.*", r".*group.*id.*"
    ],
    "Task": [
        r".*task.*id.*", r".*ticket.*id.*", r".*issue.*id.*",
        r".*story.*id.*", r".*epic.*id.*", r".*bug.*id.*"
    ]
}

# Column header patterns that indicate entity attributes
ENTITY_ATTRIBUTE_PATTERNS = {
    "name": [r".*name.*", r".*title.*", r".*label.*", r".*description.*"],
    "status": [r".*status.*", r".*state.*", r".*phase.*", r".*stage.*"],
    "date": [r".*date.*", r".*time.*", r".*created.*", r".*updated.*", r".*modified.*"],
    "priority": [r".*priority.*", r".*urgency.*", r".*importance.*"],
    "category": [r".*category.*", r".*type.*", r".*kind.*", r".*class.*"],
    "location": [r".*location.*", r".*address.*", r".*city.*", r".*country.*"],
    "contact": [r".*phone.*", r".*mobile.*", r".*contact.*", r".*email.*"],
    "financial": [r".*cost.*", r".*budget.*", r".*price.*", r".*amount.*", r".*salary.*"]
}

# Patterns that indicate foreign key relationships
FOREIGN_KEY_PATTERNS = [
    r".*_id$", r".*_key$", r".*_ref$", r".*_reference$",
    r".*manager.*", r".*lead.*", r".*owner.*", r".*assignee.*",
    r".*parent.*", r".*child.*", r".*belongs.*to.*"
]

# Value patterns for entity type inference
VALUE_PATTERNS = {
    "email": r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
    "uuid": r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
    "numeric_id": r"^\d+$",
    "alphanumeric_id": r"^[A-Z0-9-_]+$",
    "date": r"^\d{4}-\d{2}-\d{2}",
    "datetime": r"^\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}",
    "phone": r"^[\+]?[1-9][\d\s\-\(\)]{7,15}$",
    "url": r"^https?://[^\s]+$"
}

# Relationship mapping based on column names
CSV_RELATIONSHIP_MAPPINGS = {
    "hierarchical": {
        "manager": "MANAGES",
        "manager_id": "MANAGED_BY",
        "lead": "LEADS", 
        "lead_id": "LED_BY",
        "supervisor": "SUPERVISES",
        "supervisor_id": "SUPERVISED_BY",
        "parent": "PARENT_OF",
        "parent_id": "CHILD_OF",
        "reports_to": "REPORTS_TO",
        "team_lead": "TEAM_LEAD_OF"
    },
    "assignment": {
        "assignee": "ASSIGNED_TO",
        "assignee_id": "ASSIGNED_TO",
        "owner": "OWNS",
        "owner_id": "OWNED_BY",
        "responsible": "RESPONSIBLE_FOR",
        "responsible_id": "RESPONSIBLE_FOR"
    },
    "project": {
        "project": "BELONGS_TO_PROJECT",
        "project_id": "BELONGS_TO_PROJECT",
        "initiative": "PART_OF_INITIATIVE",
        "initiative_id": "PART_OF_INITIATIVE",
        "program": "PART_OF_PROGRAM",
        "program_id": "PART_OF_PROGRAM"
    },
    "organizational": {
        "department": "BELONGS_TO_DEPARTMENT",
        "department_id": "BELONGS_TO_DEPARTMENT",
        "team": "MEMBER_OF_TEAM",
        "team_id": "MEMBER_OF_TEAM",
        "company": "WORKS_FOR",
        "company_id": "WORKS_FOR"
    }
}

# Heuristics for determining entity vs attribute columns
ENTITY_HEURISTICS = {
    "uniqueness_threshold": 0.8,  # If >80% values are unique, likely an entity identifier
    "cardinality_threshold": 0.1,  # If <10% unique values, likely an attribute
    "min_length_for_description": 20,  # Text longer than this is likely description
    "max_categories_for_enum": 20,  # If fewer unique values, treat as categorical
    "foreign_key_match_threshold": 0.3  # If >30% values match another column, likely FK
}

# CSV-specific entity types (extending base entity types)
CSV_SPECIFIC_ENTITIES = {
    "DataRecord": "DataRecord",  # Generic row/record entity
    "DataField": "DataField",    # Individual field/column value
    "DataSource": "DataSource",  # The CSV file itself
    "DataSchema": "DataSchema",  # Schema definition
    "DataRelation": "DataRelation"  # Inferred relationships
}

# Common CSV column name variations
COLUMN_NAME_VARIATIONS = {
    "id": ["id", "ID", "Id", "identifier", "key", "primary_key", "pk"],
    "name": ["name", "Name", "NAME", "title", "label", "display_name"],
    "email": ["email", "Email", "EMAIL", "e_mail", "email_address"],
    "phone": ["phone", "Phone", "PHONE", "telephone", "mobile", "contact"],
    "date": ["date", "Date", "DATE", "created", "updated", "modified"],
    "status": ["status", "Status", "STATUS", "state", "condition", "phase"],
    "type": ["type", "Type", "TYPE", "category", "kind", "class"],
    "description": ["description", "Description", "DESC", "details", "notes", "comments"]
}

# Schema suggestion templates
CSV_SCHEMA_TEMPLATES = {
    "employee_data": {
        "primary_entity": "Person",
        "identifier_columns": ["employee_id", "email", "username"],
        "attribute_columns": ["name", "department", "position", "salary", "hire_date"],
        "relationship_columns": ["manager_id", "team_id", "department_id"],
        "suggested_relationships": [
            ("Person", "REPORTS_TO", "Person"),
            ("Person", "MEMBER_OF", "Team"),
            ("Person", "WORKS_IN", "Department")
        ]
    },
    "project_data": {
        "primary_entity": "Project",
        "identifier_columns": ["project_id", "project_code"],
        "attribute_columns": ["name", "description", "status", "start_date", "end_date", "budget"],
        "relationship_columns": ["manager_id", "client_id", "department_id"],
        "suggested_relationships": [
            ("Project", "MANAGED_BY", "Person"),
            ("Project", "CLIENT_OF", "Company"),
            ("Project", "BELONGS_TO", "Department")
        ]
    },
    "task_data": {
        "primary_entity": "Task",
        "identifier_columns": ["task_id", "ticket_id"],
        "attribute_columns": ["title", "description", "status", "priority", "created_date"],
        "relationship_columns": ["assignee_id", "project_id", "reporter_id"],
        "suggested_relationships": [
            ("Task", "ASSIGNED_TO", "Person"),
            ("Task", "BELONGS_TO", "Project"),
            ("Task", "REPORTED_BY", "Person")
        ]
    }
}


def get_csv_entity_patterns() -> Dict[str, List[str]]:
    """
    Get all CSV entity identifier patterns.
    
    Returns:
        Dictionary mapping entity types to their identifier patterns
    """
    return ENTITY_IDENTIFIER_PATTERNS


def get_csv_relationship_mappings() -> Dict[str, Dict[str, str]]:
    """
    Get all CSV relationship mappings.
    
    Returns:
        Dictionary of relationship categories and their mappings
    """
    return CSV_RELATIONSHIP_MAPPINGS


def get_csv_value_patterns() -> Dict[str, str]:
    """
    Get regex patterns for value type detection.
    
    Returns:
        Dictionary mapping value types to regex patterns
    """
    return VALUE_PATTERNS


def get_csv_heuristics() -> Dict[str, float]:
    """
    Get heuristic thresholds for CSV analysis.
    
    Returns:
        Dictionary of heuristic parameters
    """
    return ENTITY_HEURISTICS


def get_csv_schema_templates() -> Dict[str, Dict[str, Any]]:
    """
    Get predefined schema templates for common CSV types.

    Returns:
        Dictionary of schema templates
    """
    return CSV_SCHEMA_TEMPLATES


def normalize_column_name(column_name: str) -> str:
    """
    Normalize column name for pattern matching.

    Args:
        column_name: Original column name

    Returns:
        Normalized column name
    """
    # Convert to lowercase and replace common separators
    normalized = column_name.lower()
    normalized = re.sub(r'[_\-\s]+', '_', normalized)
    normalized = normalized.strip('_')
    return normalized


def match_column_pattern(column_name: str, patterns: List[str]) -> bool:
    """
    Check if column name matches any of the given patterns.

    Args:
        column_name: Column name to check
        patterns: List of regex patterns

    Returns:
        True if column matches any pattern
    """
    normalized_name = normalize_column_name(column_name)

    for pattern in patterns:
        if re.search(pattern, normalized_name, re.IGNORECASE):
            return True

    return False


def infer_entity_type_from_column(column_name: str) -> str:
    """
    Infer entity type from column name using patterns.

    Args:
        column_name: Column name to analyze

    Returns:
        Inferred entity type or "Unknown"
    """
    for entity_type, patterns in ENTITY_IDENTIFIER_PATTERNS.items():
        if match_column_pattern(column_name, patterns):
            return entity_type

    return "Unknown"


def get_relationship_from_column(column_name: str) -> str:
    """
    Get relationship type from column name.

    Args:
        column_name: Column name to analyze

    Returns:
        Relationship type or None
    """
    normalized_name = normalize_column_name(column_name)

    for category, mappings in CSV_RELATIONSHIP_MAPPINGS.items():
        for col_pattern, relationship in mappings.items():
            if col_pattern in normalized_name:
                return relationship

    return None
