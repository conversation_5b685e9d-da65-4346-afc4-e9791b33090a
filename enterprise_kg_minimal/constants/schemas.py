"""
Data schema definitions for Enterprise KG

This module defines the data structures used throughout the enterprise knowledge graph system.
These schemas are used by the LLM extraction process and data storage components.
"""

import dataclasses
from typing import List, Optional, Dict, Tuple
from datetime import datetime


@dataclasses.dataclass
class DocumentSummary:
    """
    Summary information extracted from a document.

    This schema is used by the LLM to extract high-level information
    about documents before detailed entity/relationship extraction.
    """
    title: str
    summary: str
    document_type: Optional[str] = None
    key_topics: Optional[List[str]] = None
    language: Optional[str] = None
    confidence_score: Optional[float] = None

    # Sentiment analysis fields for feedback documents
    overall_sentiment: Optional[str] = None  # "positive", "negative", "neutral", "mixed"
    sentiment_score: Optional[float] = None  # -1.0 to 1.0
    sentiment_confidence: Optional[float] = None  # 0.0 to 1.0


@dataclasses.dataclass
class EntityRelationship:
    """
    Represents a relationship between two entities in the knowledge graph.

    This is the core schema for entity-relationship extraction.
    The LLM will extract relationships in this format.

    Examples:
    - EntityRelationship(subject="<PERSON>", predicate="involved_in", object="AI Project")
    - EntityRelationship(subject="Marketing Team", predicate="reports_to", object="<PERSON>")
    """
    subject: str
    predicate: str
    object: str

    # Entity types for proper Neo4j labeling
    subject_type: Optional[str] = None
    object_type: Optional[str] = None

    # Optional metadata
    confidence_score: Optional[float] = None
    context: Optional[str] = None
    source_sentence: Optional[str] = None

    # Optional organizational context (for integration with existing graphs)
    source_file_id: Optional[str] = None        # Link to existing file node
    org_context: Optional[Dict[str, str]] = None # Organizational hierarchy context
    extraction_timestamp: Optional[datetime] = None

    def __post_init__(self):
        """Validate the relationship after initialization."""
        if not self.subject or not self.subject.strip():
            raise ValueError("Subject cannot be empty")
        if not self.predicate or not self.predicate.strip():
            raise ValueError("Predicate cannot be empty")
        if not self.object or not self.object.strip():
            raise ValueError("Object cannot be empty")

        # Clean up whitespace
        self.subject = self.subject.strip()
        self.predicate = self.predicate.strip()
        self.object = self.object.strip()


@dataclasses.dataclass
class Entity:
    """
    Represents an individual entity in the knowledge graph.

    This schema can be used for entity-only extraction or
    as a component of relationship extraction.
    """
    name: str
    entity_type: str

    # Optional metadata
    description: Optional[str] = None
    aliases: Optional[List[str]] = None
    confidence_score: Optional[float] = None
    properties: Optional[dict] = None

    def __post_init__(self):
        """Validate the entity after initialization."""
        if not self.name or not self.name.strip():
            raise ValueError("Entity name cannot be empty")
        if not self.entity_type or not self.entity_type.strip():
            raise ValueError("Entity type cannot be empty")

        # Clean up whitespace
        self.name = self.name.strip()
        self.entity_type = self.entity_type.strip()


@dataclasses.dataclass
class ExtractedData:
    """
    Complete extraction result from a document.

    This schema combines document summary, entities, and relationships
    for comprehensive document analysis.
    """
    document_summary: DocumentSummary
    entities: List[Entity]
    relationships: List[EntityRelationship]

    # Metadata
    extraction_timestamp: Optional[datetime] = None
    extraction_model: Optional[str] = None
    processing_time_seconds: Optional[float] = None


@dataclasses.dataclass
class FeedbackClassification:
    """
    Sentiment and classification information for customer feedback.

    This schema is used to store detailed sentiment analysis results
    for customer feedback documents.
    """
    feedback_id: str
    customer_name: Optional[str] = None
    product_service: Optional[str] = None

    # Sentiment classification
    overall_sentiment: str = "neutral"  # "positive", "negative", "neutral", "mixed"
    sentiment_score: float = 0.0  # -1.0 (very negative) to 1.0 (very positive)
    sentiment_confidence: float = 0.0  # 0.0 to 1.0

    # Detailed sentiment aspects
    product_sentiment: Optional[str] = None
    service_sentiment: Optional[str] = None
    support_sentiment: Optional[str] = None

    # Classification categories
    feedback_type: Optional[str] = None  # "complaint", "compliment", "suggestion", "question"
    priority_level: Optional[str] = None  # "low", "medium", "high", "critical"

    # Key insights
    key_issues: Optional[List[str]] = None
    key_positives: Optional[List[str]] = None
    action_required: bool = False

    # Metadata
    classification_timestamp: Optional[datetime] = None
    classification_model: Optional[str] = None


@dataclasses.dataclass
class ProcessingMetadata:
    """
    Metadata about the document processing pipeline.

    This schema tracks information about how documents were processed.
    """
    document_id: str
    document_path: str
    file_size_bytes: int
    processing_start_time: datetime
    processing_end_time: Optional[datetime] = None

    # Processing stages
    summarization_completed: bool = False
    entity_extraction_completed: bool = False
    relationship_extraction_completed: bool = False
    vector_storage_completed: bool = False
    graph_storage_completed: bool = False

    # Error tracking
    errors: Optional[List[str]] = None
    warnings: Optional[List[str]] = None

    @property
    def processing_duration_seconds(self) -> Optional[float]:
        """Calculate processing duration if end time is available."""
        if self.processing_end_time:
            return (self.processing_end_time - self.processing_start_time).total_seconds()
        return None

    @property
    def is_completed(self) -> bool:
        """Check if all processing stages are completed."""
        return (
            self.summarization_completed and
            self.entity_extraction_completed and
            self.relationship_extraction_completed and
            self.vector_storage_completed and
            self.graph_storage_completed
        )


@dataclasses.dataclass
class ChunkData:
    """
    Represents a chunk of text from document splitting.

    This schema is used when documents are split into smaller chunks
    for processing or vector embedding.
    """
    chunk_id: str
    text: str
    start_position: int
    end_position: int

    # Optional metadata
    chunk_index: Optional[int] = None
    overlap_with_previous: Optional[int] = None
    overlap_with_next: Optional[int] = None
    embedding: Optional[List[float]] = None


@dataclasses.dataclass
class GraphNode:
    """
    Represents a node in the knowledge graph for storage.

    This schema is used when storing entities as nodes in Neo4j.
    """
    node_id: str
    label: str  # Entity type
    name: str

    # Properties
    properties: dict = dataclasses.field(default_factory=dict)

    # Metadata
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    source_documents: Optional[List[str]] = None


@dataclasses.dataclass
class GraphRelationship:
    """
    Represents a relationship in the knowledge graph for storage.

    This schema is used when storing relationships as edges in Neo4j.
    """
    relationship_id: str
    source_node_id: str
    target_node_id: str
    relationship_type: str

    # Properties
    properties: dict = dataclasses.field(default_factory=dict)

    # Metadata
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    source_documents: Optional[List[str]] = None
    confidence_score: Optional[float] = None


@dataclasses.dataclass
class VectorDocument:
    """
    Represents a document for vector storage in Pinecone.

    This schema is used when storing document embeddings.
    """
    document_id: str
    text: str
    embedding: List[float]

    # Metadata for filtering and retrieval
    metadata: dict = dataclasses.field(default_factory=dict)

    def __post_init__(self):
        """Validate vector document after initialization."""
        if not self.document_id:
            raise ValueError("Document ID cannot be empty")
        if not self.text:
            raise ValueError("Text cannot be empty")
        if not self.embedding:
            raise ValueError("Embedding cannot be empty")


# Type aliases for convenience
EntityList = List[Entity]
RelationshipList = List[EntityRelationship]
ChunkList = List[ChunkData]


@dataclasses.dataclass
class CSVColumnProfile:
    """
    Profile information for a CSV column.

    This schema captures the analysis results for a single CSV column,
    including type inference and statistical properties.
    """
    column_name: str
    column_index: int
    data_type: str  # inferred data type
    column_type: str  # CSVColumnType enum value

    # Statistical properties
    total_values: int
    unique_values: int
    null_values: int
    uniqueness_ratio: float

    # Sample values for analysis
    sample_values: List[str] = dataclasses.field(default_factory=list)

    # Inferred properties
    inferred_entity_type: Optional[str] = None
    inferred_relationship: Optional[str] = None
    is_identifier: bool = False
    is_foreign_key: bool = False

    # Pattern matches
    value_patterns: List[str] = dataclasses.field(default_factory=list)
    header_patterns: List[str] = dataclasses.field(default_factory=list)


@dataclasses.dataclass
class CSVSchemaInference:
    """
    Complete schema inference result for a CSV file.

    This schema represents the suggested entity-relationship structure
    for a CSV file based on column analysis.
    """
    file_name: str
    total_rows: int
    total_columns: int

    # Column profiles
    column_profiles: List[CSVColumnProfile] = dataclasses.field(default_factory=list)

    # Suggested schema
    primary_entity_type: Optional[str] = None
    identifier_columns: List[str] = dataclasses.field(default_factory=list)
    attribute_columns: List[str] = dataclasses.field(default_factory=list)
    relationship_columns: List[str] = dataclasses.field(default_factory=list)

    # Suggested relationships
    suggested_relationships: List[Tuple[str, str, str]] = dataclasses.field(default_factory=list)

    # Confidence scores
    schema_confidence: float = 0.0
    inference_strategy: str = "hybrid"

    # Metadata
    analysis_timestamp: Optional[datetime] = None


@dataclasses.dataclass
class CSVRowChunk:
    """
    Represents a chunk of CSV rows for processing.

    This schema is used when CSV data is processed in chunks,
    similar to text document chunks but for structured data.
    """
    chunk_id: str
    file_id: str
    start_row: int
    end_row: int

    # Row data
    headers: List[str]
    rows: List[Dict[str, str]]  # List of row dictionaries

    # Schema context
    schema_inference: Optional[CSVSchemaInference] = None

    # Chunk metadata
    chunk_index: int = 0
    total_chunks: int = 1
    processing_strategy: str = "row_based"


@dataclasses.dataclass
class CSVEntityExtraction:
    """
    Result of entity extraction from CSV data.

    This schema captures entities and relationships extracted
    from CSV rows using the inferred schema.
    """
    chunk_id: str
    row_index: int

    # Extracted entities from this row
    entities: List[Entity] = dataclasses.field(default_factory=list)

    # Extracted relationships from this row
    relationships: List[EntityRelationship] = dataclasses.field(default_factory=list)

    # Row context
    row_data: Dict[str, str] = dataclasses.field(default_factory=dict)

    # Extraction metadata
    extraction_method: str = "deterministic"  # or "llm_assisted"
    confidence_score: float = 1.0
