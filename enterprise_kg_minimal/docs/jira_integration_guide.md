# Jira Integration Guide for Enterprise KG

This guide explains how to integrate Jira API data with the Enterprise Knowledge Graph system to answer queries like **"What is UserA's pending task for Project A for today?"**

## Overview

The enterprise_kg_minimal system now supports Jira-related entities and relationships, enabling you to:

- ✅ Process Jira API data (issues, projects, users, comments)
- ✅ Create knowledge graphs from Jira information
- ✅ Query user tasks, project assignments, and sprint planning
- ✅ Track issue dependencies and blocking relationships
- ✅ Analyze team workload and project progress

## Supported Jira Entities

### Core Work Items
- **Issue**: General Jira issue or work item
- **Task**: Specific task or work assignment  
- **Epic**: Large work initiative containing multiple stories
- **Story**: User story or feature requirement
- **Subtask**: Subtask of a larger issue
- **Bug**: Software bug or defect

### Project Management
- **Sprint**: Time-boxed development iteration
- **Status**: Current status of an issue (Open, In Progress, Done)
- **Priority**: Priority level (High, Medium, Low)
- **Resolution**: Resolution of completed issue

### Collaboration
- **Comment**: Comment on an issue
- **Attachment**: File attachment to an issue
- **Worklog**: Time log entry for work performed

## Supported Jira Relationships

### Assignment & Ownership
- `assigned_to`: Task/Issue assigned to person
- `created_by`: Issue created by person
- `resolved_by`: Issue resolved by person

### Project Organization
- `belongs_to_project`: Issue belongs to project
- `belongs_to_sprint`: Issue belongs to sprint
- `has_status`: Issue has specific status
- `has_priority`: Issue has priority level

### Dependencies & Hierarchy
- `blocks_issue`: Issue blocks another issue
- `depends_on_issue`: Issue depends on another issue
- `parent_issue`: Epic/Story parent relationship
- `child_issue`: Subtask child relationship

### Time Management
- `due_on`: Task due on specific date
- `estimated_at`: Issue estimated at time/points

## Quick Start

### 1. Basic Jira Data Processing

```python
from enterprise_kg_minimal import process_document

# Convert your Jira API data to text format
jira_content = """
Project Alpha (PROJ-A) - AI Analytics Initiative

Team Members:
- Alice Johnson (UserA) - Senior Developer
- Bob Smith (UserB) - UI Designer

Issues:
Issue PROJ-A-123: Implement user authentication
Status: In Progress
Priority: High
Assigned to: UserA
Due Date: 2024-01-20
Sprint: Sprint 2024-01
"""

# Process into knowledge graph
result = process_document(
    file_id="jira_project_alpha",
    file_content=jira_content,
    neo4j_uri="bolt://localhost:7687",
    neo4j_user="neo4j",
    neo4j_password="password",
    content_type="project"  # Use project type for better extraction
)

print(f"Created {result['total_entities']} entities")
print(f"Created {result['total_relationships']} relationships")
```

### 2. Querying User Tasks

```python
from enterprise_kg_minimal.search import create_hybrid_search_engine, SearchStrategy

# Create search engine
search_engine = create_hybrid_search_engine(
    neo4j_uri="bolt://localhost:7687",
    neo4j_user="neo4j", 
    neo4j_password="password"
)

# Query for user's pending tasks
result = search_engine.search(
    chunk_indices=["jira_project_alpha_chunk_0"],  # From vector search
    query_text="What tasks are assigned to UserA that are pending or in progress?",
    strategy=SearchStrategy.ENTITY_CENTRIC,
    entity_types={"Task", "Issue", "Person", "Status"},
    relationship_types={"assigned_to", "has_status", "due_on"},
    max_results=20
)

print(f"Found {result.total_results} relevant items")
for entity in result.graph_context.entities:
    print(f"- {entity.name} ({entity.entity_type})")
```

## Advanced Usage

### 1. Processing Real Jira API Data

```python
import requests
from datetime import datetime

def fetch_jira_data(jira_url, username, api_token, project_key):
    """Fetch data from Jira API"""
    headers = {
        'Authorization': f'Basic {base64.b64encode(f"{username}:{api_token}".encode()).decode()}',
        'Content-Type': 'application/json'
    }
    
    # Fetch project issues
    response = requests.get(
        f"{jira_url}/rest/api/3/search",
        headers=headers,
        params={
            'jql': f'project = {project_key}',
            'fields': 'summary,status,assignee,priority,duedate,created'
        }
    )
    
    return response.json()

def format_jira_for_kg(jira_response):
    """Convert Jira API response to KG format"""
    content_parts = []
    
    for issue in jira_response['issues']:
        fields = issue['fields']
        content_parts.append(f"Issue {issue['key']}: {fields['summary']}")
        content_parts.append(f"Status: {fields['status']['name']}")
        
        if fields.get('assignee'):
            content_parts.append(f"Assigned to: {fields['assignee']['displayName']}")
        
        if fields.get('priority'):
            content_parts.append(f"Priority: {fields['priority']['name']}")
            
        if fields.get('duedate'):
            content_parts.append(f"Due Date: {fields['duedate']}")
        
        content_parts.append("")
    
    return "\n".join(content_parts)

# Usage
jira_data = fetch_jira_data("https://your-domain.atlassian.net", "<EMAIL>", "api_token", "PROJ")
kg_content = format_jira_for_kg(jira_data)
result = process_document("jira_project_data", kg_content)
```

### 2. Complex Queries

```python
# Query for blocking issues
blocking_issues = search_engine.search(
    chunk_indices=chunk_indices,
    query_text="What issues are blocking other work?",
    entity_types={"Issue", "Task"},
    relationship_types={"blocks_issue", "depends_on_issue"},
    strategy=SearchStrategy.RELATIONSHIP_CENTRIC
)

# Query for sprint planning
sprint_tasks = search_engine.search(
    chunk_indices=chunk_indices,
    query_text="What tasks are planned for the current sprint?",
    entity_types={"Task", "Issue", "Sprint", "Person"},
    relationship_types={"belongs_to_sprint", "assigned_to"},
    strategy=SearchStrategy.HIERARCHICAL
)

# Query for overdue tasks
overdue_tasks = search_engine.search(
    chunk_indices=chunk_indices,
    query_text="What tasks are overdue or due today?",
    entity_types={"Task", "Issue", "Deadline"},
    relationship_types={"due_on", "assigned_to", "has_status"}
)
```

## Sample Queries Supported

The system can now answer queries like:

1. **"What is UserA's pending task for Project A for today?"**
2. **"Which issues are blocking the current sprint?"**
3. **"Who is assigned to high-priority bugs in Project B?"**
4. **"What tasks are due this week in the AI initiative?"**
5. **"Which epics contain unresolved stories?"**
6. **"What is the status of authentication-related tasks?"**

## Integration with Vector Databases

For production use with embeddings:

```python
# 1. Store Jira content in Pinecone with metadata
import pinecone

# Initialize Pinecone
pinecone.init(api_key="your-key", environment="your-env")
index = pinecone.Index("enterprise-kg")

# Create embeddings and store
for issue in jira_issues:
    embedding = create_embedding(issue['description'])  # Your embedding function
    
    index.upsert([{
        'id': f"jira_{issue['key']}",
        'values': embedding,
        'metadata': {
            'file_id': f"jira_{issue['project']}",
            'chunk_id': f"jira_{issue['key']}_chunk_0",
            'issue_key': issue['key'],
            'assignee': issue['assignee'],
            'status': issue['status'],
            'project': issue['project']
        }
    }])

# 2. Query Pinecone first, then use KG
query_embedding = create_embedding("UserA pending tasks Project A")
pinecone_results = index.query(
    vector=query_embedding,
    top_k=10,
    include_metadata=True,
    filter={"assignee": "UserA", "project": "Project A"}
)

# Extract chunk indices for KG search
chunk_indices = [match['metadata']['chunk_id'] for match in pinecone_results['matches']]

# Use KG for detailed analysis
kg_result = search_engine.search(
    chunk_indices=chunk_indices,
    query_text="UserA pending tasks Project A today",
    entity_types={"Task", "Issue", "Person", "Status"},
    relationship_types={"assigned_to", "has_status", "due_on"}
)
```

## Best Practices

1. **Content Formatting**: Structure Jira data clearly with consistent field names
2. **Entity Consistency**: Use consistent naming (e.g., always use display names)
3. **Chunking Strategy**: Use "hybrid" chunking for mixed content types
4. **Query Specificity**: Include relevant entity and relationship filters
5. **Date Handling**: Format dates consistently (ISO format recommended)
6. **Status Mapping**: Map Jira statuses to standard categories (Open, In Progress, Done)

## Troubleshooting

### Common Issues

1. **Low Entity Extraction**: Ensure content uses clear entity names and relationships
2. **Missing Relationships**: Check that relationship keywords are present in text
3. **Query Performance**: Use appropriate entity/relationship filters to narrow search
4. **Date Queries**: Include temporal entities and relationships in filters

### Debug Tips

```python
# Check what entities were extracted
print(f"Entities found: {result['total_entities']}")
print(f"Relationships found: {result['total_relationships']}")

# Examine search results
print(f"Search relevance: {search_result.relevance_score}")
print(f"Entity types found: {[e.entity_type for e in search_result.graph_context.entities]}")
```

## Next Steps

1. **Run the Demo**: Execute `python3 enterprise_kg_minimal/examples/jira_integration_demo.py`
2. **Test Configuration**: Run `python3 enterprise_kg_minimal/test_jira_entities.py`
3. **Integrate with Your Jira**: Adapt the API integration examples
4. **Add Vector Search**: Integrate with Pinecone or similar for semantic search
5. **Customize Entities**: Add project-specific entity types as needed

The system is now ready to handle Jira data and answer complex queries about user tasks, project assignments, and team workload!
