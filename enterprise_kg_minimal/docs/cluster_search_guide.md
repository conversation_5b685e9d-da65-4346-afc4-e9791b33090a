# Coreference Cluster Search Guide

## 🎯 Overview

This guide explains how to identify and use coreference resolution clusters in your Neo4j knowledge graph for improved search capabilities.

## 🔍 Understanding Clusters

Coreference resolution creates "clusters" of entities that refer to the same real-world entity. For example:
- **Person cluster**: "<PERSON>" → "<PERSON>" → "<PERSON><PERSON> <PERSON>" → "he"
- **Company cluster**: "IBM" → "International Business Machines" → "Big Blue"
- **System cluster**: "CRM" → "Customer Relationship Management System"

## 📊 Identifying Clusters in Neo4j

### 1. Cross-Chunk Entities (High Confidence Clusters)

Entities appearing across multiple chunks are strong evidence of successful coreference resolution:

```cypher
// Find entities appearing in multiple chunks
MATCH (c:Chunk)-[:EXTRACTED_FROM]->(e:Entity)
WITH e, count(DISTINCT c) as chunk_count
WHERE chunk_count > 1
RETURN e.name as entity_name, 
       e.entity_type as entity_type,
       chunk_count
ORDER BY chunk_count DESC
LIMIT 20
```

**What this shows:**
- Entities that were successfully linked across document chunks
- Higher `chunk_count` = stronger evidence of coreference resolution
- These are your most reliable entity clusters

### 2. Entity Type Distribution

Understanding your entity types helps focus cluster analysis:

```cypher
// Get entity type distribution
MATCH (e:Entity)
RETURN DISTINCT e.entity_type as entity_type, count(e) as count
ORDER BY count DESC
```

### 3. Potential Name Clusters

Find entities with similar names that might be variations:

```cypher
// Find person entities with similar first names
MATCH (e:Entity)
WHERE e.entity_type = "Person"
WITH split(e.name, " ")[0] as first_name, collect(e.name) as names
WHERE size(names) > 1
RETURN first_name, names as potential_cluster
```

## 🔍 Search Strategies Using Clusters

### 1. Exact Entity Search

```cypher
// Find specific entity and its relationships
MATCH (e:Entity {name: "John Smith"})
OPTIONAL MATCH (e)-[r]-(related:Entity)
RETURN e, type(r) as relationship_type, related
LIMIT 20
```

### 2. Partial Name Search with Expansion

```cypher
// Search for entities containing "John"
MATCH (e:Entity)
WHERE e.name CONTAINS "John"
RETURN e.name, e.entity_type
ORDER BY e.name
```

### 3. Cross-Chunk Entity Search

```cypher
// Find entities like "John" that appear across multiple chunks
MATCH (c:Chunk)-[:EXTRACTED_FROM]->(e:Entity)
WHERE e.name CONTAINS "John"
WITH e, count(DISTINCT c) as chunk_count
WHERE chunk_count > 1
RETURN e.name, e.entity_type, chunk_count
ORDER BY chunk_count DESC
```

### 4. Relationship-Based Search

```cypher
// Find all entities that "LEADS" something
MATCH (e1:Entity)-[:LEADS]->(e2:Entity)
RETURN e1.name as leader, e2.name as leads_what, e2.entity_type
ORDER BY e1.name
```

### 5. Entity Neighborhood Search

```cypher
// Find all entities connected to "John Smith" within 2 hops
MATCH (e:Entity {name: "John Smith"})
MATCH path = (e)-[*1..2]-(connected:Entity)
WHERE connected <> e
RETURN connected.name, connected.entity_type, length(path) as distance
ORDER BY distance, connected.name
```

## 🛠️ Practical Search Implementation

### Python Search Function Example

```python
def cluster_aware_search(client, query, entity_type=None):
    """Search with cluster awareness."""
    
    # 1. Direct matches
    direct_query = """
    MATCH (e:Entity)
    WHERE e.name CONTAINS $query
    AND ($entity_type IS NULL OR e.entity_type = $entity_type)
    RETURN e.name, e.entity_type, 'direct' as match_type
    """
    
    # 2. Cross-chunk matches (higher confidence)
    cross_chunk_query = """
    MATCH (c:Chunk)-[:EXTRACTED_FROM]->(e:Entity)
    WHERE e.name CONTAINS $query
    AND ($entity_type IS NULL OR e.entity_type = $entity_type)
    WITH e, count(DISTINCT c) as chunk_count
    WHERE chunk_count > 1
    RETURN e.name, e.entity_type, 'cross_chunk' as match_type, chunk_count
    ORDER BY chunk_count DESC
    """
    
    # 3. Related entities
    related_query = """
    MATCH (e:Entity)-[r]-(related:Entity)
    WHERE e.name CONTAINS $query
    AND ($entity_type IS NULL OR related.entity_type = $entity_type)
    RETURN related.name, related.entity_type, 'related' as match_type, type(r) as via_relationship
    """
    
    # Execute and combine results
    params = {"query": query, "entity_type": entity_type}
    
    direct_results = client.execute_query(direct_query, params)
    cross_chunk_results = client.execute_query(cross_chunk_query, params)
    related_results = client.execute_query(related_query, params)
    
    return {
        'direct_matches': list(direct_results),
        'cross_chunk_matches': list(cross_chunk_results),
        'related_matches': list(related_results)
    }
```

## 🎯 Search Use Cases

### 1. Person Search

```cypher
// Find all mentions of people named "John"
MATCH (e:Entity)
WHERE e.entity_type = "Person" AND e.name CONTAINS "John"
RETURN e.name
ORDER BY e.name

// Find who John works with
MATCH (john:Entity)-[r]-(colleague:Entity)
WHERE john.name CONTAINS "John" AND john.entity_type = "Person"
AND colleague.entity_type = "Person"
RETURN john.name, type(r), colleague.name
```

### 2. Project Search

```cypher
// Find all project-related entities
MATCH (e:Entity)
WHERE e.entity_type IN ["Project", "Initiative"] 
   OR e.name CONTAINS "Project"
RETURN e.name, e.entity_type
ORDER BY e.name

// Find project leaders
MATCH (person:Entity)-[:LEADS]->(project:Entity)
WHERE project.entity_type = "Project"
RETURN person.name, project.name
```

### 3. Technology Search

```cypher
// Find technology entities and their relationships
MATCH (tech:Entity)
WHERE tech.entity_type IN ["Technology", "System", "Tool", "Platform"]
OPTIONAL MATCH (tech)-[r]-(related:Entity)
RETURN tech.name, collect(DISTINCT type(r)) as relationship_types, 
       collect(DISTINCT related.entity_type) as connected_to
```

## 🚀 Advanced Search Techniques

### 1. Fuzzy Name Matching

For more sophisticated cluster detection, implement fuzzy matching:

```python
def fuzzy_entity_search(client, query, threshold=0.8):
    """Search with fuzzy name matching."""
    
    # Get all entities
    all_entities = client.execute_query("""
        MATCH (e:Entity)
        RETURN e.name, e.entity_type
    """)
    
    # Use fuzzy matching (requires fuzzywuzzy or similar)
    from fuzzywuzzy import fuzz
    
    matches = []
    for entity in all_entities:
        similarity = fuzz.ratio(query.lower(), entity['e.name'].lower()) / 100.0
        if similarity >= threshold:
            matches.append({
                'name': entity['e.name'],
                'type': entity['e.entity_type'],
                'similarity': similarity
            })
    
    return sorted(matches, key=lambda x: x['similarity'], reverse=True)
```

### 2. Semantic Search Integration

Combine with vector search for semantic similarity:

```python
def semantic_cluster_search(client, query_vector, top_k=10):
    """Combine vector search with cluster information."""
    
    # 1. Get semantically similar entities (pseudo-code)
    similar_entities = vector_search(query_vector, top_k)
    
    # 2. For each similar entity, find its cluster
    cluster_results = []
    for entity in similar_entities:
        cluster_query = """
        MATCH (c:Chunk)-[:EXTRACTED_FROM]->(e:Entity {name: $entity_name})
        WITH e, count(DISTINCT c) as chunk_count
        RETURN e.name, e.entity_type, chunk_count
        """
        cluster_info = client.execute_query(cluster_query, {"entity_name": entity.name})
        cluster_results.extend(cluster_info)
    
    return cluster_results
```

## 📈 Performance Optimization

### 1. Index Creation

Create indexes for faster search:

```cypher
// Create indexes for common search patterns
CREATE INDEX entity_name_index FOR (e:Entity) ON (e.name);
CREATE INDEX entity_type_index FOR (e:Entity) ON (e.entity_type);
CREATE INDEX chunk_id_index FOR (c:Chunk) ON (c.id);
```

### 2. Query Optimization

- Use `LIMIT` to control result size
- Filter by entity type early in queries
- Use `WITH` clauses to reduce intermediate results
- Consider using `PROFILE` to analyze query performance

## 🎉 Summary

Coreference clusters in your knowledge graph enable:

1. **Better Search Recall**: Find entities even with name variations
2. **Entity Disambiguation**: Distinguish between different entities with similar names
3. **Relationship Discovery**: Find connections through resolved entity references
4. **Context-Aware Results**: Leverage cross-chunk appearances for confidence scoring

The key is to identify cross-chunk entities as your highest-confidence clusters and use them as the foundation for more sophisticated search capabilities.
