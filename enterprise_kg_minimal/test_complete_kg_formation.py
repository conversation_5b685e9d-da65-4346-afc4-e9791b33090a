#!/usr/bin/env python3
"""
Complete Knowledge Graph Formation Test

This script tests the complete KG formation process using documents from the
enterprise_kg_minimal/documents folder, including:
- PDF text extraction
- Coreference resolution
- Entity and relationship extraction
- Neo4j graph creation
- Clean database setup (deletes existing graphs)
"""

import os
import sys
import logging
import time
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv('enterprise_kg_minimal/.env')

from enterprise_kg_minimal.core.document_processor import process_document
from enterprise_kg_minimal.storage.neo4j_client import Neo4jClient, Neo4jConnection

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def extract_text_from_pdf(pdf_path: str) -> str:
    """
    Extract text from PDF file using available PDF libraries.

    Args:
        pdf_path: Path to the PDF file

    Returns:
        Extracted text content
    """
    try:
        # Try pdfplumber first (better text extraction)
        import pdfplumber

        text_content = ""
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages, 1):
                page_text = page.extract_text()
                if page_text:
                    text_content += f"\n--- Page {page_num} ---\n"
                    text_content += page_text + "\n"

        logger.info(f"Extracted {len(text_content)} characters using pdfplumber")
        return text_content

    except ImportError:
        logger.warning("pdfplumber not available, trying PyPDF2")

        try:
            import PyPDF2

            text_content = ""
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)

                for page_num, page in enumerate(pdf_reader.pages, 1):
                    page_text = page.extract_text()
                    if page_text:
                        text_content += f"\n--- Page {page_num} ---\n"
                        text_content += page_text + "\n"

            logger.info(f"Extracted {len(text_content)} characters using PyPDF2")
            return text_content

        except ImportError:
            logger.warning("PDF libraries not available - skipping PDF files")
            logger.info("To process PDF files, install: pip install pdfplumber")
            return None

    except Exception as e:
        logger.error(f"Error extracting text from PDF: {e}")
        return None

def clear_neo4j_database(neo4j_uri: str, neo4j_user: str, neo4j_password: str):
    """
    Clear all data from Neo4j database to ensure clean test environment.
    
    Args:
        neo4j_uri: Neo4j connection URI
        neo4j_user: Neo4j username
        neo4j_password: Neo4j password
    """
    logger.info("🗑️  Clearing Neo4j database for clean test environment")
    
    try:
        connection = Neo4jConnection(uri=neo4j_uri, user=neo4j_user, password=neo4j_password)
        client = Neo4jClient(connection)
        
        # Delete all nodes and relationships
        clear_query = """
        MATCH (n)
        DETACH DELETE n
        """
        
        with client._get_driver().session() as session:
            result = session.run(clear_query)
            logger.info("✅ Successfully cleared all nodes and relationships from Neo4j")

        # Verify database is empty
        count_query = """
        MATCH (n)
        RETURN count(n) as node_count
        """

        with client._get_driver().session() as session:
            result = session.run(count_query)
            node_count = result.single()["node_count"]
            logger.info(f"📊 Database verification: {node_count} nodes remaining")
        
        client.close()
        
    except Exception as e:
        logger.error(f"❌ Error clearing Neo4j database: {e}")
        raise

def find_documents_in_folder(documents_folder: str) -> list:
    """
    Find all documents in the documents folder.
    
    Args:
        documents_folder: Path to documents folder
        
    Returns:
        List of document file paths
    """
    documents_path = Path(documents_folder)
    
    if not documents_path.exists():
        logger.error(f"Documents folder not found: {documents_folder}")
        return []
    
    # Find supported document types
    supported_extensions = ['.pdf', '.txt', '.md', '.doc', '.docx']
    documents = []
    
    for ext in supported_extensions:
        documents.extend(documents_path.glob(f"*{ext}"))
    
    logger.info(f"Found {len(documents)} documents in {documents_folder}")
    for doc in documents:
        logger.info(f"  - {doc.name} ({doc.suffix})")
    
    return documents

def process_document_with_analysis(file_path: Path, neo4j_config: dict) -> dict:
    """
    Process a single document and analyze the results.
    
    Args:
        file_path: Path to the document file
        neo4j_config: Neo4j connection configuration
        
    Returns:
        Processing results with analysis
    """
    logger.info(f"📄 Processing document: {file_path.name}")
    
    # Extract text based on file type
    if file_path.suffix.lower() == '.pdf':
        logger.info("🔍 Extracting text from PDF...")
        file_content = extract_text_from_pdf(str(file_path))
        if file_content is None:
            logger.warning("⚠️  PDF processing failed - skipping this file")
            return {"error": "PDF processing not available"}
    elif file_path.suffix.lower() in ['.txt', '.md']:
        logger.info("📖 Reading text file...")
        with open(file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
    else:
        logger.warning(f"⚠️  Unsupported file type: {file_path.suffix}")
        return {"error": f"Unsupported file type: {file_path.suffix}"}

    if not file_content or not file_content.strip():
        logger.warning("⚠️  No content extracted from document")
        return {"error": "No content extracted"}
    
    logger.info(f"📊 Extracted {len(file_content)} characters")
    logger.info(f"📝 Content preview: {file_content[:200]}...")
    
    # Process with coreference resolution enabled
    start_time = time.time()
    
    try:
        result = process_document(
            file_id=f"doc_{file_path.stem}",
            file_content=file_content,
            neo4j_uri=neo4j_config['uri'],
            neo4j_user=neo4j_config['user'],
            neo4j_password=neo4j_config['password'],
            llm_provider=os.getenv('LLM_PROVIDER', 'openai'),
            llm_model=os.getenv('LLM_MODEL', 'gpt-4o'),
            chunking_strategy="hybrid",
            chunk_size=1000,
            chunk_overlap=200,
            enable_coreference_resolution=True  # Enable coreference resolution
        )
        
        processing_time = time.time() - start_time
        
        if result.get('success'):
            logger.info("✅ Document processing completed successfully")
            logger.info(f"⏱️  Processing time: {processing_time:.2f} seconds")
            logger.info(f"📊 Results summary:")
            logger.info(f"   - Content type: {result.get('content_type', 'unknown')}")
            logger.info(f"   - Chunks created: {result.get('chunks_created', 0)}")
            logger.info(f"   - Entities extracted: {result.get('total_entities', 0)}")
            logger.info(f"   - Relationships created: {result.get('total_relationships', 0)}")
            logger.info(f"   - Coreference resolution: {result.get('coreference_resolution_enabled', False)}")
            
            # Show coreference statistics if available
            coref_stats = result.get('coreference_statistics')
            if coref_stats:
                logger.info(f"🔗 Coreference resolution statistics:")
                logger.info(f"   - Total entities: {coref_stats.get('total_entities', 0)}")
                logger.info(f"   - Unique entities: {coref_stats.get('unique_entities', 0)}")
                logger.info(f"   - Resolution rate: {coref_stats.get('resolution_rate', 0):.1%}")
            
            result['processing_time'] = processing_time
            result['file_name'] = file_path.name
            result['file_size'] = len(file_content)
            
        else:
            logger.error(f"❌ Document processing failed: {result.get('error', 'Unknown error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Error processing document: {e}")
        return {
            "success": False,
            "error": str(e),
            "file_name": file_path.name,
            "processing_time": time.time() - start_time
        }

def analyze_neo4j_graph(neo4j_config: dict) -> dict:
    """
    Analyze the created Neo4j graph to verify KG formation.
    
    Args:
        neo4j_config: Neo4j connection configuration
        
    Returns:
        Graph analysis results
    """
    logger.info("📊 Analyzing created knowledge graph in Neo4j")
    
    try:
        connection = Neo4jConnection(uri=neo4j_config['uri'], user=neo4j_config['user'], password=neo4j_config['password'])
        client = Neo4jClient(connection)
        
        analysis_queries = {
            "total_nodes": "MATCH (n) RETURN count(n) as count",
            "total_relationships": "MATCH ()-[r]->() RETURN count(r) as count",
            "node_types": """
                MATCH (n) 
                RETURN labels(n)[0] as node_type, count(n) as count 
                ORDER BY count DESC
            """,
            "relationship_types": """
                MATCH ()-[r]->() 
                RETURN type(r) as rel_type, count(r) as count 
                ORDER BY count DESC
            """,
            "entity_types": """
                MATCH (e:Entity) 
                RETURN e.entity_type as entity_type, count(e) as count 
                ORDER BY count DESC
            """,
            "file_structure": """
                MATCH (f:File)-[:CONTAINS]->(c:Chunk)
                RETURN f.id as file_id, count(c) as chunk_count
            """,
            "sample_entities": """
                MATCH (e:Entity)
                RETURN e.name, e.entity_type, e.description
                LIMIT 10
            """
        }
        
        analysis_results = {}
        
        with client._get_driver().session() as session:
            for query_name, query in analysis_queries.items():
                try:
                    result = session.run(query)
                    if query_name in ["total_nodes", "total_relationships"]:
                        analysis_results[query_name] = result.single()["count"]
                    else:
                        analysis_results[query_name] = [dict(record) for record in result]
                except Exception as e:
                    logger.warning(f"Query {query_name} failed: {e}")
                    analysis_results[query_name] = []
        
        client.close()
        
        # Log analysis results
        logger.info(f"🎯 Graph Analysis Results:")
        logger.info(f"   - Total nodes: {analysis_results.get('total_nodes', 0)}")
        logger.info(f"   - Total relationships: {analysis_results.get('total_relationships', 0)}")
        
        logger.info(f"📋 Node types:")
        for node_type in analysis_results.get('node_types', [])[:5]:
            logger.info(f"   - {node_type['node_type']}: {node_type['count']}")
        
        logger.info(f"🔗 Relationship types:")
        for rel_type in analysis_results.get('relationship_types', [])[:5]:
            logger.info(f"   - {rel_type['rel_type']}: {rel_type['count']}")
        
        logger.info(f"👥 Entity types:")
        for entity_type in analysis_results.get('entity_types', [])[:5]:
            logger.info(f"   - {entity_type['entity_type']}: {entity_type['count']}")
        
        return analysis_results
        
    except Exception as e:
        logger.error(f"❌ Error analyzing Neo4j graph: {e}")
        return {"error": str(e)}

def generate_test_report(processing_results: list, graph_analysis: dict) -> str:
    """
    Generate a comprehensive test report.
    
    Args:
        processing_results: List of document processing results
        graph_analysis: Neo4j graph analysis results
        
    Returns:
        Formatted test report
    """
    report = f"""
# Complete Knowledge Graph Formation Test Report

**Test Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**System:** enterprise_kg_minimal with Coreference Resolution

## Test Summary

- **Documents Processed:** {len(processing_results)}
- **Successful Processes:** {len([r for r in processing_results if r.get('success')])}
- **Failed Processes:** {len([r for r in processing_results if not r.get('success')])}

## Document Processing Results

"""
    
    for result in processing_results:
        if result.get('success'):
            report += f"""
### ✅ {result.get('file_name', 'Unknown')}
- **Content Type:** {result.get('content_type', 'unknown')}
- **File Size:** {result.get('file_size', 0):,} characters
- **Processing Time:** {result.get('processing_time', 0):.2f} seconds
- **Chunks Created:** {result.get('chunks_created', 0)}
- **Entities Extracted:** {result.get('total_entities', 0)}
- **Relationships Created:** {result.get('total_relationships', 0)}
- **Coreference Resolution:** {result.get('coreference_resolution_enabled', False)}
"""
            coref_stats = result.get('coreference_statistics')
            if coref_stats:
                report += f"""
- **Coreference Statistics:**
  - Total entities: {coref_stats.get('total_entities', 0)}
  - Unique entities: {coref_stats.get('unique_entities', 0)}
  - Resolution rate: {coref_stats.get('resolution_rate', 0):.1%}
"""
        else:
            report += f"""
### ❌ {result.get('file_name', 'Unknown')}
- **Error:** {result.get('error', 'Unknown error')}
- **Processing Time:** {result.get('processing_time', 0):.2f} seconds
"""
    
    # Add graph analysis
    if 'error' not in graph_analysis:
        report += f"""

## Knowledge Graph Analysis

### Graph Statistics
- **Total Nodes:** {graph_analysis.get('total_nodes', 0):,}
- **Total Relationships:** {graph_analysis.get('total_relationships', 0):,}

### Node Types
"""
        for node_type in graph_analysis.get('node_types', [])[:10]:
            report += f"- **{node_type['node_type']}:** {node_type['count']:,}\n"
        
        report += "\n### Relationship Types\n"
        for rel_type in graph_analysis.get('relationship_types', [])[:10]:
            report += f"- **{rel_type['rel_type']}:** {rel_type['count']:,}\n"
        
        report += "\n### Entity Types\n"
        for entity_type in graph_analysis.get('entity_types', [])[:10]:
            report += f"- **{entity_type['entity_type']}:** {entity_type['count']:,}\n"
    
    return report

def main():
    """Main test execution function."""
    print("🧪 Complete Knowledge Graph Formation Test")
    print("🔗 Testing with Coreference Resolution")
    print("=" * 70)
    
    # Configuration from environment variables
    neo4j_config = {
        'uri': os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
        'user': os.getenv('NEO4J_USER', 'neo4j'),
        'password': os.getenv('NEO4J_PASSWORD', 'password')
    }

    print(f"🔗 Using Neo4j: {neo4j_config['uri']}")
    print(f"👤 User: {neo4j_config['user']}")
    
    documents_folder = "enterprise_kg_minimal/documents"
    
    try:
        # Step 1: Clear Neo4j database
        clear_neo4j_database(neo4j_config['uri'], neo4j_config['user'], neo4j_config['password'])
        
        # Step 2: Find documents to process
        documents = find_documents_in_folder(documents_folder)
        
        if not documents:
            print("❌ No documents found to process")
            return 1
        
        # Step 3: Process each document
        processing_results = []
        
        for doc_path in documents:
            result = process_document_with_analysis(doc_path, neo4j_config)
            processing_results.append(result)
            print()  # Add spacing between documents
        
        # Step 4: Analyze the created knowledge graph
        graph_analysis = analyze_neo4j_graph(neo4j_config)
        
        # Step 5: Generate comprehensive report
        report = generate_test_report(processing_results, graph_analysis)
        
        # Save report
        report_filename = f"kg_formation_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_filename, 'w') as f:
            f.write(report)
        
        print(f"\n📄 Test report saved to: {report_filename}")
        
        # Display summary
        successful_docs = len([r for r in processing_results if r.get('success')])
        total_entities = sum(r.get('total_entities', 0) for r in processing_results if r.get('success'))
        total_relationships = sum(r.get('total_relationships', 0) for r in processing_results if r.get('success'))
        
        print(f"\n🎉 Test Completed Successfully!")
        print(f"📊 Final Summary:")
        print(f"   - Documents processed: {successful_docs}/{len(documents)}")
        print(f"   - Total entities extracted: {total_entities:,}")
        print(f"   - Total relationships created: {total_relationships:,}")
        print(f"   - Neo4j nodes: {graph_analysis.get('total_nodes', 0):,}")
        print(f"   - Neo4j relationships: {graph_analysis.get('total_relationships', 0):,}")
        
        if successful_docs > 0:
            print(f"\n✅ Knowledge graph formation successful with coreference resolution!")
        else:
            print(f"\n❌ No documents were processed successfully")
            return 1
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        logger.error(f"Test error: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
