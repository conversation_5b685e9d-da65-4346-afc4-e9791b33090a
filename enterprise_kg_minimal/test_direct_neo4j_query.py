#!/usr/bin/env python3
"""
Direct Neo4j Query Test

This script directly queries the Neo4j database to verify that Jira data
was properly stored and can be retrieved.
"""

import os
from dotenv import load_dotenv
from enterprise_kg_minimal.storage.neo4j_client import Neo4jClient, Neo4jConnection

# Load environment variables
load_dotenv()


def test_direct_neo4j_queries():
    """Test direct Neo4j queries to verify data storage."""
    print("🔍 Direct Neo4j Query Test")
    print("=" * 50)
    
    # Connect to Neo4j
    connection = Neo4jConnection(
        uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        user=os.getenv("NEO4J_USER", "neo4j"),
        password=os.getenv("NEO4J_PASSWORD", "password"),
        database=os.getenv("NEO4J_DATABASE")
    )

    neo4j_client = Neo4jClient(connection)
    
    try:
        # Test 1: Check if file node exists
        print("\n1️⃣ Checking File Node:")
        file_query = """
        MATCH (f:File {file_id: 'jira_project_alpha_comprehensive'})
        RETURN f.file_id as file_id, f.created_at as created_at
        """
        file_results = neo4j_client.execute_query(file_query)
        if file_results:
            print(f"   ✅ File node found: {file_results[0]['file_id']}")
        else:
            print("   ❌ File node not found")
        
        # Test 2: Check chunk nodes
        print("\n2️⃣ Checking Chunk Nodes:")
        chunk_query = """
        MATCH (c:Chunk)
        WHERE c.chunk_id CONTAINS 'jira_project_alpha_comprehensive'
        RETURN c.chunk_id as chunk_id, c.chunk_index as chunk_index
        ORDER BY c.chunk_index
        LIMIT 5
        """
        chunk_results = neo4j_client.execute_query(chunk_query)
        print(f"   📦 Found {len(chunk_results)} chunks:")
        for chunk in chunk_results:
            print(f"      - {chunk['chunk_id']} (index: {chunk['chunk_index']})")
        
        # Test 3: Check for UserA entity
        print("\n3️⃣ Checking UserA Entity:")
        usera_query = """
        MATCH (p:Person)
        WHERE p.name CONTAINS 'UserA' OR p.name CONTAINS 'Alice'
        RETURN p.name as name, p.entity_type as type, labels(p) as labels
        """
        usera_results = neo4j_client.execute_query(usera_query)
        print(f"   👤 Found {len(usera_results)} UserA-related entities:")
        for person in usera_results:
            print(f"      - {person['name']} ({person['type']}) {person['labels']}")
        
        # Test 4: Check for Project Alpha
        print("\n4️⃣ Checking Project Alpha:")
        project_query = """
        MATCH (p:Project)
        WHERE p.name CONTAINS 'Project Alpha' OR p.name CONTAINS 'PROJ-A'
        RETURN p.name as name, p.entity_type as type
        """
        project_results = neo4j_client.execute_query(project_query)
        print(f"   🏗️ Found {len(project_results)} Project Alpha entities:")
        for project in project_results:
            print(f"      - {project['name']} ({project['type']})")
        
        # Test 5: Check for Issues/Tasks
        print("\n5️⃣ Checking Issues/Tasks:")
        issue_query = """
        MATCH (i)
        WHERE i.name CONTAINS 'PROJ-A-123' OR i.name CONTAINS 'authentication'
        RETURN i.name as name, labels(i) as labels, i.entity_type as type
        """
        issue_results = neo4j_client.execute_query(issue_query)
        print(f"   🎫 Found {len(issue_results)} issue-related entities:")
        for issue in issue_results:
            print(f"      - {issue['name']} ({issue['type']}) {issue['labels']}")
        
        # Test 6: Check for ASSIGNED_TO relationships (uppercase)
        print("\n6️⃣ Checking ASSIGNED_TO Relationships:")
        assignment_query = """
        MATCH (person)-[r:ASSIGNED_TO]->(task)
        WHERE person.name CONTAINS 'UserA' OR person.name CONTAINS 'Alice'
        RETURN person.name as person, type(r) as relationship, task.name as task
        LIMIT 10
        """
        assignment_results = neo4j_client.execute_query(assignment_query)
        print(f"   🔗 Found {len(assignment_results)} assignment relationships:")
        for rel in assignment_results:
            print(f"      - {rel['person']} --{rel['relationship']}--> {rel['task']}")
        
        # Test 7: Check for status relationships
        print("\n7️⃣ Checking Status Relationships:")
        status_query = """
        MATCH (task)-[r:has_status]->(status)
        WHERE task.name CONTAINS 'PROJ-A' OR task.name CONTAINS 'authentication'
        RETURN task.name as task, type(r) as relationship, status.name as status
        LIMIT 10
        """
        status_results = neo4j_client.execute_query(status_query)
        print(f"   📊 Found {len(status_results)} status relationships:")
        for rel in status_results:
            print(f"      - {rel['task']} --{rel['relationship']}--> {rel['status']}")
        
        # Test 8: Complex query to find UserA's pending tasks (using correct relationship types)
        print("\n8️⃣ Complex Query - UserA's Pending Tasks:")
        complex_query = """
        MATCH (person)-[:ASSIGNED_TO]->(task)
        WHERE (person.name CONTAINS 'UserA' OR person.name CONTAINS 'Alice')
        OPTIONAL MATCH (task)-[:HAS_STATUS]->(status)
        WHERE (status.name CONTAINS 'Progress' OR status.name CONTAINS 'To Do' OR status.name CONTAINS 'pending')
        OPTIONAL MATCH (task)-[:BELONGS_TO_PROJECT|PART_OF|INVOLVED_IN]->(project)
        WHERE project.name CONTAINS 'Project Alpha' OR project.name CONTAINS 'PROJ-A'
        RETURN person.name as person, task.name as task, status.name as status, project.name as project
        """
        complex_results = neo4j_client.execute_query(complex_query)
        print(f"   🎯 Found {len(complex_results)} matching task assignments:")
        for result in complex_results:
            print(f"      - Person: {result['person']}")
            print(f"        Task: {result['task']}")
            print(f"        Status: {result['status']}")
            print(f"        Project: {result['project']}")
            print()
        
        # Test 9: All relationships involving UserA
        print("\n9️⃣ All UserA Relationships:")
        usera_rel_query = """
        MATCH (person)-[r]-(other)
        WHERE person.name CONTAINS 'UserA' OR person.name CONTAINS 'Alice'
        RETURN person.name as person, type(r) as relationship, other.name as other, labels(other) as other_labels
        LIMIT 15
        """
        usera_rel_results = neo4j_client.execute_query(usera_rel_query)
        print(f"   🔗 Found {len(usera_rel_results)} relationships involving UserA:")
        for rel in usera_rel_results:
            print(f"      - {rel['person']} --{rel['relationship']}-- {rel['other']} {rel['other_labels']}")
        
        # Summary
        print(f"\n📊 SUMMARY:")
        print(f"   • File nodes: {len(file_results)}")
        print(f"   • Chunk nodes: {len(chunk_results)}")
        print(f"   • UserA entities: {len(usera_results)}")
        print(f"   • Project entities: {len(project_results)}")
        print(f"   • Issue entities: {len(issue_results)}")
        print(f"   • Assignment relationships: {len(assignment_results)}")
        print(f"   • Status relationships: {len(status_results)}")
        print(f"   • Complex query matches: {len(complex_results)}")
        print(f"   • UserA relationships: {len(usera_rel_results)}")
        
        if complex_results:
            print(f"\n✅ SUCCESS: Found UserA's pending tasks!")
        else:
            print(f"\n⚠️ PARTIAL: Data exists but complex query needs refinement")
            
    except Exception as e:
        print(f"❌ Error querying Neo4j: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        neo4j_client.close()


if __name__ == "__main__":
    test_direct_neo4j_queries()
