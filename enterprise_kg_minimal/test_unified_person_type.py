#!/usr/bin/env python3
"""
Test script to verify unified Person entity type implementation.

This script tests:
1. Entity type constants are updated correctly
2. Person entities are created with role/persona properties
3. Search functionality works with unified Person type
4. Prompt generation includes Person-specific instructions
"""

import sys
import os

# Add the parent directory to the path so we can import the modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_entity_constants():
    """Test that entity constants are updated correctly."""
    print("🧪 Testing Entity Constants...")
    
    try:
        from enterprise_kg_minimal.constants.entities import (
            EntityType, 
            get_person_related_types,
            get_organizational_hierarchy_types,
            get_entity_properties
        )
        
        # Check that old person types are removed
        person_types = get_person_related_types()
        print(f"Person-related types: {person_types}")
        
        # Should only contain Person, Consultant, Client, Stakeholder
        expected_types = {"Person", "Consultant", "Client", "Stakeholder"}
        if person_types == expected_types:
            print("✅ Person-related types updated correctly")
        else:
            print(f"❌ Expected {expected_types}, got {person_types}")
        
        # Check Person properties
        person_props = get_entity_properties("Person")
        print(f"Person properties keys: {list(person_props.keys())}")
        
        if "supports_role_property" in person_props and "supports_persona_property" in person_props:
            print("✅ Person properties include role/persona support")
        else:
            print("❌ Person properties missing role/persona support")
            
        return True
        
    except Exception as e:
        print(f"❌ Entity constants test failed: {e}")
        return False

def test_role_persona_extraction():
    """Test role and persona extraction from context."""
    print("\n🧪 Testing Role/Persona Extraction...")
    
    try:
        from enterprise_kg_minimal.storage.neo4j_client import Neo4jClient
        from enterprise_kg_minimal.storage.neo4j_client import Neo4jConnection
        
        # Create a mock client to test the extraction method
        connection = Neo4jConnection(
            uri="bolt://localhost:7687",
            user="neo4j", 
            password="test"
        )
        client = Neo4jClient(connection)
        
        # Test role extraction
        test_cases = [
            ("John Smith", "John Smith is the CEO of the company", {"role": "CEO", "persona": "leadership"}),
            ("Sarah Johnson", "Sarah Johnson works as a manager in engineering", {"role": "Manager", "persona": "leadership"}),
            ("Mike Chen", "Mike Chen is a software developer", {"role": "Developer", "persona": "technical"}),
            ("Jennifer Walsh", "Jennifer Walsh is the CTO", {"role": "CTO", "persona": "leadership"}),
        ]
        
        for name, context, expected in test_cases:
            result = client._extract_role_persona_from_context(name, context)
            print(f"Name: {name}, Context: {context}")
            print(f"Extracted: {result}")
            
            # Check if at least one expected property is present
            if any(key in result for key in expected.keys()):
                print("✅ Role/persona extraction working")
            else:
                print(f"❌ Expected some of {expected}, got {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Role/persona extraction test failed: {e}")
        return False

def test_prompt_generation():
    """Test that prompt generation includes Person-specific instructions."""
    print("\n🧪 Testing Prompt Generation...")
    
    try:
        from enterprise_kg_minimal.core.prompt_generator import create_full_prompt_generator
        
        generator = create_full_prompt_generator()
        prompt = generator.generate_relationship_extraction_prompt("Test content")
        
        # Check for Person-specific instructions
        if "PERSON ENTITIES: Use \"Person\" type for all people" in prompt:
            print("✅ Prompt includes Person-specific instructions")
        else:
            print("❌ Prompt missing Person-specific instructions")
            
        # Check example names
        if "John Smith (Manager)" in prompt or "Person" in prompt:
            print("✅ Prompt includes unified Person examples")
        else:
            print("❌ Prompt missing unified Person examples")
            
        return True
        
    except Exception as e:
        print(f"❌ Prompt generation test failed: {e}")
        return False

def test_search_functionality():
    """Test that search functionality works with unified Person type."""
    print("\n🧪 Testing Search Functionality...")

    try:
        from enterprise_kg_minimal.search.query_analyzer import QueryAnalyzer

        analyzer = QueryAnalyzer()
        
        # Test entity detection with person-related queries
        test_queries = [
            "Who is the manager of the project?",
            "Find all employees working on this initiative",
            "Show me the CEO's involvement in the strategy"
        ]
        
        for query in test_queries:
            analysis = analyzer.analyze_query(query)
            print(f"Query: {query}")
            print(f"Detected entities: {analysis.detected_entities}")
            
            if "Person" in analysis.detected_entities:
                print("✅ Person entity detected correctly")
            else:
                print("❌ Person entity not detected")
        
        return True
        
    except Exception as e:
        print(f"❌ Search functionality test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Unified Person Type Implementation")
    print("=" * 50)
    
    tests = [
        test_entity_constants,
        test_role_persona_extraction,
        test_prompt_generation,
        test_search_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Unified Person type implementation is working correctly.")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
