#!/usr/bin/env python3
"""
CSV Integration Validation

Quick validation script to ensure all CSV components work together.
"""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """Test that all CSV-related imports work."""
    print("🔍 Testing imports...")
    
    try:
        # Core CSV components
        from enterprise_kg_minimal.core.csv_analyzer import CSVAnalyzer
        from enterprise_kg_minimal.core.csv_schema_engine import CSVSchemaEngine
        from enterprise_kg_minimal.core.csv_graph_builder import CSVGraphBuilder
        
        # Constants
        from enterprise_kg_minimal.constants.csv_processing import (
            CSVColumnType, get_csv_entity_patterns, get_csv_relationship_mappings
        )
        from enterprise_kg_minimal.constants.schemas import (
            CSVColumnProfile, CSVSchemaInference, CSVRowChunk, CSVEntityExtraction
        )
        
        # Updated components
        from enterprise_kg_minimal.core.chunking_engine import ChunkingStrategy
        from enterprise_kg_minimal.core.prompt_generator import create_csv_focused_generator
        from enterprise_kg_minimal.core.document_processor import process_document, is_csv_content
        
        print("✅ All imports successful")
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False


def test_csv_detection():
    """Test CSV content detection."""
    print("\n🔍 Testing CSV detection...")
    
    try:
        from enterprise_kg_minimal.core.document_processor import is_csv_content
        
        # Test cases
        test_cases = [
            ("test.csv", "id,name\n1,John\n2,Jane", True),
            ("data.txt", "id,name\n1,John\n2,Jane", True),  # Should detect by content
            ("report.pdf", "This is a regular text document.", False),
            ("empty.csv", "", False),
        ]
        
        for file_id, content, expected in test_cases:
            result = is_csv_content(content, file_id)
            status = "✅" if result == expected else "❌"
            print(f"   {status} {file_id}: {result} (expected {expected})")
        
        return True
        
    except Exception as e:
        print(f"❌ CSV detection test failed: {e}")
        return False


def test_csv_analysis():
    """Test CSV analysis components."""
    print("\n🔍 Testing CSV analysis...")
    
    try:
        from enterprise_kg_minimal.core.csv_analyzer import CSVAnalyzer
        from enterprise_kg_minimal.core.csv_schema_engine import CSVSchemaEngine
        
        # Sample CSV
        csv_content = """employee_id,name,department,manager_id
E001,John Smith,Engineering,E005
E002,Sarah Johnson,Marketing,E006"""
        
        # Test analyzer
        analyzer = CSVAnalyzer()
        schema_inference = analyzer.analyze_csv_content(csv_content, "test.csv")
        
        print(f"   ✅ Schema inference completed")
        print(f"      Primary entity: {schema_inference.primary_entity_type}")
        print(f"      Confidence: {schema_inference.schema_confidence:.2f}")
        print(f"      Columns: {schema_inference.total_columns}")
        
        # Test schema engine
        schema_engine = CSVSchemaEngine()
        enhanced_schema = schema_engine.suggest_schema(schema_inference)
        
        print(f"   ✅ Schema enhancement completed")
        print(f"      Enhanced confidence: {enhanced_schema.schema_confidence:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ CSV analysis test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_chunking_strategies():
    """Test CSV chunking strategies."""
    print("\n🔍 Testing CSV chunking strategies...")
    
    try:
        from enterprise_kg_minimal.core.chunking_engine import ChunkingEngine, ChunkingStrategy
        
        csv_content = """id,name,dept
1,John,Eng
2,Jane,HR
3,Bob,IT"""
        
        # Test CSV chunking strategies
        strategies = [ChunkingStrategy.CSV_ROW_BASED, ChunkingStrategy.CSV_BATCH_BASED]
        
        for strategy in strategies:
            engine = ChunkingEngine(strategy=strategy, chunk_size=2)
            chunks = engine.chunk_document(csv_content, "test.csv")
            
            print(f"   ✅ {strategy.value}: {len(chunks)} chunks created")
            
            if chunks:
                first_chunk = chunks[0]
                print(f"      First chunk has CSV metadata: {first_chunk.metadata.csv_headers is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chunking test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_prompt_generation():
    """Test CSV-specific prompt generation."""
    print("\n🔍 Testing CSV prompt generation...")
    
    try:
        from enterprise_kg_minimal.core.prompt_generator import create_csv_focused_generator
        
        generator = create_csv_focused_generator()
        
        # Test CSV extraction prompt
        headers = ["employee_id", "name", "department"]
        csv_content = "E001,John Smith,Engineering"
        
        prompt = generator.generate_csv_extraction_prompt(csv_content, headers)
        
        print(f"   ✅ CSV extraction prompt generated ({len(prompt)} characters)")
        print(f"      Contains headers: {'employee_id' in prompt}")
        print(f"      Contains instructions: {'CSV' in prompt}")
        
        # Test schema analysis prompt
        sample_data = [["E001", "John Smith", "Engineering"]]
        schema_prompt = generator.generate_csv_schema_analysis_prompt(headers, sample_data)
        
        print(f"   ✅ Schema analysis prompt generated ({len(schema_prompt)} characters)")
        
        return True
        
    except Exception as e:
        print(f"❌ Prompt generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all validation tests."""
    print("🚀 CSV Integration Validation")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_csv_detection,
        test_csv_analysis,
        test_chunking_strategies,
        test_prompt_generation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print(f"\n📊 Validation Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All validation tests passed! CSV integration is working correctly.")
        print("\nNext steps:")
        print("1. Run the demo: python csv_demo.py")
        print("2. Run comprehensive tests: python test_csv_processing.py")
        print("3. Check the CSV_PROCESSING_GUIDE.md for usage instructions")
    else:
        print("❌ Some validation tests failed. Please check the errors above.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
