#!/usr/bin/env python3
"""
Test script to verify Jira entities and relationships are properly configured.
"""

from enterprise_kg_minimal.constants.entities import (
    EntityType, 
    get_jira_related_types,
    get_entity_properties,
    get_entity_type_description
)
from enterprise_kg_minimal.constants.relationships import (
    RelationshipType,
    get_jira_relationships,
    get_relationship_description,
    get_common_entity_relationship_patterns
)


def test_jira_entities():
    """Test that Jira entities are properly configured."""
    print("🧪 Testing Jira Entity Types")
    print("=" * 40)
    
    # Test that Jira entities exist
    jira_entities = get_jira_related_types()
    print(f"✅ Found {len(jira_entities)} Jira entity types:")
    for entity_type in sorted(jira_entities):
        print(f"   • {entity_type}")
    
    # Test specific entities
    test_entities = [
        EntityType.ISSUE,
        EntityType.TASK, 
        EntityType.EPIC,
        EntityType.SPRINT,
        EntityType.STATUS
    ]
    
    print(f"\n🔍 Testing specific entity properties:")
    for entity in test_entities:
        description = get_entity_type_description(entity)
        properties = get_entity_properties(entity.value)
        
        print(f"\n   {entity.value}:")
        print(f"     Description: {description}")
        print(f"     Category: {properties.get('category', 'Unknown')}")
        print(f"     Graph Importance: {properties.get('graph_importance', 0)}")
        
        if 'typical_relationships' in properties:
            print(f"     Typical Relationships: {', '.join(properties['typical_relationships'])}")


def test_jira_relationships():
    """Test that Jira relationships are properly configured."""
    print("\n\n🧪 Testing Jira Relationship Types")
    print("=" * 40)
    
    # Test that Jira relationships exist
    jira_relationships = get_jira_relationships()
    print(f"✅ Found {len(jira_relationships)} Jira relationship types:")
    for rel_type in sorted(jira_relationships):
        print(f"   • {rel_type}")
    
    # Test specific relationships
    test_relationships = [
        RelationshipType.ASSIGNED_TO,
        RelationshipType.HAS_STATUS,
        RelationshipType.DUE_ON,
        RelationshipType.BELONGS_TO_PROJECT,
        RelationshipType.BLOCKS_ISSUE
    ]
    
    print(f"\n🔍 Testing specific relationship descriptions:")
    for relationship in test_relationships:
        description = get_relationship_description(relationship)
        print(f"   {relationship.value}: {description}")


def test_jira_patterns():
    """Test that Jira entity-relationship patterns are included."""
    print("\n\n🧪 Testing Jira Entity-Relationship Patterns")
    print("=" * 40)
    
    patterns = get_common_entity_relationship_patterns()
    jira_patterns = [p for p in patterns if any(
        jira_term in str(p).lower() 
        for jira_term in ['issue', 'task', 'assigned', 'sprint', 'status', 'epic']
    )]
    
    print(f"✅ Found {len(jira_patterns)} Jira-related patterns:")
    for pattern in jira_patterns:
        subject, relationship, obj = pattern
        print(f"   • {subject} --{relationship}--> {obj}")


def test_sample_jira_content():
    """Test processing sample Jira content."""
    print("\n\n🧪 Testing Sample Jira Content Processing")
    print("=" * 40)
    
    sample_content = """
    Project Alpha (PROJ-A) Status Report
    
    Team Members:
    - Alice Johnson (UserA) - Senior Developer
    - Bob Smith (UserB) - UI Designer
    
    Current Sprint: Sprint 2024-01
    
    Issues and Tasks:
    
    Issue PROJ-A-123: Implement user authentication system
    Type: Task
    Status: In Progress  
    Priority: High
    Assigned to: UserA
    Due Date: 2024-01-20
    Description: Create secure login system with multi-factor authentication
    
    Issue PROJ-A-124: Design user dashboard mockups
    Type: Story
    Status: To Do
    Priority: Medium
    Assigned to: UserB
    Due Date: 2024-01-22
    Description: Create wireframes and mockups for the main user dashboard
    
    Issue PROJ-A-125: Database performance optimization
    Type: Task
    Status: Done
    Priority: High
    Assigned to: UserA
    Resolved: 2024-01-17
    Description: Optimize database queries for better performance
    """
    
    print("📄 Sample Jira content:")
    print(sample_content[:300] + "..." if len(sample_content) > 300 else sample_content)
    
    # Test content type detection
    from enterprise_kg_minimal.core.document_processor import detect_content_type
    
    content_type = detect_content_type(sample_content, "jira_project_alpha")
    print(f"\n🔍 Detected content type: {content_type}")
    
    # Count potential entities and relationships
    jira_entity_keywords = ['issue', 'task', 'sprint', 'status', 'priority', 'assigned', 'project']
    jira_rel_keywords = ['assigned to', 'due date', 'status:', 'priority:', 'type:']
    
    entity_matches = sum(1 for keyword in jira_entity_keywords if keyword.lower() in sample_content.lower())
    rel_matches = sum(1 for keyword in jira_rel_keywords if keyword.lower() in sample_content.lower())
    
    print(f"📊 Potential Jira entities detected: {entity_matches}")
    print(f"🔗 Potential Jira relationships detected: {rel_matches}")


def main():
    """Run all Jira configuration tests."""
    print("🚀 Jira Integration Configuration Test")
    print("=" * 60)
    
    try:
        test_jira_entities()
        test_jira_relationships() 
        test_jira_patterns()
        test_sample_jira_content()
        
        print("\n\n✅ All tests completed successfully!")
        print("\n💡 Summary:")
        print("   • Jira entity types are properly configured")
        print("   • Jira relationship types are properly configured") 
        print("   • Entity-relationship patterns include Jira scenarios")
        print("   • Sample Jira content can be processed")
        print("\n🎯 The system is ready to handle Jira API data!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
