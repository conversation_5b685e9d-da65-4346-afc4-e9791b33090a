#!/usr/bin/env python3
"""
Quick KG Formation Test Runner

This script provides a simple way to test the complete KG formation process
with coreference resolution using the documents in the documents folder.
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are available."""
    print("🔍 Checking dependencies...")
    
    # Check Neo4j connection
    try:
        from enterprise_kg_minimal.storage.neo4j_client import Neo4jClient
        client = Neo4jClient("bolt://localhost:7687", "neo4j", "password")
        client.close()
        print("✅ Neo4j connection: OK")
    except Exception as e:
        print(f"❌ Neo4j connection failed: {e}")
        print("   Please ensure Neo4j is running on bolt://localhost:7687")
        return False
    
    # Check LLM API key (Requesty)
    requesty_key = os.getenv('REQUESTY_API_KEY')
    if not requesty_key:
        print("⚠️  REQUESTY_API_KEY not found in environment variables")
        print("   Please set your Requesty API key in the .env file")
        return False
    else:
        print("✅ Requesty API key: Found")
    
    return True

def list_available_documents():
    """List documents available for testing."""
    docs_path = Path("enterprise_kg_minimal/documents")
    
    if not docs_path.exists():
        print("❌ Documents folder not found")
        return []
    
    documents = list(docs_path.glob("*.txt")) + list(docs_path.glob("*.md")) + list(docs_path.glob("*.pdf"))
    
    print(f"\n📄 Available documents ({len(documents)}):")
    for i, doc in enumerate(documents, 1):
        size = doc.stat().st_size if doc.exists() else 0
        print(f"   {i}. {doc.name} ({size:,} bytes)")
    
    return documents

def run_test():
    """Run the complete KG formation test."""
    print("\n🚀 Running Complete KG Formation Test...")
    print("=" * 60)
    
    try:
        # Run the test script
        result = subprocess.run([
            sys.executable, 
            "enterprise_kg_minimal/test_complete_kg_formation.py"
        ], capture_output=True, text=True, timeout=600)  # 10 minute timeout
        
        if result.returncode == 0:
            print("✅ Test completed successfully!")
            print("\n📊 Test Output:")
            print(result.stdout)
        else:
            print("❌ Test failed!")
            print("\n🔍 Error Output:")
            print(result.stderr)
            if result.stdout:
                print("\n📊 Standard Output:")
                print(result.stdout)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("⏰ Test timed out after 10 minutes")
        return False
    except Exception as e:
        print(f"❌ Error running test: {e}")
        return False

def show_neo4j_queries():
    """Show useful Neo4j queries to explore the created graph."""
    print("\n🔍 Useful Neo4j Queries to Explore the Created Graph:")
    print("=" * 60)
    
    queries = [
        {
            "name": "Count all nodes and relationships",
            "query": """
MATCH (n)
OPTIONAL MATCH ()-[r]->()
RETURN 
  count(DISTINCT n) as total_nodes,
  count(DISTINCT r) as total_relationships
"""
        },
        {
            "name": "Show node types and counts",
            "query": """
MATCH (n)
RETURN labels(n)[0] as node_type, count(n) as count
ORDER BY count DESC
"""
        },
        {
            "name": "Show entity types and examples",
            "query": """
MATCH (e:Entity)
RETURN e.entity_type, count(e) as count, collect(e.name)[0..3] as examples
ORDER BY count DESC
"""
        },
        {
            "name": "Find entities with coreference resolution",
            "query": """
MATCH (e:Entity)
WHERE e.aliases IS NOT NULL OR size([x IN collect(e.name) WHERE x <> e.name]) > 0
RETURN e.name, e.entity_type, e.aliases
LIMIT 10
"""
        },
        {
            "name": "Show file structure",
            "query": """
MATCH (f:File)-[:CONTAINS]->(c:Chunk)
RETURN f.id as file_id, count(c) as chunks
ORDER BY chunks DESC
"""
        },
        {
            "name": "Find person entities and their relationships",
            "query": """
MATCH (p:Entity {entity_type: 'Person'})-[r]-(other)
RETURN p.name, type(r), other.name, other.entity_type
LIMIT 20
"""
        },
        {
            "name": "Show relationship types and counts",
            "query": """
MATCH ()-[r]->()
RETURN type(r) as relationship_type, count(r) as count
ORDER BY count DESC
"""
        }
    ]
    
    for i, query_info in enumerate(queries, 1):
        print(f"\n{i}. {query_info['name']}:")
        print("```cypher")
        print(query_info['query'].strip())
        print("```")

def main():
    """Main function."""
    print("🧪 Enterprise KG Formation Test Runner")
    print("🔗 Testing Coreference Resolution & Knowledge Graph Creation")
    print("=" * 70)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please resolve the issues above.")
        return 1
    
    # List available documents
    documents = list_available_documents()
    if not documents:
        print("\n❌ No documents found for testing.")
        return 1
    
    # Ask user if they want to proceed
    print(f"\n🎯 This test will:")
    print("1. Clear the existing Neo4j database")
    print("2. Process all documents with coreference resolution")
    print("3. Create a comprehensive knowledge graph")
    print("4. Generate a detailed test report")
    
    response = input("\n❓ Do you want to proceed? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Test cancelled.")
        return 0
    
    # Run the test
    success = run_test()
    
    if success:
        print("\n🎉 Test completed successfully!")
        print("\n📚 Next Steps:")
        print("1. Check the generated test report (kg_formation_test_report_*.md)")
        print("2. Explore the Neo4j graph using the queries below")
        print("3. Verify coreference resolution worked correctly")
        
        show_neo4j_queries()
        
        print(f"\n🌐 Neo4j Browser: http://localhost:7474")
        print(f"   Username: neo4j")
        print(f"   Password: password")
        
    else:
        print("\n❌ Test failed. Check the error messages above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
