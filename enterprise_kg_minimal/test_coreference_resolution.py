#!/usr/bin/env python3
"""
Coreference Resolution Test Script

This script tests the coreference resolution capabilities of the enterprise_kg_minimal
system by processing documents with various types of entity references.
"""

import os
import sys
import logging
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enterprise_kg_minimal.core.document_processor import process_document
from enterprise_kg_minimal.core.coreference_resolver import CoreferenceResolver

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_coreference_resolution(neo4j_uri, neo4j_user, neo4j_password, llm_provider, llm_model):
    """Test coreference resolution with various entity reference patterns."""
    print("🔗 Testing Coreference Resolution Capabilities")
    print("=" * 60)
    
    # Test documents with coreference challenges
    test_documents = [
        {
            "name": "Person Name Variations",
            "file_id": "coreference_test_person",
            "content": """
            Project Status Report
            
            <PERSON> is the project manager for the AI Initiative. <PERSON> has been working 
            on this project for 6 months. <PERSON><PERSON> reports that the project is on track.
            He mentioned that the team is making good progress. The manager expects to 
            complete the project by Q4. <PERSON> will present the results to the board.
            
            <PERSON> <PERSON> is the lead developer. She has implemented the core algorithms.
            <PERSON>'s team consists of 5 engineers. <PERSON> believes the system will be 
            ready for testing next month.
            """,
            "expected_entities": ["<PERSON> <PERSON>", "<PERSON> <PERSON>", "AI Initiative"],
            "coreference_challenges": [
                "<PERSON> <PERSON> / <PERSON> / Mr. <PERSON> / he / the manager / <PERSON>",
                "<PERSON> <PERSON> / She / <PERSON> / <PERSON>"
            ]
        },
        {
            "name": "Organization Name Variations", 
            "file_id": "coreference_test_org",
            "content": """
            Business Partnership Agreement
            
            International Business Machines Corporation (IBM) has entered into a 
            partnership with TechCorp. IBM will provide cloud services while 
            TechCorp handles implementation. The company (IBM) has extensive experience
            in enterprise solutions. They have been in business for over 100 years.
            
            TechCorp Inc. specializes in software development. The organization has
            50 employees. TechCorp's CEO is excited about this partnership.
            """,
            "expected_entities": ["International Business Machines Corporation", "TechCorp Inc."],
            "coreference_challenges": [
                "International Business Machines Corporation / IBM / The company / They",
                "TechCorp / TechCorp Inc. / The organization"
            ]
        },
        {
            "name": "System and Technology References",
            "file_id": "coreference_test_system", 
            "content": """
            Technical Documentation
            
            The Customer Relationship Management System (CRM) has been deployed successfully.
            CRM System users report improved efficiency. The system processes over 1000
            transactions daily. It has been running stable for 3 months. This platform
            integrates with our existing infrastructure.
            
            The Enterprise Resource Planning software (ERP) will be upgraded next quarter.
            ERP handles financial operations. The software requires maintenance downtime.
            """,
            "expected_entities": ["Customer Relationship Management System", "Enterprise Resource Planning software"],
            "coreference_challenges": [
                "Customer Relationship Management System / CRM / CRM System / The system / It / This platform",
                "Enterprise Resource Planning software / ERP / The software"
            ]
        },
        {
            "name": "Mixed Entity Types",
            "file_id": "coreference_test_mixed",
            "content": """
            Meeting Notes - Q3 Review
            
            Dr. Jennifer Walsh, Chief Technology Officer, presented the quarterly results.
            Dr. Walsh highlighted the success of Project Alpha. She mentioned that the 
            initiative exceeded expectations. The CTO praised the development team.
            
            Project Alpha was led by Michael Chen. The project involved implementing
            a new data analytics platform. Chen's team completed it ahead of schedule.
            He will now focus on Project Beta. The manager is confident about the next phase.
            
            The data analytics platform processes customer information efficiently.
            It replaced the legacy system. This technology will support future growth.
            """,
            "expected_entities": ["Dr. Jennifer Walsh", "Michael Chen", "Project Alpha", "data analytics platform"],
            "coreference_challenges": [
                "Dr. Jennifer Walsh / Dr. Walsh / She / The CTO",
                "Michael Chen / Chen / He / The manager", 
                "Project Alpha / the initiative / The project / it",
                "data analytics platform / It / This technology"
            ]
        }
    ]
    
    print(f"Testing {len(test_documents)} documents with coreference challenges:\n")
    
    results = []
    
    for i, doc in enumerate(test_documents, 1):
        print(f"{i}. Testing: {doc['name']}")
        print(f"   File ID: {doc['file_id']}")
        print(f"   Expected entities: {', '.join(doc['expected_entities'])}")
        print(f"   Coreference challenges:")
        for challenge in doc['coreference_challenges']:
            print(f"     - {challenge}")
        
        try:
            # Test WITHOUT coreference resolution
            print(f"   🔍 Processing WITHOUT coreference resolution...")
            result_without = process_document(
                file_id=f"{doc['file_id']}_no_coref",
                file_content=doc['content'],
                neo4j_uri=neo4j_uri,
                neo4j_user=neo4j_user,
                neo4j_password=neo4j_password,
                llm_provider=llm_provider,
                llm_model=llm_model,
                enable_coreference_resolution=False
            )

            # Test WITH coreference resolution
            print(f"   🔗 Processing WITH coreference resolution...")
            result_with = process_document(
                file_id=f"{doc['file_id']}_with_coref",
                file_content=doc['content'],
                neo4j_uri=neo4j_uri,
                neo4j_user=neo4j_user,
                neo4j_password=neo4j_password,
                llm_provider=llm_provider,
                llm_model=llm_model,
                enable_coreference_resolution=True
            )
            
            # Compare results
            entities_without = result_without.get('total_entities', 0)
            entities_with = result_with.get('total_entities', 0)
            coref_stats = result_with.get('coreference_statistics', {})
            
            print(f"   📊 Results:")
            print(f"     Without coreference: {entities_without} entities")
            print(f"     With coreference: {entities_with} entities")
            if coref_stats:
                print(f"     Resolution rate: {coref_stats.get('resolution_rate', 0):.2%}")
                print(f"     Unique entities: {coref_stats.get('unique_entities', 0)}")
            
            improvement = entities_without - entities_with if entities_without > entities_with else 0
            if improvement > 0:
                print(f"     ✅ Improvement: {improvement} duplicate entities resolved")
            else:
                print(f"     ⚠️  No improvement detected")
            
            results.append({
                'document': doc['name'],
                'entities_without_coref': entities_without,
                'entities_with_coref': entities_with,
                'improvement': improvement,
                'coref_stats': coref_stats
            })
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            results.append({
                'document': doc['name'],
                'error': str(e)
            })
        
        print()
    
    return results

def test_coreference_resolver_directly():
    """Test the CoreferenceResolver class directly."""
    print("\n🧪 Testing CoreferenceResolver Class Directly")
    print("=" * 50)
    
    resolver = CoreferenceResolver()
    
    # Test entity clusters
    test_entities = {
        'chunk_1': [
            {'name': 'John Smith', 'entity_type': 'Person'},
            {'name': 'IBM', 'entity_type': 'Company'},
            {'name': 'CRM System', 'entity_type': 'System'}
        ],
        'chunk_2': [
            {'name': 'John', 'entity_type': 'Person'},
            {'name': 'International Business Machines', 'entity_type': 'Company'},
            {'name': 'the system', 'entity_type': 'System'}
        ],
        'chunk_3': [
            {'name': 'Mr. Smith', 'entity_type': 'Person'},
            {'name': 'he', 'entity_type': 'Person'},
            {'name': 'it', 'entity_type': 'System'}
        ]
    }
    
    print("Input entities by chunk:")
    for chunk_id, entities in test_entities.items():
        print(f"  {chunk_id}: {[e['name'] for e in entities]}")
    
    # Resolve coreferences
    resolution_result = resolver.resolve_entities(test_entities)
    
    print(f"\nResolution Results:")
    print(f"  Total entities: {resolution_result['statistics']['total_entities']}")
    print(f"  Unique entities: {resolution_result['statistics']['unique_entities']}")
    print(f"  Resolution rate: {resolution_result['statistics']['resolution_rate']:.2%}")
    
    print(f"\nEntity clusters:")
    for entity_type, clusters in resolution_result['clusters_by_type'].items():
        print(f"  {entity_type}:")
        for cluster in clusters:
            canonical = cluster['canonical']
            mentions = list(cluster['mentions'])
            print(f"    '{canonical}' ← {mentions}")
    
    print(f"\nMention to canonical mapping:")
    for mention, canonical in resolution_result['mention_to_canonical'].items():
        if mention != canonical:
            print(f"  '{mention}' → '{canonical}'")

def generate_coreference_report(results):
    """Generate a comprehensive coreference resolution report."""
    print("\n📊 Coreference Resolution Test Report")
    print("=" * 60)
    
    total_tests = len([r for r in results if 'error' not in r])
    successful_improvements = len([r for r in results if r.get('improvement', 0) > 0])
    
    print(f"**Test Summary:**")
    print(f"  Total tests: {len(results)}")
    print(f"  Successful tests: {total_tests}")
    print(f"  Tests with improvements: {successful_improvements}")
    print(f"  Success rate: {successful_improvements/total_tests:.1%}" if total_tests > 0 else "  Success rate: 0%")
    
    print(f"\n**Detailed Results:**")
    for result in results:
        if 'error' in result:
            print(f"  ❌ {result['document']}: {result['error']}")
        else:
            improvement = result.get('improvement', 0)
            status = "✅" if improvement > 0 else "⚠️"
            print(f"  {status} {result['document']}: {improvement} entities resolved")
            
            coref_stats = result.get('coref_stats', {})
            if coref_stats:
                print(f"     Resolution rate: {coref_stats.get('resolution_rate', 0):.1%}")
    
    print(f"\n**Recommendations:**")
    if successful_improvements == 0:
        print("  • Coreference resolution may need tuning")
        print("  • Check if prompts are being used correctly")
        print("  • Verify LLM is following coreference instructions")
    elif successful_improvements < total_tests:
        print("  • Some documents may need specialized handling")
        print("  • Consider domain-specific coreference rules")
    else:
        print("  • Coreference resolution is working well!")
        print("  • Consider enabling by default for all documents")

def main():
    """Main test execution function."""
    print("🧪 Enterprise KG Coreference Resolution Test Suite")
    print("🔗 Testing entity linking and pronoun resolution capabilities")
    print("=" * 70)

    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()

    # Get configuration from environment variables
    neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    neo4j_user = os.getenv("NEO4J_USER", "neo4j")
    neo4j_password = os.getenv("NEO4J_PASSWORD", "password")
    llm_provider = os.getenv("LLM_PROVIDER", "requesty")
    llm_model = os.getenv("LLM_MODEL", "anthropic/claude-3-5-sonnet-20241022")

    print(f"🔧 Configuration:")
    print(f"   Neo4j URI: {neo4j_uri}")
    print(f"   LLM Provider: {llm_provider}")
    print(f"   LLM Model: {llm_model}")
    print()

    try:
        # Test coreference resolution in document processing
        results = test_coreference_resolution(neo4j_uri, neo4j_user, neo4j_password, llm_provider, llm_model)
        
        # Test coreference resolver directly
        test_coreference_resolver_directly()
        
        # Generate comprehensive report
        generate_coreference_report(results)
        
        print("\n🎉 Coreference resolution testing completed!")
        print("\n📚 Key Capabilities Tested:")
        print("1. ✅ Person name variations (John Smith / John / Mr. Smith / he)")
        print("2. ✅ Organization name variations (IBM / International Business Machines)")
        print("3. ✅ System/technology references (CRM System / the system / it)")
        print("4. ✅ Cross-chunk entity resolution")
        print("5. ✅ Pronoun resolution (he, she, it, they)")
        print("6. ✅ Title and formal name handling")
        
        print("\n🚀 Next Steps:")
        print("• Review the Neo4j graph to see resolved entities")
        print("• Adjust coreference resolution parameters if needed")
        print("• Test with your own documents containing entity variations")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {str(e)}")
        logger.error(f"Test suite error: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
