#!/usr/bin/env python3
"""
Test Entity Type Compatibility

This script tests the entity type compatibility validation to ensure
Jira-related entities can properly connect to each other.
"""

import logging
from enterprise_kg_minimal.constants.schemas import EntityRelationship
from enterprise_kg_minimal.core.graph_builder import GraphBuilder
from enterprise_kg_minimal.storage.neo4j_client import Neo4jConnection

# Configure logging to see debug messages
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def test_jira_entity_compatibility():
    """Test that Jira entities are compatible for relationships."""
    print("🧪 Testing Jira Entity Type Compatibility")
    print("=" * 50)
    
    # Create a dummy Neo4j connection (we won't actually use it)
    connection = Neo4jConnection(
        uri="bolt://localhost:7687",
        user="neo4j", 
        password="password"
    )
    
    # Create graph builder
    graph_builder = GraphBuilder(connection)
    
    # Test cases for Jira relationships
    test_relationships = [
        # Person assigned to Issue (should be compatible)
        EntityRelationship(
            subject="UserA",
            subject_type="Person",
            predicate="assigned_to",
            object="PROJ-A-123",
            object_type="Issue",
            confidence_score=0.9
        ),
        
        # Issue has Status (should be compatible)
        EntityRelationship(
            subject="PROJ-A-123",
            subject_type="Issue",
            predicate="has_status",
            object="In Progress",
            object_type="Status",
            confidence_score=0.9
        ),

        # Issue belongs to Project (should be compatible)
        EntityRelationship(
            subject="PROJ-A-123",
            subject_type="Issue",
            predicate="belongs_to_project",
            object="Project Alpha",
            object_type="Project",
            confidence_score=0.9
        ),

        # Issue belongs to Sprint (should be compatible)
        EntityRelationship(
            subject="PROJ-A-123",
            subject_type="Issue",
            predicate="belongs_to_sprint",
            object="Sprint-2024-01",
            object_type="Sprint",
            confidence_score=0.9
        ),

        # Issue has Priority (should be compatible)
        EntityRelationship(
            subject="PROJ-A-123",
            subject_type="Issue",
            predicate="has_priority",
            object="High",
            object_type="Priority",
            confidence_score=0.9
        ),

        # Issue due on Deadline (should be compatible)
        EntityRelationship(
            subject="PROJ-A-123",
            subject_type="Issue",
            predicate="due_on",
            object="2024-01-20",
            object_type="Deadline",
            confidence_score=0.9
        ),
        
        # Person works for Organization (should be compatible - same group)
        EntityRelationship(
            subject="UserA",
            subject_type="Person",
            predicate="works_for",
            object="Company Inc",
            object_type="Organization",
            confidence_score=0.9
        ),

        # Epic contains Story (should be compatible - same group)
        EntityRelationship(
            subject="PROJ-A-E1",
            subject_type="Epic",
            predicate="contains",
            object="PROJ-A-123",
            object_type="Story",
            confidence_score=0.9
        ),

        # Project involves Person (should be compatible - cross-group)
        EntityRelationship(
            subject="Project Alpha",
            subject_type="Project",
            predicate="involves",
            object="UserA",
            object_type="Person",
            confidence_score=0.9
        ),

        # Technology used in Project (should be compatible - cross-group)
        EntityRelationship(
            subject="OAuth 2.0",
            subject_type="Technology",
            predicate="used_in",
            object="Project Alpha",
            object_type="Project",
            confidence_score=0.9
        )
    ]
    
    print(f"Testing {len(test_relationships)} relationship compatibility scenarios:\n")
    
    compatible_count = 0
    for i, relationship in enumerate(test_relationships, 1):
        print(f"{i:2d}. {relationship.subject} ({relationship.subject_type}) "
              f"--{relationship.predicate}--> "
              f"{relationship.object} ({relationship.object_type})")
        
        # Test compatibility
        is_compatible = graph_builder._validate_entity_relationship_compatibility(relationship)
        
        if is_compatible:
            print(f"    ✅ COMPATIBLE")
            compatible_count += 1
        else:
            print(f"    ❌ INCOMPATIBLE")
        print()
    
    print(f"📊 RESULTS:")
    print(f"   Compatible: {compatible_count}/{len(test_relationships)}")
    print(f"   Success Rate: {compatible_count/len(test_relationships)*100:.1f}%")
    
    if compatible_count == len(test_relationships):
        print(f"   🎉 ALL TESTS PASSED!")
    else:
        print(f"   ⚠️  Some relationships failed compatibility check")
    
    return compatible_count == len(test_relationships)


def test_compatibility_groups():
    """Test the compatibility group logic directly."""
    print(f"\n🧪 Testing Compatibility Group Logic")
    print("=" * 50)
    
    # Test same-group compatibility
    same_group_tests = [
        ("Person", "Employee"),  # People group
        ("Issue", "Task"),       # Jira work items group  
        ("Technology", "System"), # Technology group
        ("Project", "Initiative"), # Business concepts group
    ]
    
    print("Same-group compatibility tests:")
    for subject_type, object_type in same_group_tests:
        test_rel = EntityRelationship(
            subject="Test Subject",
            subject_type=subject_type,
            predicate="test_relationship",
            object="Test Object",
            object_type=object_type,
            confidence_score=0.9
        )
        
        connection = Neo4jConnection(uri="bolt://localhost:7687", user="neo4j", password="password")
        graph_builder = GraphBuilder(connection)
        is_compatible = graph_builder._validate_entity_relationship_compatibility(test_rel)
        
        status = "✅" if is_compatible else "❌"
        print(f"   {status} {subject_type} <-> {object_type}")
    
    # Test cross-group compatibility
    cross_group_tests = [
        ("Person", "Issue"),     # Person assigned to Issue
        ("Issue", "Status"),     # Issue has Status
        ("Issue", "Project"),    # Issue belongs to Project
        ("Project", "Person"),   # Project involves Person
        ("Technology", "Project"), # Technology used in Project
    ]
    
    print(f"\nCross-group compatibility tests:")
    for subject_type, object_type in cross_group_tests:
        test_rel = EntityRelationship(
            subject="Test Subject",
            subject_type=subject_type,
            predicate="test_relationship",
            object="Test Object", 
            object_type=object_type,
            confidence_score=0.9
        )
        
        connection = Neo4jConnection(uri="bolt://localhost:7687", user="neo4j", password="password")
        graph_builder = GraphBuilder(connection)
        is_compatible = graph_builder._validate_entity_relationship_compatibility(test_rel)
        
        status = "✅" if is_compatible else "❌"
        print(f"   {status} {subject_type} <-> {object_type}")


def main():
    """Run all compatibility tests."""
    print("🚀 Entity Type Compatibility Test Suite")
    print("=" * 60)
    
    # Test 1: Jira-specific relationships
    jira_success = test_jira_entity_compatibility()
    
    # Test 2: Compatibility group logic
    test_compatibility_groups()
    
    print(f"\n🎯 OVERALL RESULT:")
    if jira_success:
        print("✅ Entity type compatibility is working correctly for Jira relationships!")
        print("💡 The system can now handle Jira entities without false warnings.")
    else:
        print("❌ Some compatibility issues remain - check the implementation.")


if __name__ == "__main__":
    main()
