#!/usr/bin/env python3
"""
CSV Processing Demo

A simple demonstration of how to use the CSV processing capabilities
of the enterprise_kg_minimal system.
"""

import os
import sys
import logging

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enterprise_kg_minimal import process_document

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Sample CSV data
SAMPLE_CSV = """employee_id,name,department,manager_id,position,email
E001,<PERSON>,Engineering,E005,Software Engineer,<EMAIL>
E002,<PERSON>,Marketing,E006,Marketing Specialist,<EMAIL>
E003,<PERSON>,<PERSON>,E005,Senior Developer,<EMAIL>
E004,<PERSON>,<PERSON>,E007,HR Coordinator,<EMAIL>
E005,<PERSON>,Engineering,,Engineering Manager,<EMAIL>
E006,<PERSON>,<PERSON>,,Marketing Manager,<EMAIL>
E007,<PERSON>,<PERSON><PERSON>,,<PERSON><PERSON> <PERSON>,<EMAIL>"""


def main():
    """Demonstrate CSV processing."""
    print("🚀 CSV Processing Demo")
    print("=" * 50)
    
    try:
        # Load environment variables
        try:
            from dotenv import load_dotenv
            load_dotenv()
        except ImportError:
            print("⚠️ python-dotenv not installed, using environment variables directly")
        
        # Process the CSV
        print("\n📊 Processing sample employee CSV data...")
        
        result = process_document(
            file_id="demo_employees.csv",
            file_content=SAMPLE_CSV,
            neo4j_uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            neo4j_user=os.getenv("NEO4J_USER", "neo4j"),
            neo4j_password=os.getenv("NEO4J_PASSWORD", "password"),
            llm_provider=os.getenv("LLM_PROVIDER", "requesty"),
            llm_model=os.getenv("LLM_MODEL", "anthropic/claude-3-5-sonnet-20241022"),
            llm_api_key=os.getenv("REQUESTY_API_KEY")
        )
        
        # Display results
        if result["success"]:
            print("✅ CSV processing completed successfully!")
            print(f"\n📈 Results:")
            print(f"   Content Type: {result['content_type']}")
            print(f"   Chunks Created: {result['chunks_created']}")
            print(f"   Total Entities: {result['total_entities']}")
            print(f"   Total Relationships: {result['total_relationships']}")
            
            if 'schema_inference' in result:
                schema = result['schema_inference']
                print(f"\n🔍 Schema Analysis:")
                print(f"   Primary Entity Type: {schema['primary_entity_type']}")
                print(f"   Confidence Score: {schema['confidence']:.2f}")
                print(f"   Identifier Columns: {', '.join(schema['identifier_columns'])}")
                print(f"   Relationship Columns: {', '.join(schema['relationship_columns'])}")
                
                if schema['suggested_relationships']:
                    print(f"\n🔗 Suggested Relationships:")
                    for subj, pred, obj in schema['suggested_relationships']:
                        print(f"   {subj} -> {pred} -> {obj}")
            
            if 'processing_statistics' in result:
                stats = result['processing_statistics']
                print(f"\n📊 Processing Statistics:")
                for key, value in stats.items():
                    print(f"   {key.replace('_', ' ').title()}: {value}")
        
        else:
            print(f"❌ CSV processing failed: {result.get('error', 'Unknown error')}")
            return False
        
        print(f"\n🎉 Demo completed! Check your Neo4j database to see the created graph.")
        print(f"   You can query the data using Cypher queries like:")
        print(f"   MATCH (p:Person) RETURN p.name, p.entity_type")
        print(f"   MATCH (p:Person)-[r]->(d:Department) RETURN p.name, type(r), d.name")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
