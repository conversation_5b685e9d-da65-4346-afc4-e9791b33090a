#!/usr/bin/env python3
"""
Fix Entity Duplication Issues

This script consolidates duplicate entities (same name, different types)
to create a clean, connected organizational hierarchy.
"""

import os
import sys
from dotenv import load_dotenv

# Add the parent directory to Python path to enable proper imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from enterprise_kg_minimal.storage.neo4j_client import Neo4jClient, Neo4jConnection

# Load environment variables
load_dotenv()

def fix_entity_duplication():
    """Fix entity duplication by merging duplicate entities."""
    try:
        neo4j_conn = Neo4jConnection(
            uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            user=os.getenv("NEO4J_USER", "neo4j"),
            password=os.getenv("NEO4J_PASSWORD", "password"),
            database=os.getenv("NEO4J_DATABASE")
        )
        neo4j_client = Neo4jClient(neo4j_conn)
        
        print("🔧 Fixing Entity Duplication Issues")
        print("=" * 40)
        
        # Step 1: Merge <PERSON> entities
        print("\n1. Merging <PERSON> entities...")
        merge_john_query = """
        // Find both John Smith entities
        MATCH (manager:Manager {name: 'John Smith'})
        MATCH (person:Person {name: 'John Smith'})
        
        // Get all relationships from manager
        OPTIONAL MATCH (manager)-[r1]->(target1)
        OPTIONAL MATCH (source1)-[r2]->(manager)
        
        // Transfer relationships to person node
        FOREACH (rel IN CASE WHEN r1 IS NOT NULL THEN [r1] ELSE [] END |
            MERGE (person)-[new_rel:REPORTS_TO]->(target1)
            WHERE type(r1) = 'REPORTS_TO'
        )
        
        FOREACH (rel IN CASE WHEN r1 IS NOT NULL THEN [r1] ELSE [] END |
            MERGE (person)-[new_rel:MANAGES]->(target1)
            WHERE type(r1) = 'MANAGES'
        )
        
        FOREACH (rel IN CASE WHEN r2 IS NOT NULL THEN [r2] ELSE [] END |
            MERGE (source1)-[new_rel:REPORTS_TO]->(person)
            WHERE type(r2) = 'REPORTS_TO'
        )
        
        // Add Manager label to person and set role property
        SET person:Manager
        SET person.role = 'Engineering Manager'
        
        // Delete the separate manager node
        DETACH DELETE manager
        
        RETURN count(*) as merged_count
        """
        
        try:
            result = neo4j_client.execute_query(merge_john_query)
            print(f"   ✅ John Smith entities merged")
        except Exception as e:
            print(f"   ⚠️  John Smith merge failed: {e}")
        
        # Step 2: Merge Michael Chen entities
        print("\n2. Merging Michael Chen entities...")
        merge_michael_query = """
        // Find both Michael Chen entities
        MATCH (executive:Executive {name: 'Michael Chen'})
        MATCH (person:Person {name: 'Michael Chen'})
        
        // Get all relationships from executive
        OPTIONAL MATCH (executive)-[r1]->(target1)
        OPTIONAL MATCH (source1)-[r2]->(executive)
        
        // Transfer relationships to person node
        FOREACH (rel IN CASE WHEN r1 IS NOT NULL THEN [r1] ELSE [] END |
            MERGE (person)-[new_rel:REPORTS_TO]->(target1)
            WHERE type(r1) = 'REPORTS_TO'
        )
        
        FOREACH (rel IN CASE WHEN r2 IS NOT NULL THEN [r2] ELSE [] END |
            MERGE (source1)-[new_rel:REPORTS_TO]->(person)
            WHERE type(r2) = 'REPORTS_TO'
        )
        
        // Add Executive label to person and set role property
        SET person:Executive
        SET person.role = 'Chief Technology Officer'
        
        // Delete the separate executive node
        DETACH DELETE executive
        
        RETURN count(*) as merged_count
        """
        
        try:
            result = neo4j_client.execute_query(merge_michael_query)
            print(f"   ✅ Michael Chen entities merged")
        except Exception as e:
            print(f"   ⚠️  Michael Chen merge failed: {e}")
        
        # Step 3: Merge Alex Rodriguez entities
        print("\n3. Merging Alex Rodriguez entities...")
        merge_alex_query = """
        // Find both Alex Rodriguez entities
        MATCH (employee:Employee {name: 'Alex Rodriguez'})
        MATCH (person:Person {name: 'Alex Rodriguez'})
        
        // Get all relationships from employee
        OPTIONAL MATCH (employee)-[r1]->(target1)
        OPTIONAL MATCH (source1)-[r2]->(employee)
        
        // Transfer relationships to person node
        FOREACH (rel IN CASE WHEN r1 IS NOT NULL THEN [r1] ELSE [] END |
            MERGE (person)-[new_rel:REPORTS_TO]->(target1)
            WHERE type(r1) = 'REPORTS_TO'
        )
        
        FOREACH (rel IN CASE WHEN r2 IS NOT NULL THEN [r2] ELSE [] END |
            MERGE (source1)-[new_rel:REPORTS_TO]->(person)
            WHERE type(r2) = 'REPORTS_TO'
        )
        
        // Add Employee label to person and set role property
        SET person:Employee
        SET person.role = 'Senior Software Engineer'
        
        // Delete the separate employee node
        DETACH DELETE employee
        
        RETURN count(*) as merged_count
        """
        
        try:
            result = neo4j_client.execute_query(merge_alex_query)
            print(f"   ✅ Alex Rodriguez entities merged")
        except Exception as e:
            print(f"   ⚠️  Alex Rodriguez merge failed: {e}")
        
        # Step 4: Fix any remaining relationship inconsistencies
        print("\n4. Standardizing relationship types...")
        
        # Ensure all people have consistent Person label
        standardize_query = """
        MATCH (n)
        WHERE n.name IS NOT NULL AND 
              (n:Manager OR n:Executive OR n:Employee) AND
              NOT n:Person
        SET n:Person
        RETURN count(n) as standardized_count
        """
        
        try:
            result = neo4j_client.execute_query(standardize_query)
            count = result[0]['standardized_count'] if result else 0
            print(f"   ✅ {count} entities standardized with Person label")
        except Exception as e:
            print(f"   ⚠️  Standardization failed: {e}")
        
        # Step 5: Verify the fix
        print("\n5. Verifying the fix...")
        
        # Check for remaining duplicates
        duplicate_check_query = """
        MATCH (n)
        WHERE n.name IS NOT NULL
        WITH n.name as name, collect(DISTINCT labels(n)[0]) as types
        WHERE size(types) > 1
        RETURN name, types
        """
        
        duplicate_results = neo4j_client.execute_query(duplicate_check_query)
        
        if duplicate_results:
            print("   ⚠️  Remaining duplicates found:")
            for record in duplicate_results:
                print(f"     {record['name']}: {record['types']}")
        else:
            print("   ✅ No duplicates found!")
        
        # Check REPORTS_TO relationships
        reports_check_query = """
        MATCH (a:Person)-[r:REPORTS_TO]->(b:Person)
        RETURN a.name as subordinate, b.name as manager
        ORDER BY b.name, a.name
        """
        
        reports_results = neo4j_client.execute_query(reports_check_query)
        
        print(f"\n   📊 Current REPORTS_TO hierarchy ({len(reports_results)} relationships):")
        for record in reports_results:
            print(f"     {record['subordinate']} → {record['manager']}")
        
        neo4j_client.close()
        
        print("\n✅ Entity duplication fix completed!")
        print("\nThe organizational hierarchy should now be connected.")
        print("Try running: MATCH (a)-[r:REPORTS_TO]->(b) RETURN a, r, b")
        
    except Exception as e:
        print(f"Error fixing entity duplication: {e}")

if __name__ == "__main__":
    fix_entity_duplication()
