"""
Test Suite for Advanced Reranking Functionality

This test suite validates the advanced reranking mechanisms:
1. RRF (Reciprocal Rank Fusion)
2. MMR (Maximal Marginal Relevance) 
3. Cross-encoder semantic relevance
4. Node distance graph-aware ranking

Run with: python test_advanced_reranking.py
"""

import unittest
import logging
from typing import List, Dict, Any
from datetime import datetime

# Configure logging for tests
logging.basicConfig(level=logging.WARNING)

try:
    from advanced_reranker import AdvancedReranker, ADVANCED_DEPS_AVAILABLE
    from search_schemas import EntityMatch, RelationshipMatch, SearchQuery, GraphContext, SearchStrategy
    from result_aggregator import SearchResultAggregator
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running from the search directory")
    exit(1)


class TestAdvancedReranker(unittest.TestCase):
    """Test cases for the AdvancedReranker class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.reranker = AdvancedReranker(
            enable_cross_encoder=False,  # Disable for faster tests
            rrf_k=60,
            mmr_lambda=0.7,
            max_node_distance=3
        )
        
        # Create sample entities
        self.sample_entities = [
            EntityMatch(
                name="<PERSON>",
                entity_type="Person",
                node_id="person_1",
                properties={"role": "Manager", "department": "Engineering"},
                relevance_score=0.9,
                match_reason="High relevance match",
                chunk_sources=["chunk_1", "chunk_2"],
                relationship_count=5
            ),
            EntityMatch(
                name="AI Project",
                entity_type="Project",
                node_id="project_1", 
                properties={"status": "Active", "priority": "High"},
                relevance_score=0.8,
                match_reason="Project match",
                chunk_sources=["chunk_2", "chunk_3"],
                relationship_count=3
            ),
            EntityMatch(
                name="Engineering Team",
                entity_type="Team",
                node_id="team_1",
                properties={"size": "15", "location": "San Francisco"},
                relevance_score=0.7,
                match_reason="Team match",
                chunk_sources=["chunk_1", "chunk_3"],
                relationship_count=8
            )
        ]
        
        # Create sample relationships
        self.sample_relationships = [
            RelationshipMatch(
                source_entity="John Smith",
                target_entity="AI Project",
                relationship_type="MANAGES",
                properties={"since": "2023-01-01"},
                confidence_score=0.9,
                context="John Smith manages the AI Project",
                relevance_score=0.85,
                match_reason="Management relationship",
                chunk_sources=["chunk_1", "chunk_2"]
            ),
            RelationshipMatch(
                source_entity="John Smith",
                target_entity="Engineering Team",
                relationship_type="LEADS",
                properties={"role": "Team Lead"},
                confidence_score=0.8,
                context="John Smith leads the Engineering Team",
                relevance_score=0.75,
                match_reason="Leadership relationship",
                chunk_sources=["chunk_2", "chunk_3"]
            )
        ]
        
        # Create sample query
        self.sample_query = SearchQuery(
            chunk_indices=["chunk_1", "chunk_2", "chunk_3"],
            query_text="Find project managers in engineering",
            strategy=SearchStrategy.HYBRID,
            max_results=10
        )
        
        # Create sample graph context
        self.sample_graph_context = GraphContext(
            entities=self.sample_entities,
            relationships=self.sample_relationships
        )
    
    def test_reranker_initialization(self):
        """Test reranker initialization and configuration."""
        reranker = AdvancedReranker()
        self.assertIsNotNone(reranker)
        
        stats = reranker.get_reranker_stats()
        self.assertIn("capabilities", stats)
        self.assertIn("rrf", stats["capabilities"])
        self.assertIn("mmr", stats["capabilities"])
        self.assertIn("cross_encoder", stats["capabilities"])
        self.assertIn("node_distance", stats["capabilities"])
    
    def test_rrf_ranking_combination(self):
        """Test RRF (Reciprocal Rank Fusion) functionality."""
        # Create multiple rankings
        rankings = {
            "original": {"item1": 0.9, "item2": 0.8, "item3": 0.7},
            "alternative": {"item1": 0.7, "item2": 0.9, "item3": 0.6}
        }
        
        rrf_scores = self.reranker._apply_rrf(rankings)
        
        self.assertIsInstance(rrf_scores, dict)
        self.assertEqual(len(rrf_scores), 3)
        
        # All scores should be between 0 and 1
        for score in rrf_scores.values():
            self.assertGreaterEqual(score, 0.0)
            self.assertLessEqual(score, 1.0)
    
    def test_entity_reranking(self):
        """Test entity reranking functionality."""
        # Test with basic methods only (no cross-encoder)
        ranking_methods = ["rrf", "node_distance"]
        
        ranked_entities = self.reranker.rerank_entities(
            entities=self.sample_entities.copy(),
            query=self.sample_query,
            graph_context=self.sample_graph_context,
            ranking_methods=ranking_methods
        )
        
        self.assertEqual(len(ranked_entities), len(self.sample_entities))
        self.assertIsInstance(ranked_entities, list)
        
        # Check that entities are still EntityMatch objects
        for entity in ranked_entities:
            self.assertIsInstance(entity, EntityMatch)
    
    def test_relationship_reranking(self):
        """Test relationship reranking functionality."""
        ranking_methods = ["rrf"]
        
        ranked_relationships = self.reranker.rerank_relationships(
            relationships=self.sample_relationships.copy(),
            query=self.sample_query,
            ranking_methods=ranking_methods
        )
        
        self.assertEqual(len(ranked_relationships), len(self.sample_relationships))
        self.assertIsInstance(ranked_relationships, list)
        
        # Check that relationships are still RelationshipMatch objects
        for rel in ranked_relationships:
            self.assertIsInstance(rel, RelationshipMatch)
    
    def test_mmr_diversity_promotion(self):
        """Test MMR (Maximal Marginal Relevance) diversity promotion."""
        if not ADVANCED_DEPS_AVAILABLE:
            self.skipTest("Advanced dependencies not available for MMR testing")
        
        # Create entities with similar content to test diversity
        similar_entities = [
            EntityMatch(
                name="Software Engineer A",
                entity_type="Person",
                node_id="person_a",
                properties={"role": "Software Engineer", "team": "Backend"},
                relevance_score=0.9,
                match_reason="Engineer match",
                chunk_sources=["chunk_1"]
            ),
            EntityMatch(
                name="Software Engineer B", 
                entity_type="Person",
                node_id="person_b",
                properties={"role": "Software Engineer", "team": "Backend"},
                relevance_score=0.85,
                match_reason="Engineer match",
                chunk_sources=["chunk_2"]
            ),
            EntityMatch(
                name="Product Manager",
                entity_type="Person", 
                node_id="person_c",
                properties={"role": "Product Manager", "team": "Product"},
                relevance_score=0.8,
                match_reason="Manager match",
                chunk_sources=["chunk_3"]
            )
        ]
        
        # Apply MMR
        mmr_entities = self.reranker._apply_mmr_entities(similar_entities)
        
        self.assertEqual(len(mmr_entities), len(similar_entities))
        # MMR should promote diversity, so Product Manager might rank higher
        # than the second Software Engineer despite lower initial relevance
    
    def test_node_distance_calculation(self):
        """Test node distance calculation for graph-aware ranking."""
        node_distance_scores = self.reranker._get_node_distance_entity_ranking(
            self.sample_entities,
            self.sample_graph_context
        )
        
        self.assertIsInstance(node_distance_scores, dict)
        
        # Scores should be between 0 and 1
        for score in node_distance_scores.values():
            self.assertGreaterEqual(score, 0.0)
            self.assertLessEqual(score, 1.0)
    
    def test_empty_input_handling(self):
        """Test handling of empty inputs."""
        # Test empty entity list
        empty_entities = self.reranker.rerank_entities(
            entities=[],
            query=self.sample_query,
            graph_context=self.sample_graph_context
        )
        self.assertEqual(len(empty_entities), 0)
        
        # Test empty relationship list
        empty_relationships = self.reranker.rerank_relationships(
            relationships=[],
            query=self.sample_query
        )
        self.assertEqual(len(empty_relationships), 0)
    
    def test_graceful_degradation(self):
        """Test graceful degradation when advanced features fail."""
        # Test with invalid ranking methods
        ranked_entities = self.reranker.rerank_entities(
            entities=self.sample_entities.copy(),
            query=self.sample_query,
            graph_context=self.sample_graph_context,
            ranking_methods=["invalid_method"]
        )
        
        # Should still return entities (graceful degradation)
        self.assertEqual(len(ranked_entities), len(self.sample_entities))


class TestResultAggregatorIntegration(unittest.TestCase):
    """Test integration of advanced reranking with result aggregator."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.aggregator = SearchResultAggregator(enable_advanced_reranking=False)  # Disable for faster tests
    
    def test_aggregator_initialization(self):
        """Test aggregator initialization with reranking options."""
        # Test with reranking enabled
        aggregator_with_reranking = SearchResultAggregator(enable_advanced_reranking=True)
        self.assertTrue(hasattr(aggregator_with_reranking, 'enable_advanced_reranking'))
        
        # Test with reranking disabled
        aggregator_without_reranking = SearchResultAggregator(enable_advanced_reranking=False)
        self.assertFalse(aggregator_without_reranking.enable_advanced_reranking)


def run_performance_benchmark():
    """Run a simple performance benchmark for reranking methods."""
    print("\n🚀 Running Performance Benchmark")
    print("=" * 40)
    
    # Create larger dataset for benchmarking
    entities = []
    for i in range(50):
        entities.append(EntityMatch(
            name=f"Entity_{i}",
            entity_type="TestEntity",
            node_id=f"entity_{i}",
            properties={"index": str(i)},
            relevance_score=0.9 - (i * 0.01),  # Decreasing relevance
            match_reason=f"Test entity {i}",
            chunk_sources=[f"chunk_{i}"]
        ))
    
    query = SearchQuery(
        chunk_indices=[f"chunk_{i}" for i in range(50)],
        query_text="Test query for benchmarking",
        strategy=SearchStrategy.HYBRID
    )
    
    graph_context = GraphContext(entities=entities)
    
    # Test different configurations
    configs = [
        {"name": "RRF Only", "methods": ["rrf"]},
        {"name": "RRF + Node Distance", "methods": ["rrf", "node_distance"]},
    ]
    
    if ADVANCED_DEPS_AVAILABLE:
        configs.append({"name": "RRF + MMR", "methods": ["rrf", "mmr"]})
    
    reranker = AdvancedReranker(enable_cross_encoder=False)
    
    for config in configs:
        start_time = datetime.now()
        
        ranked_entities = reranker.rerank_entities(
            entities=entities.copy(),
            query=query,
            graph_context=graph_context,
            ranking_methods=config["methods"]
        )
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds() * 1000
        
        print(f"⏱️  {config['name']}: {processing_time:.2f}ms ({len(ranked_entities)} entities)")


if __name__ == "__main__":
    print("🧪 Advanced Reranking Test Suite")
    print("=" * 40)
    
    # Check dependencies
    if ADVANCED_DEPS_AVAILABLE:
        print("✅ Advanced dependencies available")
    else:
        print("⚠️  Advanced dependencies not available (some tests will be skipped)")
    
    print()
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance benchmark
    run_performance_benchmark()
    
    print("\n🎉 Test suite completed!")
