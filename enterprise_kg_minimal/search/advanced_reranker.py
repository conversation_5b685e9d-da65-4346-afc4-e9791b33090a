"""
Advanced Reranking Module for Enterprise KG Search

This module implements sophisticated reranking techniques to improve search result
quality and relevance:

1. RRF (Reciprocal Rank Fusion) - Combines multiple ranking sources
2. MMR (Maximal Marginal Relevance) - Promotes diversity in results
3. Cross-encoder - Deep semantic relevance scoring
4. Node Distance - Graph-aware ranking based on entity relationships

These techniques work together to provide more accurate, diverse, and contextually
relevant search results.
"""

import logging
from typing import List, Dict, Any, Set
from collections import defaultdict

try:
    from sentence_transformers import CrossEncoder
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.feature_extraction.text import TfidfVectorizer
    ADVANCED_DEPS_AVAILABLE = True
except ImportError:
    ADVANCED_DEPS_AVAILABLE = False
    logging.warning("Advanced reranking dependencies not available. Install with: pip install sentence-transformers scikit-learn")

from .search_schemas import (
    EntityMatch, 
    RelationshipMatch, 
    SearchQuery,
    GraphContext
)

logger = logging.getLogger(__name__)


class AdvancedReranker:
    """
    Advanced reranking system that combines multiple ranking techniques
    for optimal search result quality and diversity.
    """
    
    def __init__(self, 
                 cross_encoder_model: str = "cross-encoder/ms-marco-MiniLM-L-6-v2",
                 enable_cross_encoder: bool = True,
                 rrf_k: int = 60,
                 mmr_lambda: float = 0.7,
                 max_node_distance: int = 3):
        """
        Initialize the advanced reranker.
        
        Args:
            cross_encoder_model: HuggingFace model name for cross-encoder
            enable_cross_encoder: Whether to use cross-encoder (requires GPU/CPU resources)
            rrf_k: RRF parameter (higher = more conservative fusion)
            mmr_lambda: MMR parameter (0.0 = max diversity, 1.0 = max relevance)
            max_node_distance: Maximum graph distance to consider for node distance ranking
        """
        self.cross_encoder_model = cross_encoder_model
        self.enable_cross_encoder = enable_cross_encoder and ADVANCED_DEPS_AVAILABLE
        self.rrf_k = rrf_k
        self.mmr_lambda = mmr_lambda
        self.max_node_distance = max_node_distance
        
        # Initialize cross-encoder if available
        self.cross_encoder = None
        if self.enable_cross_encoder:
            try:
                self.cross_encoder = CrossEncoder(cross_encoder_model)
                logger.info(f"Cross-encoder initialized: {cross_encoder_model}")
            except Exception as e:
                logger.warning(f"Failed to initialize cross-encoder: {e}")
                self.enable_cross_encoder = False
        
        # Initialize TF-IDF vectorizer for MMR
        self.tfidf_vectorizer = None
        if ADVANCED_DEPS_AVAILABLE:
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words='english',
                ngram_range=(1, 2)
            )
        
        logger.info(f"AdvancedReranker initialized - Cross-encoder: {self.enable_cross_encoder}, "
                   f"RRF_k: {rrf_k}, MMR_lambda: {mmr_lambda}")
    
    def rerank_entities(self, 
                       entities: List[EntityMatch], 
                       query: SearchQuery,
                       graph_context: GraphContext,
                       ranking_methods: List[str] = None) -> List[EntityMatch]:
        """
        Rerank entities using advanced techniques.
        
        Args:
            entities: List of entities to rerank
            query: Search query for context
            graph_context: Graph context for node distance calculations
            ranking_methods: List of methods to use ['rrf', 'mmr', 'cross_encoder', 'node_distance']
        
        Returns:
            Reranked list of entities
        """
        if not entities:
            return entities
        
        if ranking_methods is None:
            ranking_methods = ['rrf', 'mmr', 'cross_encoder', 'node_distance']
        
        logger.debug(f"Reranking {len(entities)} entities using methods: {ranking_methods}")
        
        # Get multiple rankings
        rankings = {}
        
        # Original relevance ranking
        rankings['original'] = self._get_original_entity_ranking(entities)
        
        # Cross-encoder ranking
        if 'cross_encoder' in ranking_methods and self.enable_cross_encoder and query.query_text:
            rankings['cross_encoder'] = self._get_cross_encoder_entity_ranking(entities, query.query_text)
        
        # Node distance ranking
        if 'node_distance' in ranking_methods:
            rankings['node_distance'] = self._get_node_distance_entity_ranking(entities, graph_context)
        
        # Apply RRF if multiple rankings available
        if 'rrf' in ranking_methods and len(rankings) > 1:
            rrf_scores = self._apply_rrf(rankings)
            # Update entity scores with RRF
            for i, entity in enumerate(entities):
                entity.relevance_score = rrf_scores.get(entity.name, entity.relevance_score)
        
        # Sort by updated scores
        ranked_entities = sorted(entities, key=lambda e: e.relevance_score, reverse=True)
        
        # Apply MMR for diversity
        if 'mmr' in ranking_methods and ADVANCED_DEPS_AVAILABLE:
            ranked_entities = self._apply_mmr_entities(ranked_entities)
        
        logger.debug(f"Entity reranking completed using {len(rankings)} ranking sources")
        return ranked_entities
    
    def rerank_relationships(self,
                           relationships: List[RelationshipMatch],
                           query: SearchQuery,
                           ranking_methods: List[str] = None) -> List[RelationshipMatch]:
        """
        Rerank relationships using advanced techniques.

        Args:
            relationships: List of relationships to rerank
            query: Search query for context
            ranking_methods: List of methods to use

        Returns:
            Reranked list of relationships
        """
        if not relationships:
            return relationships
        
        if ranking_methods is None:
            ranking_methods = ['rrf', 'mmr', 'cross_encoder']
        
        logger.debug(f"Reranking {len(relationships)} relationships using methods: {ranking_methods}")
        
        # Get multiple rankings
        rankings = {}
        
        # Original relevance ranking
        rankings['original'] = self._get_original_relationship_ranking(relationships)
        
        # Cross-encoder ranking
        if 'cross_encoder' in ranking_methods and self.enable_cross_encoder and query.query_text:
            rankings['cross_encoder'] = self._get_cross_encoder_relationship_ranking(relationships, query.query_text)
        
        # Apply RRF if multiple rankings available
        if 'rrf' in ranking_methods and len(rankings) > 1:
            rrf_scores = self._apply_rrf(rankings)
            # Update relationship scores with RRF
            for relationship in relationships:
                rel_key = f"{relationship.source_entity}-{relationship.relationship_type}-{relationship.target_entity}"
                relationship.relevance_score = rrf_scores.get(rel_key, relationship.relevance_score)
        
        # Sort by updated scores
        ranked_relationships = sorted(relationships, key=lambda r: r.relevance_score, reverse=True)
        
        # Apply MMR for diversity
        if 'mmr' in ranking_methods and ADVANCED_DEPS_AVAILABLE:
            ranked_relationships = self._apply_mmr_relationships(ranked_relationships)
        
        logger.debug(f"Relationship reranking completed using {len(rankings)} ranking sources")
        return ranked_relationships
    
    def _get_original_entity_ranking(self, entities: List[EntityMatch]) -> Dict[str, float]:
        """Get original relevance-based ranking for entities."""
        return {entity.name: entity.relevance_score for entity in entities}
    
    def _get_original_relationship_ranking(self, relationships: List[RelationshipMatch]) -> Dict[str, float]:
        """Get original relevance-based ranking for relationships."""
        ranking = {}
        for rel in relationships:
            key = f"{rel.source_entity}-{rel.relationship_type}-{rel.target_entity}"
            ranking[key] = rel.relevance_score
        return ranking
    
    def _get_cross_encoder_entity_ranking(self, entities: List[EntityMatch], query_text: str) -> Dict[str, float]:
        """Get cross-encoder based ranking for entities."""
        if not self.cross_encoder:
            return {}
        
        try:
            # Prepare query-entity pairs
            pairs = []
            entity_names = []
            
            for entity in entities:
                # Create context text for entity
                context_parts = [entity.name, entity.entity_type]
                if entity.properties:
                    context_parts.extend([str(v) for v in entity.properties.values() if v])
                context_text = " ".join(context_parts)
                
                pairs.append([query_text, context_text])
                entity_names.append(entity.name)
            
            # Get cross-encoder scores
            scores = self.cross_encoder.predict(pairs)
            
            # Normalize scores to 0-1 range
            if len(scores) > 1:
                min_score, max_score = min(scores), max(scores)
                if max_score > min_score:
                    scores = [(s - min_score) / (max_score - min_score) for s in scores]
            
            return dict(zip(entity_names, scores))
            
        except Exception as e:
            logger.warning(f"Cross-encoder entity ranking failed: {e}")
            return {}

    def _get_cross_encoder_relationship_ranking(self, relationships: List[RelationshipMatch], query_text: str) -> Dict[str, float]:
        """Get cross-encoder based ranking for relationships."""
        if not self.cross_encoder:
            return {}

        try:
            # Prepare query-relationship pairs
            pairs = []
            relationship_keys = []

            for rel in relationships:
                # Create context text for relationship
                context_parts = [
                    rel.source_entity,
                    rel.relationship_type,
                    rel.target_entity
                ]
                if rel.context:
                    context_parts.append(rel.context)
                context_text = " ".join(context_parts)

                pairs.append([query_text, context_text])
                rel_key = f"{rel.source_entity}-{rel.relationship_type}-{rel.target_entity}"
                relationship_keys.append(rel_key)

            # Get cross-encoder scores
            scores = self.cross_encoder.predict(pairs)

            # Normalize scores to 0-1 range
            if len(scores) > 1:
                min_score, max_score = min(scores), max(scores)
                if max_score > min_score:
                    scores = [(s - min_score) / (max_score - min_score) for s in scores]

            return dict(zip(relationship_keys, scores))

        except Exception as e:
            logger.warning(f"Cross-encoder relationship ranking failed: {e}")
            return {}

    def _get_node_distance_entity_ranking(self, entities: List[EntityMatch], graph_context: GraphContext) -> Dict[str, float]:
        """Get node distance based ranking for entities."""
        if not entities or not graph_context.relationships:
            return {}

        try:
            # Build adjacency graph from relationships
            graph = defaultdict(set)
            for rel in graph_context.relationships:
                graph[rel.source_entity].add(rel.target_entity)
                graph[rel.target_entity].add(rel.source_entity)  # Undirected

            # Calculate centrality scores (simplified PageRank-like)
            entity_names = [e.name for e in entities]
            centrality_scores = self._calculate_centrality(graph, entity_names)

            # Calculate average distances between entities
            distance_scores = self._calculate_distance_scores(graph, entity_names)

            # Combine centrality and distance scores
            combined_scores = {}
            for entity_name in entity_names:
                centrality = centrality_scores.get(entity_name, 0.0)
                distance = distance_scores.get(entity_name, 0.0)
                # Higher centrality and lower average distance = higher score
                combined_scores[entity_name] = (centrality * 0.7) + (distance * 0.3)

            return combined_scores

        except Exception as e:
            logger.warning(f"Node distance ranking failed: {e}")
            return {}

    def _calculate_centrality(self, graph: Dict[str, Set[str]], entity_names: List[str]) -> Dict[str, float]:
        """Calculate centrality scores for entities."""
        centrality = {}

        for entity in entity_names:
            # Simple degree centrality
            degree = len(graph.get(entity, set()))
            centrality[entity] = degree

        # Normalize
        if centrality:
            max_degree = max(centrality.values())
            if max_degree > 0:
                centrality = {k: v / max_degree for k, v in centrality.items()}

        return centrality

    def _calculate_distance_scores(self, graph: Dict[str, Set[str]], entity_names: List[str]) -> Dict[str, float]:
        """Calculate distance-based scores for entities."""
        distance_scores = {}

        for entity in entity_names:
            # Calculate average shortest path to other entities
            distances = []
            for other_entity in entity_names:
                if entity != other_entity:
                    distance = self._shortest_path_distance(graph, entity, other_entity)
                    if distance > 0:
                        distances.append(distance)

            if distances:
                avg_distance = sum(distances) / len(distances)
                # Convert to score (lower distance = higher score)
                distance_scores[entity] = 1.0 / (1.0 + avg_distance)
            else:
                distance_scores[entity] = 0.0

        return distance_scores

    def _shortest_path_distance(self, graph: Dict[str, Set[str]], start: str, end: str) -> int:
        """Calculate shortest path distance between two nodes."""
        if start == end:
            return 0

        if start not in graph or end not in graph:
            return self.max_node_distance + 1

        # BFS for shortest path
        queue = [(start, 0)]
        visited = {start}

        while queue:
            node, distance = queue.pop(0)

            if distance >= self.max_node_distance:
                break

            for neighbor in graph[node]:
                if neighbor == end:
                    return distance + 1

                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append((neighbor, distance + 1))

        return self.max_node_distance + 1

    def _apply_rrf(self, rankings: Dict[str, Dict[str, float]]) -> Dict[str, float]:
        """
        Apply Reciprocal Rank Fusion (RRF) to combine multiple rankings.

        Args:
            rankings: Dictionary of ranking name -> {item_key: score}

        Returns:
            Dictionary of item_key -> combined_rrf_score
        """
        if not rankings:
            return {}

        # Convert scores to ranks for each ranking
        ranked_lists = {}
        for ranking_name, scores in rankings.items():
            # Sort by score (descending) and create rank mapping
            sorted_items = sorted(scores.items(), key=lambda x: x[1], reverse=True)
            ranks = {item_key: rank + 1 for rank, (item_key, _) in enumerate(sorted_items)}
            ranked_lists[ranking_name] = ranks

        # Apply RRF formula: RRF(d) = Σ(1 / (k + rank(d)))
        rrf_scores = defaultdict(float)

        # Get all unique item keys
        all_item_keys = set()
        for ranks in ranked_lists.values():
            all_item_keys.update(ranks.keys())

        for item_key in all_item_keys:
            rrf_score = 0.0
            for ranking_name, ranks in ranked_lists.items():
                rank = ranks.get(item_key, len(ranks) + 1)  # Default to worst rank + 1
                rrf_score += 1.0 / (self.rrf_k + rank)
            rrf_scores[item_key] = rrf_score

        # Normalize RRF scores to 0-1 range
        if rrf_scores:
            max_rrf = max(rrf_scores.values())
            if max_rrf > 0:
                rrf_scores = {k: v / max_rrf for k, v in rrf_scores.items()}

        logger.debug(f"RRF applied to {len(rankings)} rankings for {len(all_item_keys)} items")
        return dict(rrf_scores)

    def _apply_mmr_entities(self, entities: List[EntityMatch]) -> List[EntityMatch]:
        """
        Apply Maximal Marginal Relevance (MMR) to promote diversity in entity results.

        Args:
            entities: List of entities sorted by relevance

        Returns:
            Reordered list of entities with improved diversity
        """
        if not ADVANCED_DEPS_AVAILABLE or len(entities) <= 2:
            return entities

        try:
            # Create text representations for entities
            entity_texts = []
            for entity in entities:
                text_parts = [entity.name, entity.entity_type]
                if entity.properties:
                    text_parts.extend([str(v) for v in entity.properties.values() if v])
                entity_texts.append(" ".join(text_parts))

            # Fit TF-IDF vectorizer and transform texts
            tfidf_matrix = self.tfidf_vectorizer.fit_transform(entity_texts)

            # Calculate similarity matrix
            similarity_matrix = cosine_similarity(tfidf_matrix)

            # Apply MMR algorithm
            selected_indices = []
            remaining_indices = list(range(len(entities)))

            # Start with the highest relevance entity
            selected_indices.append(0)
            remaining_indices.remove(0)

            # Iteratively select entities that maximize MMR score
            while remaining_indices and len(selected_indices) < len(entities):
                best_idx = None
                best_mmr_score = -1

                for idx in remaining_indices:
                    # Relevance score (normalized)
                    relevance = entities[idx].relevance_score

                    # Maximum similarity to already selected entities
                    max_similarity = 0.0
                    if selected_indices:
                        similarities = [similarity_matrix[idx][selected_idx] for selected_idx in selected_indices]
                        max_similarity = max(similarities)

                    # MMR score: λ * relevance - (1-λ) * max_similarity
                    mmr_score = self.mmr_lambda * relevance - (1 - self.mmr_lambda) * max_similarity

                    if mmr_score > best_mmr_score:
                        best_mmr_score = mmr_score
                        best_idx = idx

                if best_idx is not None:
                    selected_indices.append(best_idx)
                    remaining_indices.remove(best_idx)
                else:
                    break

            # Reorder entities based on MMR selection
            mmr_entities = [entities[idx] for idx in selected_indices]

            logger.debug(f"MMR applied to {len(entities)} entities, diversity improved")
            return mmr_entities

        except Exception as e:
            logger.warning(f"MMR for entities failed: {e}")
            return entities

    def _apply_mmr_relationships(self, relationships: List[RelationshipMatch]) -> List[RelationshipMatch]:
        """
        Apply Maximal Marginal Relevance (MMR) to promote diversity in relationship results.

        Args:
            relationships: List of relationships sorted by relevance

        Returns:
            Reordered list of relationships with improved diversity
        """
        if not ADVANCED_DEPS_AVAILABLE or len(relationships) <= 2:
            return relationships

        try:
            # Create text representations for relationships
            rel_texts = []
            for rel in relationships:
                text_parts = [rel.source_entity, rel.relationship_type, rel.target_entity]
                if rel.context:
                    text_parts.append(rel.context)
                rel_texts.append(" ".join(text_parts))

            # Fit TF-IDF vectorizer and transform texts
            tfidf_matrix = self.tfidf_vectorizer.fit_transform(rel_texts)

            # Calculate similarity matrix
            similarity_matrix = cosine_similarity(tfidf_matrix)

            # Apply MMR algorithm (similar to entities)
            selected_indices = []
            remaining_indices = list(range(len(relationships)))

            # Start with the highest relevance relationship
            selected_indices.append(0)
            remaining_indices.remove(0)

            # Iteratively select relationships that maximize MMR score
            while remaining_indices and len(selected_indices) < len(relationships):
                best_idx = None
                best_mmr_score = -1

                for idx in remaining_indices:
                    # Relevance score (normalized)
                    relevance = relationships[idx].relevance_score

                    # Maximum similarity to already selected relationships
                    max_similarity = 0.0
                    if selected_indices:
                        similarities = [similarity_matrix[idx][selected_idx] for selected_idx in selected_indices]
                        max_similarity = max(similarities)

                    # MMR score: λ * relevance - (1-λ) * max_similarity
                    mmr_score = self.mmr_lambda * relevance - (1 - self.mmr_lambda) * max_similarity

                    if mmr_score > best_mmr_score:
                        best_mmr_score = mmr_score
                        best_idx = idx

                if best_idx is not None:
                    selected_indices.append(best_idx)
                    remaining_indices.remove(best_idx)
                else:
                    break

            # Reorder relationships based on MMR selection
            mmr_relationships = [relationships[idx] for idx in selected_indices]

            logger.debug(f"MMR applied to {len(relationships)} relationships, diversity improved")
            return mmr_relationships

        except Exception as e:
            logger.warning(f"MMR for relationships failed: {e}")
            return relationships

    def get_reranker_stats(self) -> Dict[str, Any]:
        """Get statistics about the reranker configuration and capabilities."""
        return {
            "cross_encoder_enabled": self.enable_cross_encoder,
            "cross_encoder_model": self.cross_encoder_model if self.enable_cross_encoder else None,
            "rrf_k": self.rrf_k,
            "mmr_lambda": self.mmr_lambda,
            "max_node_distance": self.max_node_distance,
            "advanced_deps_available": ADVANCED_DEPS_AVAILABLE,
            "capabilities": {
                "rrf": True,
                "mmr": ADVANCED_DEPS_AVAILABLE,
                "cross_encoder": self.enable_cross_encoder,
                "node_distance": True
            }
        }
