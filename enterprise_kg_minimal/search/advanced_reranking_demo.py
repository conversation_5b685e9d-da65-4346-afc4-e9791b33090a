"""
Advanced Reranking Demo for Enterprise KG Search

This demo showcases the new advanced reranking capabilities including:
1. RRF (Reciprocal Rank Fusion) - Combines multiple ranking sources
2. MMR (Maximal Marginal Relevance) - Promotes diversity in results
3. Cross-encoder - Deep semantic relevance scoring
4. Node Distance - Graph-aware ranking based on entity relationships

Run this demo to see how advanced reranking improves search result quality.
"""

import logging
import os
from typing import List, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from hybrid_search_engine import create_hybrid_search_engine, search_with_chunk_indices
from advanced_reranker import AdvancedReranker
from search_schemas import SearchStrategy, RoutingDecision

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def demo_advanced_reranking():
    """
    Demonstrate advanced reranking capabilities with sample data.
    """
    print("🚀 Advanced Reranking Demo for Enterprise KG Search")
    print("=" * 60)
    
    # Sample chunk indices (simulating results from vector search like Pinecone)
    sample_chunk_indices = [
        "doc1_chunk_1", "doc1_chunk_2", "doc1_chunk_3",
        "doc2_chunk_1", "doc2_chunk_2", "doc3_chunk_1",
        "doc3_chunk_2", "doc4_chunk_1", "doc4_chunk_2"
    ]
    
    # Sample queries to test different reranking scenarios
    test_queries = [
        {
            "query": "What are the key performance metrics for our team?",
            "description": "Business metrics query - should benefit from cross-encoder"
        },
        {
            "query": "Who are the project managers working on AI initiatives?",
            "description": "Entity-focused query - should benefit from node distance ranking"
        },
        {
            "query": "Show me diverse information about company policies",
            "description": "Diversity-focused query - should benefit from MMR"
        }
    ]
    
    # Neo4j connection parameters
    neo4j_config = {
        "neo4j_uri": os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        "neo4j_user": os.getenv("NEO4J_USER", "neo4j"),
        "neo4j_password": os.getenv("NEO4J_PASSWORD", "password")
    }
    
    print(f"📊 Testing with {len(sample_chunk_indices)} sample chunks")
    print(f"🔍 Running {len(test_queries)} test queries")
    print()
    
    # Test different reranking configurations
    reranking_configs = [
        {
            "name": "Basic (No Advanced Reranking)",
            "enable_advanced_reranking": False,
            "description": "Standard ranking without advanced techniques"
        },
        {
            "name": "RRF + MMR Only",
            "enable_advanced_reranking": True,
            "reranking_methods": ["rrf", "mmr"],
            "description": "Reciprocal Rank Fusion + Maximal Marginal Relevance"
        },
        {
            "name": "Full Advanced Reranking",
            "enable_advanced_reranking": True,
            "reranking_methods": ["rrf", "mmr", "cross_encoder", "node_distance"],
            "description": "All advanced reranking techniques enabled"
        }
    ]
    
    for query_info in test_queries:
        query_text = query_info["query"]
        query_desc = query_info["description"]
        
        print(f"🔍 Query: {query_text}")
        print(f"📝 Description: {query_desc}")
        print("-" * 50)
        
        for config in reranking_configs:
            print(f"\n🧪 Testing: {config['name']}")
            print(f"📋 {config['description']}")
            
            try:
                # Perform search with current configuration
                result = search_with_chunk_indices(
                    chunk_indices=sample_chunk_indices,
                    query_text=query_text,
                    strategy=SearchStrategy.HYBRID,
                    max_results=10,
                    expansion_depth=2,
                    enable_intelligent_routing=True,
                    enable_advanced_reranking=config["enable_advanced_reranking"],
                    **neo4j_config
                )
                
                # Display results summary
                print(f"✅ Results: {result.total_results} items")
                print(f"⏱️  Processing time: {result.processing_time_ms:.2f}ms")
                print(f"📊 Coverage score: {result.coverage_score:.3f}")
                print(f"🔗 Coherence score: {result.coherence_score:.3f}")
                print(f"🎯 Relevance score: {result.relevance_score:.3f}")
                
                # Show top entities and relationships
                if result.graph_context.entities:
                    print(f"🏷️  Top entities: {[e.name for e in result.graph_context.entities[:3]]}")
                
                if result.graph_context.relationships:
                    print(f"🔗 Top relationships: {len(result.graph_context.relationships)} found")
                
                # Show debug info if available
                if "aggregation" in result.debug_info:
                    print(f"🔧 Aggregation: {result.debug_info['aggregation']}")
                
            except Exception as e:
                print(f"❌ Error: {e}")
                logger.error(f"Search failed for config {config['name']}: {e}")
        
        print("\n" + "=" * 60 + "\n")


def demo_standalone_reranker():
    """
    Demonstrate the standalone advanced reranker capabilities.
    """
    print("🔧 Standalone Advanced Reranker Demo")
    print("=" * 40)
    
    try:
        # Initialize the advanced reranker
        reranker = AdvancedReranker(
            enable_cross_encoder=True,
            rrf_k=60,
            mmr_lambda=0.7,
            max_node_distance=3
        )
        
        # Get reranker statistics
        stats = reranker.get_reranker_stats()
        print("📊 Reranker Configuration:")
        for key, value in stats.items():
            if isinstance(value, dict):
                print(f"  {key}:")
                for sub_key, sub_value in value.items():
                    print(f"    {sub_key}: {sub_value}")
            else:
                print(f"  {key}: {value}")
        
        print("\n✅ Advanced reranker initialized successfully!")
        
        # Show available capabilities
        capabilities = stats.get("capabilities", {})
        enabled_features = [name for name, enabled in capabilities.items() if enabled]
        print(f"🚀 Enabled features: {', '.join(enabled_features)}")
        
    except Exception as e:
        print(f"❌ Error initializing reranker: {e}")
        logger.error(f"Standalone reranker demo failed: {e}")


def main():
    """
    Main demo function.
    """
    print("🎯 Enterprise KG Advanced Reranking Demo")
    print("=" * 50)
    print()
    
    # Check if advanced dependencies are available
    try:
        from sentence_transformers import CrossEncoder
        from sklearn.metrics.pairwise import cosine_similarity
        print("✅ Advanced dependencies available")
        deps_available = True
    except ImportError:
        print("⚠️  Advanced dependencies not available")
        print("   Install with: pip install sentence-transformers scikit-learn")
        deps_available = False
    
    print()
    
    # Run standalone reranker demo
    demo_standalone_reranker()
    print()
    
    # Run full search demo if dependencies are available
    if deps_available:
        try:
            demo_advanced_reranking()
        except Exception as e:
            print(f"❌ Full demo failed: {e}")
            logger.error(f"Advanced reranking demo failed: {e}")
    else:
        print("⚠️  Skipping full search demo due to missing dependencies")
    
    print("🎉 Demo completed!")


if __name__ == "__main__":
    main()
