"""
Hybrid Search Engine for Enterprise KG

This module provides hybrid search capabilities that combine vector similarity search
with graph-based retrieval (GraphRAG) for enhanced information discovery.

The search engine takes chunk indices (typically from Pinecone vector search) and
enriches them with graph-based context using Neo4j knowledge graph traversal.

New in v1.1.0: Advanced Reranking Capabilities
- RRF (Reciprocal Rank Fusion) for combining multiple rankings
- MMR (Maximal Marginal Relevance) for diversity promotion
- Cross-encoder for deep semantic relevance scoring
- Node distance for graph-aware ranking
"""

from .hybrid_search_engine import HybridSearchEngine, create_hybrid_search_engine, search_with_chunk_indices
from .graph_rag import GraphRAG
from .search_strategies import (
    EntityCentricStrategy,
    RelationshipCentricStrategy,
    ChunkExpansionStrategy,
    HierarchicalStrategy
)
from .result_aggregator import SearchResultAggregator
from .search_schemas import (
    SearchQuery,
    SearchResult,
    GraphContext,
    EntityMatch,
    RelationshipMatch,
    SearchStrategy,
    QueryComplexity,
    RoutingDecision,
    QueryAnalysis,
    VectorMetadata,
    RoutingResult
)
from .query_analyzer import QueryAnalyzer
from .vector_metadata_analyzer import VectorMetadataAnalyzer
from .search_router import SearchRouter
from .advanced_reranker import AdvancedReranker

__all__ = [
    'HybridSearchEngine',
    'create_hybrid_search_engine',
    'search_with_chunk_indices',
    'GraphRAG',
    'EntityCentricStrategy',
    'RelationshipCentricStrategy',
    'ChunkExpansionStrategy',
    'HierarchicalStrategy',
    'SearchResultAggregator',
    'SearchQuery',
    'SearchResult',
    'GraphContext',
    'EntityMatch',
    'RelationshipMatch',
    'SearchStrategy',
    'QueryComplexity',
    'RoutingDecision',
    'QueryAnalysis',
    'VectorMetadata',
    'RoutingResult',
    'QueryAnalyzer',
    'VectorMetadataAnalyzer',
    'SearchRouter',
    'AdvancedReranker'
]

# Version info
__version__ = "1.1.0"  # Updated for advanced reranking features
__author__ = "Enterprise KG Team"
