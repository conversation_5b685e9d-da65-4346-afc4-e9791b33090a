# Advanced Reranking Guide for Enterprise KG Search

## Overview

The Enterprise KG search system now includes sophisticated reranking techniques that significantly improve search result quality, relevance, and diversity. This guide explains the four advanced reranking mechanisms and how to use them effectively.

## Advanced Reranking Techniques

### 1. RRF (Reciprocal Rank Fusion)

**Purpose**: Combines multiple ranking sources to create a more robust final ranking.

**How it works**:
- Takes rankings from different sources (original relevance, cross-encoder, node distance)
- Applies the RRF formula: `RRF(d) = Σ(1 / (k + rank(d)))`
- Produces a unified ranking that leverages strengths of all sources

**Benefits**:
- Reduces bias from any single ranking method
- More stable and reliable results
- Handles cases where different methods disagree

**Configuration**:
```python
reranker = AdvancedReranker(rrf_k=60)  # Higher k = more conservative fusion
```

### 2. MMR (Maximal Marginal Relevance)

**Purpose**: Promotes diversity in search results while maintaining relevance.

**How it works**:
- Iteratively selects results that maximize: `λ * relevance - (1-λ) * max_similarity`
- Balances between relevance and diversity
- Prevents redundant or overly similar results

**Benefits**:
- Reduces result redundancy
- Provides broader coverage of topics
- Better user experience with diverse information

**Configuration**:
```python
reranker = AdvancedReranker(mmr_lambda=0.7)  # 0.0 = max diversity, 1.0 = max relevance
```

### 3. Cross-encoder for Semantic Relevance

**Purpose**: Deep semantic understanding of query-result relevance.

**How it works**:
- Uses transformer models to score query-result pairs
- Considers full context and semantic meaning
- More accurate than simple similarity metrics

**Benefits**:
- Superior semantic understanding
- Better handling of complex queries
- Improved relevance scoring

**Configuration**:
```python
reranker = AdvancedReranker(
    cross_encoder_model="cross-encoder/ms-marco-MiniLM-L-6-v2",
    enable_cross_encoder=True
)
```

### 4. Node Distance for Graph-aware Ranking

**Purpose**: Leverages graph structure for entity importance and connectivity.

**How it works**:
- Calculates centrality scores (degree centrality)
- Computes average distances between entities
- Combines centrality and distance for final scores

**Benefits**:
- Graph-aware ranking
- Identifies important hub entities
- Considers entity relationships and connectivity

**Configuration**:
```python
reranker = AdvancedReranker(max_node_distance=3)  # Maximum distance to consider
```

## Installation and Setup

### Dependencies

Install the required packages:

```bash
pip install sentence-transformers scikit-learn numpy scipy
```

### Basic Usage

```python
from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine

# Create search engine with advanced reranking enabled
search_engine = create_hybrid_search_engine(
    enable_advanced_reranking=True
)

# Perform search with automatic reranking
result = search_engine.search(
    chunk_indices=["chunk1", "chunk2", "chunk3"],
    query_text="What are the key performance metrics?",
    max_results=20
)
```

### Advanced Configuration

```python
from enterprise_kg_minimal.search.advanced_reranker import AdvancedReranker
from enterprise_kg_minimal.search.result_aggregator import SearchResultAggregator

# Custom reranker configuration
reranker = AdvancedReranker(
    cross_encoder_model="cross-encoder/ms-marco-MiniLM-L-6-v2",
    enable_cross_encoder=True,
    rrf_k=60,
    mmr_lambda=0.7,
    max_node_distance=3
)

# Use with result aggregator
aggregator = SearchResultAggregator(enable_advanced_reranking=True)
```

## Performance Considerations

### Computational Cost

| Technique | Cost | Speed | Accuracy |
|-----------|------|-------|----------|
| RRF | Low | Fast | Good |
| MMR | Medium | Medium | Good |
| Cross-encoder | High | Slow | Excellent |
| Node Distance | Medium | Medium | Good |

### Recommendations

- **For speed-critical applications**: Use RRF + MMR only
- **For accuracy-critical applications**: Enable all techniques
- **For balanced performance**: Use RRF + MMR + Node Distance

### Configuration Tips

1. **RRF Parameter (k)**:
   - Lower k (30-40): More aggressive fusion
   - Higher k (60-80): More conservative fusion

2. **MMR Lambda**:
   - 0.5-0.6: High diversity
   - 0.7-0.8: Balanced
   - 0.9-1.0: High relevance

3. **Cross-encoder Models**:
   - `ms-marco-MiniLM-L-6-v2`: Fast, good quality
   - `ms-marco-MiniLM-L-12-v2`: Slower, better quality

## Integration Examples

### With Hybrid Search Engine

```python
# Enable advanced reranking in hybrid search
result = search_with_chunk_indices(
    chunk_indices=chunk_indices,
    query_text="Find project managers in AI team",
    enable_advanced_reranking=True,
    enable_intelligent_routing=True
)
```

### Selective Reranking Methods

```python
# Use only specific reranking methods
reranker = AdvancedReranker()
ranked_entities = reranker.rerank_entities(
    entities=entities,
    query=query,
    graph_context=context,
    ranking_methods=["rrf", "mmr"]  # Skip cross-encoder and node distance
)
```

## Monitoring and Debugging

### Reranker Statistics

```python
reranker = AdvancedReranker()
stats = reranker.get_reranker_stats()
print(f"Capabilities: {stats['capabilities']}")
print(f"Cross-encoder enabled: {stats['cross_encoder_enabled']}")
```

### Debug Information

Search results include debug information about reranking:

```python
result = search_engine.search(...)
debug_info = result.debug_info

if "aggregation" in debug_info:
    print(f"Aggregation method: {debug_info['aggregation']}")
    print(f"Entities before dedup: {debug_info.get('entities_before_dedup', 0)}")
    print(f"Entities after dedup: {debug_info.get('entities_after_dedup', 0)}")
```

## Best Practices

1. **Start Simple**: Begin with RRF + MMR, add other techniques as needed
2. **Monitor Performance**: Track processing times and adjust accordingly
3. **Tune Parameters**: Experiment with different parameter values for your use case
4. **Test Thoroughly**: Validate improvements with your specific queries and data
5. **Consider Hardware**: Cross-encoder requires more computational resources

## Troubleshooting

### Common Issues

1. **ImportError for sentence-transformers**:
   ```bash
   pip install sentence-transformers
   ```

2. **Slow performance with cross-encoder**:
   - Disable cross-encoder for speed: `enable_cross_encoder=False`
   - Use smaller model: `cross_encoder_model="cross-encoder/ms-marco-TinyBERT-L-2-v2"`

3. **Memory issues**:
   - Reduce batch sizes
   - Use CPU instead of GPU for cross-encoder
   - Limit max_results parameter

### Fallback Behavior

The system gracefully handles missing dependencies:
- If advanced dependencies are missing, falls back to basic ranking
- Individual techniques fail silently and log warnings
- System continues to function with available techniques

## Future Enhancements

Planned improvements include:
- Learning-to-rank integration
- Query-specific parameter tuning
- Caching for cross-encoder results
- Distributed reranking for large result sets
