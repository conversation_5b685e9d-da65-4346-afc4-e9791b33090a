# Enterprise KG Hybrid Search Engine

A powerful hybrid search engine that combines vector similarity search with graph-based retrieval (GraphRAG) for enhanced information discovery in enterprise knowledge graphs.

## Overview

The hybrid search engine takes chunk indices (typically from vector similarity search like Pinecone) and enriches them with graph-based context using Neo4j knowledge graph traversal. All entity and relationship types are dynamically loaded from the constants module, making the system completely generic and configurable.

## Key Features

- **Hybrid Search**: Combines vector similarity with graph traversal
- **Multiple Strategies**: Entity-centric, relationship-centric, chunk expansion, and hierarchical search
- **Generic Design**: All types loaded from constants - no hardcoding
- **Graph Enrichment**: Expands context through relationship traversal
- **Result Aggregation**: Intelligent deduplication and ranking
- **Type Validation**: Validates entity and relationship types against constants
- **Performance Metrics**: Comprehensive timing and quality metrics

## Architecture

```
Vector Search (Pinecone) → Chunk Indices → Hybrid Search Engine → Enriched Results
                                              ↓
                                         GraphRAG Engine
                                              ↓
                                         Neo4j Knowledge Graph
```

## Components

### 1. HybridSearchEngine
Main search engine that orchestrates the entire search process.

### 2. GraphRAG
Graph-based retrieval augmented generation using Neo4j traversal.

### 3. Search Strategies
- **EntityCentricStrategy**: Focuses on finding and expanding around key entities
- **RelationshipCentricStrategy**: Emphasizes relationships between entities
- **ChunkExpansionStrategy**: Expands context around source chunks
- **HierarchicalStrategy**: Focuses on organizational hierarchies

### 4. SearchResultAggregator
Combines and optimizes results from multiple strategies.

## Quick Start

### Basic Usage

```python
from enterprise_kg_minimal.search import search_with_chunk_indices, SearchStrategy

# Chunk indices from vector similarity search (e.g., Pinecone)
chunk_indices = ["doc1_chunk_0_abc123", "doc1_chunk_1_def456"]

# Execute hybrid search
result = search_with_chunk_indices(
    chunk_indices=chunk_indices,
    query_text="Who manages the AI project?",
    strategy=SearchStrategy.HYBRID,
    max_results=20,
    expansion_depth=2
)

# Access results
print(f"Found {result.total_results} results")
for entity in result.get_top_entities(5):
    print(f"Entity: {entity.name} ({entity.entity_type})")

for rel in result.get_top_relationships(5):
    print(f"Relationship: {rel.source_entity} --[{rel.relationship_type}]--> {rel.target_entity}")
```

### Advanced Usage with Filters

```python
from enterprise_kg_minimal.search import HybridSearchEngine, create_hybrid_search_engine

# Create search engine
search_engine = create_hybrid_search_engine(
    neo4j_uri="bolt://localhost:7687",
    neo4j_user="neo4j",
    neo4j_password="password"
)

# Search with type filters
result = search_engine.search(
    chunk_indices=chunk_indices,
    entity_types={"Person", "Project"},  # Person is unified type for all people
    relationship_types={"manages", "works_for", "involved_in"},
    strategy=SearchStrategy.HIERARCHICAL,
    expansion_depth=3
)
```

### Entity Neighborhood Search

```python
# Find entities connected to a specific entity
result = search_engine.search_entity_neighborhood(
    entity_name="John Smith",
    max_depth=2,
    entity_types={"Person", "Project", "Company"}
)
```

## Search Strategies

### HYBRID (Default)
Combines multiple strategies for comprehensive results:
- Always includes chunk expansion
- Adds entity-centric if entity filters or high-importance boost
- Adds relationship-centric if relationship filters
- Adds hierarchical for organizational entities

### ENTITY_CENTRIC
Focuses on finding and expanding around important entities:
- Boosts high-importance entities
- Prioritizes entities with strong graph connections
- Good for "who" and "what" queries

### RELATIONSHIP_CENTRIC
Emphasizes relationships between entities:
- Boosts high-confidence relationships
- Focuses on connection patterns
- Good for "how" and "why" queries

### CHUNK_EXPANSION
Expands context around source chunks:
- Prioritizes entities from source chunks
- Maintains chunk locality
- Good for document-specific queries

### HIERARCHICAL
Focuses on organizational structure:
- Emphasizes management and reporting relationships
- Boosts organizational entities
- Good for organizational queries

## Configuration

### Entity and Relationship Types

All types are loaded from the constants module:

```python
# Get available types
available_types = search_engine.get_available_types()
print(f"Entity types: {available_types['entity_types']}")
print(f"Relationship types: {available_types['relationship_types']}")
```

### Search Parameters

```python
SearchQuery(
    chunk_indices=["chunk1", "chunk2"],           # Required: chunk IDs from vector search
    query_text="optional query text",            # Optional: for context
    strategy=SearchStrategy.HYBRID,              # Search strategy
    max_results=50,                              # Maximum results to return
    expansion_depth=2,                           # Graph traversal depth
    entity_types={"Person", "Company"},          # Entity type filters
    relationship_types={"manages", "works_for"}, # Relationship type filters
    min_confidence_score=0.3,                   # Minimum relationship confidence
    boost_high_importance=True,                  # Boost important entities
    boost_recent_entities=True                   # Boost recently created entities
)
```

## Result Structure

```python
SearchResult(
    query=search_query,                    # Original query
    graph_context=GraphContext(            # Graph results
        entities=[...],                    # Found entities
        relationships=[...],               # Found relationships
        source_chunks=[...],               # Source chunk data
        entity_types_found=set(...),       # Entity types in results
        relationship_types_found=set(...)  # Relationship types in results
    ),
    total_results=25,                      # Total number of results
    processing_time_ms=150.5,              # Processing time
    strategy_used=SearchStrategy.HYBRID,   # Strategy that was used
    coverage_score=0.85,                   # How well results cover query
    coherence_score=0.92,                  # How connected results are
    relevance_score=0.78                   # Overall relevance
)
```

## Integration with Vector Databases

### Pinecone Integration Example

```python
import pinecone
from enterprise_kg_minimal.search import search_with_chunk_indices

# Initialize Pinecone
pinecone.init(api_key="your-api-key", environment="your-env")
index = pinecone.Index("your-index")

# Vector similarity search
query_vector = get_query_embedding("Who manages the AI project?")
vector_results = index.query(
    vector=query_vector,
    top_k=10,
    include_metadata=True
)

# Extract chunk indices
chunk_indices = [match['id'] for match in vector_results['matches']]

# Enrich with graph context
graph_enriched_results = search_with_chunk_indices(
    chunk_indices=chunk_indices,
    query_text="Who manages the AI project?",
    strategy=SearchStrategy.HYBRID
)
```

## Performance Considerations

### Optimization Tips

1. **Limit Expansion Depth**: Higher depths increase processing time exponentially
2. **Use Type Filters**: Reduce search space with entity/relationship type filters
3. **Adjust Max Results**: Balance comprehensiveness with performance
4. **Choose Appropriate Strategy**: Different strategies have different performance characteristics

### Monitoring

```python
# Validate parameters before search
validation = search_engine.validate_search_parameters(
    chunk_indices=chunk_indices,
    entity_types=entity_types,
    expansion_depth=3,
    max_results=100
)

if not validation['valid']:
    print("Validation errors:", validation['errors'])
```

## Error Handling

The search engine provides comprehensive error handling:

```python
try:
    result = search_engine.search(chunk_indices=chunk_indices)
    if result.debug_info.get('error'):
        print(f"Search error: {result.debug_info['error']}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

## Examples

See `example_usage.py` for comprehensive examples including:
- Basic hybrid search
- Filtered search with type constraints
- Entity neighborhood exploration
- Parameter validation
- Strategy comparison

## Requirements

- Neo4j database with enterprise knowledge graph
- Python 3.8+
- neo4j-driver
- enterprise_kg_minimal.constants module

## Future Enhancements

- Parallel strategy execution
- Caching for frequently accessed entities
- Advanced ranking algorithms
- Real-time graph updates
- Integration with more vector databases
