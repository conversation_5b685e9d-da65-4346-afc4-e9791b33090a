"""
CSV Schema Suggestion Engine

This module provides functionality to suggest entity-relationship schemas
for CSV files based on column analysis and predefined templates.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict, Counter

from ..constants.csv_processing import (
    get_csv_schema_templates, CSVEntityInferenceStrategy
)
from ..constants.schemas import CSVSchemaInference, CSVColumnProfile
from ..constants.entities import get_all_entity_types
from ..constants.relationships import get_all_relationship_types

logger = logging.getLogger(__name__)


class CSVSchemaEngine:
    """
    Suggests entity-relationship schemas for CSV files.
    
    This engine uses template matching, pattern recognition, and heuristics
    to suggest the best schema representation for CSV data.
    """
    
    def __init__(self, confidence_threshold: float = 0.6):
        """
        Initialize the schema suggestion engine.
        
        Args:
            confidence_threshold: Minimum confidence for schema suggestions
        """
        self.confidence_threshold = confidence_threshold
        self.schema_templates = get_csv_schema_templates()
        self.available_entities = set(get_all_entity_types())
        self.available_relationships = set(get_all_relationship_types())
    
    def suggest_schema(self, schema_inference: CSVSchemaInference) -> CSVSchemaInference:
        """
        Enhance schema inference with template-based suggestions.
        
        Args:
            schema_inference: Initial schema inference from CSV analyzer
            
        Returns:
            Enhanced schema inference with template suggestions
        """
        logger.info(f"Suggesting schema for {schema_inference.file_name}")
        
        # Try to match against known templates
        template_match = self._match_template(schema_inference)
        
        if template_match:
            logger.info(f"Matched template: {template_match['template_name']}")
            schema_inference = self._apply_template(schema_inference, template_match)
        
        # Enhance with additional suggestions
        schema_inference = self._enhance_relationships(schema_inference)
        schema_inference = self._validate_entity_types(schema_inference)
        schema_inference = self._recalculate_confidence(schema_inference)
        
        return schema_inference
    
    def _match_template(self, schema_inference: CSVSchemaInference) -> Optional[Dict[str, Any]]:
        """
        Try to match the CSV schema against known templates.
        
        Args:
            schema_inference: Schema inference to match
            
        Returns:
            Best matching template or None
        """
        best_match = None
        best_score = 0.0
        
        column_names = [p.column_name.lower() for p in schema_inference.column_profiles]
        
        for template_name, template in self.schema_templates.items():
            score = self._calculate_template_match_score(column_names, template)
            
            if score > best_score and score >= self.confidence_threshold:
                best_score = score
                best_match = {
                    'template_name': template_name,
                    'template': template,
                    'match_score': score
                }
        
        return best_match
    
    def _calculate_template_match_score(self, column_names: List[str], template: Dict[str, Any]) -> float:
        """
        Calculate how well column names match a template.
        
        Args:
            column_names: List of column names (lowercase)
            template: Template to match against
            
        Returns:
            Match score between 0.0 and 1.0
        """
        total_template_columns = 0
        matched_columns = 0
        
        # Check identifier columns
        for identifier in template.get('identifier_columns', []):
            total_template_columns += 1
            if any(identifier.lower() in col_name for col_name in column_names):
                matched_columns += 2  # Higher weight for identifiers
        
        # Check attribute columns
        for attribute in template.get('attribute_columns', []):
            total_template_columns += 1
            if any(attribute.lower() in col_name for col_name in column_names):
                matched_columns += 1
        
        # Check relationship columns
        for relationship in template.get('relationship_columns', []):
            total_template_columns += 1
            if any(relationship.lower() in col_name for col_name in column_names):
                matched_columns += 1.5  # Medium weight for relationships
        
        if total_template_columns == 0:
            return 0.0
        
        return min(matched_columns / total_template_columns, 1.0)
    
    def _apply_template(self, schema_inference: CSVSchemaInference, template_match: Dict[str, Any]) -> CSVSchemaInference:
        """
        Apply template suggestions to schema inference.
        
        Args:
            schema_inference: Original schema inference
            template_match: Matched template information
            
        Returns:
            Enhanced schema inference
        """
        template = template_match['template']
        
        # Update primary entity type if template suggests one
        if template.get('primary_entity'):
            schema_inference.primary_entity_type = template['primary_entity']
        
        # Enhance column classifications based on template
        column_names_lower = {p.column_name.lower(): p for p in schema_inference.column_profiles}
        
        # Update identifier columns
        for identifier in template.get('identifier_columns', []):
            for col_name_lower, profile in column_names_lower.items():
                if identifier.lower() in col_name_lower:
                    profile.is_identifier = True
                    if profile.column_name not in schema_inference.identifier_columns:
                        schema_inference.identifier_columns.append(profile.column_name)
        
        # Update relationship columns
        for relationship in template.get('relationship_columns', []):
            for col_name_lower, profile in column_names_lower.items():
                if relationship.lower() in col_name_lower:
                    profile.is_foreign_key = True
                    if profile.column_name not in schema_inference.relationship_columns:
                        schema_inference.relationship_columns.append(profile.column_name)
        
        # Add template relationships
        template_relationships = template.get('suggested_relationships', [])
        for rel in template_relationships:
            if rel not in schema_inference.suggested_relationships:
                schema_inference.suggested_relationships.append(rel)
        
        return schema_inference
    
    def _enhance_relationships(self, schema_inference: CSVSchemaInference) -> CSVSchemaInference:
        """
        Enhance relationship suggestions based on column patterns.
        
        Args:
            schema_inference: Schema inference to enhance
            
        Returns:
            Enhanced schema inference
        """
        primary_entity = schema_inference.primary_entity_type or "Entity"
        
        # Common relationship patterns
        relationship_patterns = {
            'manager': ('MANAGED_BY', 'Person'),
            'lead': ('LED_BY', 'Person'),
            'owner': ('OWNED_BY', 'Person'),
            'assignee': ('ASSIGNED_TO', 'Person'),
            'reporter': ('REPORTED_BY', 'Person'),
            'project': ('BELONGS_TO', 'Project'),
            'department': ('BELONGS_TO', 'Department'),
            'team': ('MEMBER_OF', 'Team'),
            'company': ('WORKS_FOR', 'Company'),
            'client': ('CLIENT_OF', 'Company'),
            'vendor': ('VENDOR_OF', 'Company')
        }
        
        for profile in schema_inference.column_profiles:
            if profile.is_foreign_key or profile.inferred_relationship:
                col_name_lower = profile.column_name.lower()
                
                for pattern, (relationship, target_entity) in relationship_patterns.items():
                    if pattern in col_name_lower:
                        suggested_rel = (primary_entity, relationship, target_entity)
                        if suggested_rel not in schema_inference.suggested_relationships:
                            schema_inference.suggested_relationships.append(suggested_rel)
        
        return schema_inference
    
    def _validate_entity_types(self, schema_inference: CSVSchemaInference) -> CSVSchemaInference:
        """
        Validate and correct entity types against available types.
        
        Args:
            schema_inference: Schema inference to validate
            
        Returns:
            Validated schema inference
        """
        # Validate primary entity type
        if schema_inference.primary_entity_type not in self.available_entities:
            # Try to find a close match
            primary_lower = schema_inference.primary_entity_type.lower()
            for entity_type in self.available_entities:
                if entity_type.lower() == primary_lower:
                    schema_inference.primary_entity_type = entity_type
                    break
            else:
                # Default to generic Entity
                schema_inference.primary_entity_type = "Entity"
        
        # Validate relationship entity types
        validated_relationships = []
        for subject, predicate, obj in schema_inference.suggested_relationships:
            # Validate subject
            if subject not in self.available_entities:
                subject = self._find_closest_entity_type(subject) or "Entity"
            
            # Validate object
            if obj not in self.available_entities:
                obj = self._find_closest_entity_type(obj) or "Entity"
            
            # Validate predicate
            if predicate not in self.available_relationships:
                predicate = self._find_closest_relationship_type(predicate) or "RELATED_TO"
            
            validated_relationships.append((subject, predicate, obj))
        
        schema_inference.suggested_relationships = validated_relationships
        return schema_inference
    
    def _find_closest_entity_type(self, entity_type: str) -> Optional[str]:
        """
        Find the closest matching entity type.
        
        Args:
            entity_type: Entity type to match
            
        Returns:
            Closest matching entity type or None
        """
        entity_lower = entity_type.lower()
        
        for available_entity in self.available_entities:
            if available_entity.lower() == entity_lower:
                return available_entity
        
        # Try partial matches
        for available_entity in self.available_entities:
            if entity_lower in available_entity.lower() or available_entity.lower() in entity_lower:
                return available_entity
        
        return None
    
    def _find_closest_relationship_type(self, relationship_type: str) -> Optional[str]:
        """
        Find the closest matching relationship type.
        
        Args:
            relationship_type: Relationship type to match
            
        Returns:
            Closest matching relationship type or None
        """
        rel_lower = relationship_type.lower()
        
        for available_rel in self.available_relationships:
            if available_rel.lower() == rel_lower:
                return available_rel
        
        # Try partial matches
        for available_rel in self.available_relationships:
            if rel_lower in available_rel.lower() or available_rel.lower() in rel_lower:
                return available_rel
        
        return None
    
    def _recalculate_confidence(self, schema_inference: CSVSchemaInference) -> CSVSchemaInference:
        """
        Recalculate confidence score after enhancements.
        
        Args:
            schema_inference: Schema inference to recalculate
            
        Returns:
            Schema inference with updated confidence
        """
        confidence_factors = []
        
        # Factor 1: Template match (if any)
        if hasattr(schema_inference, '_template_match_score'):
            confidence_factors.append(schema_inference._template_match_score)
        
        # Factor 2: Identifier presence
        identifier_confidence = 0.8 if schema_inference.identifier_columns else 0.3
        confidence_factors.append(identifier_confidence)
        
        # Factor 3: Relationship suggestions
        relationship_confidence = min(len(schema_inference.suggested_relationships) * 0.2, 1.0)
        confidence_factors.append(relationship_confidence)
        
        # Factor 4: Entity type validation
        valid_entities = sum(1 for _, _, obj in schema_inference.suggested_relationships 
                           if obj in self.available_entities)
        entity_confidence = valid_entities / len(schema_inference.suggested_relationships) if schema_inference.suggested_relationships else 0.5
        confidence_factors.append(entity_confidence)
        
        # Calculate weighted average
        schema_inference.schema_confidence = sum(confidence_factors) / len(confidence_factors)
        
        return schema_inference
    
    def generate_schema_summary(self, schema_inference: CSVSchemaInference) -> Dict[str, Any]:
        """
        Generate a human-readable summary of the schema inference.
        
        Args:
            schema_inference: Schema inference to summarize
            
        Returns:
            Dictionary with schema summary
        """
        return {
            'file_name': schema_inference.file_name,
            'primary_entity': schema_inference.primary_entity_type,
            'confidence': round(schema_inference.schema_confidence, 2),
            'total_columns': schema_inference.total_columns,
            'identifier_columns': schema_inference.identifier_columns,
            'attribute_columns': schema_inference.attribute_columns,
            'relationship_columns': schema_inference.relationship_columns,
            'suggested_relationships': [
                f"{subj} -> {pred} -> {obj}" 
                for subj, pred, obj in schema_inference.suggested_relationships
            ],
            'inference_strategy': schema_inference.inference_strategy
        }
