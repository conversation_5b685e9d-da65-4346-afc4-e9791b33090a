"""
Graph Builder for Enterprise KG Minimal

This module handles the construction of knowledge graphs from document chunks,
creating the File → Chunks → Chunk Graphs structure.
"""

import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from ..constants.schemas import EntityRelationship
from ..storage.neo4j_client import Neo4jClient
from .chunking_engine import DocumentChunk

logger = logging.getLogger(__name__)


@dataclass
class ChunkGraphResult:
    """Result of processing a single chunk."""
    chunk_id: str
    chunk_index: int
    entities_extracted: int
    relationships_extracted: int
    graph_stored: bool
    error: Optional[str] = None


@dataclass
class DocumentGraphResult:
    """Result of processing an entire document."""
    file_id: str
    total_chunks: int
    successful_chunks: int
    failed_chunks: int
    total_entities: int
    total_relationships: int
    chunk_results: List[ChunkGraphResult]
    file_node_created: bool
    contains_relationships_created: int


class GraphBuilder:
    """
    Builds knowledge graphs from document chunks with proper file → chunk → graph structure.
    """
    
    def __init__(self, neo4j_client: Neo4jClient):
        """
        Initialize the graph builder.
        
        Args:
            neo4j_client: Neo4j client for graph storage
        """
        self.neo4j_client = neo4j_client
    
    def build_document_graph(
        self,
        file_id: str,
        chunks: List[DocumentChunk],
        chunk_relationships: List[List[EntityRelationship]]
    ) -> DocumentGraphResult:
        """
        Build a complete document graph with file → chunks → chunk graphs structure.
        
        Args:
            file_id: Unique identifier for the file
            chunks: List of document chunks
            chunk_relationships: List of relationships for each chunk
            
        Returns:
            DocumentGraphResult with processing summary
        """
        logger.info(f"Building graph for document {file_id} with {len(chunks)} chunks")
        
        # Initialize result
        result = DocumentGraphResult(
            file_id=file_id,
            total_chunks=len(chunks),
            successful_chunks=0,
            failed_chunks=0,
            total_entities=0,
            total_relationships=0,
            chunk_results=[],
            file_node_created=False,
            contains_relationships_created=0
        )
        
        try:
            # Step 1: Create or ensure file node exists
            file_node_created = self._create_file_node(file_id)
            result.file_node_created = file_node_created
            
            # Step 2: Process each chunk and create chunk graphs
            for i, (chunk, relationships) in enumerate(zip(chunks, chunk_relationships)):
                chunk_result = self._process_chunk(chunk, relationships)
                result.chunk_results.append(chunk_result)
                
                if chunk_result.graph_stored:
                    result.successful_chunks += 1
                    result.total_entities += chunk_result.entities_extracted
                    result.total_relationships += chunk_result.relationships_extracted
                    
                    # Step 3: Create CONTAINS relationship from file to chunk
                    contains_created = self._create_contains_relationship(file_id, chunk.metadata.chunk_id)
                    if contains_created:
                        result.contains_relationships_created += 1
                else:
                    result.failed_chunks += 1
                    logger.warning(f"Failed to process chunk {chunk.metadata.chunk_id}: {chunk_result.error}")
            
            logger.info(f"Document graph built: {result.successful_chunks}/{result.total_chunks} chunks processed")
            
        except Exception as e:
            logger.error(f"Failed to build document graph for {file_id}: {e}")
            # Mark all chunks as failed if document-level error occurs
            result.failed_chunks = len(chunks)
            for chunk in chunks:
                result.chunk_results.append(ChunkGraphResult(
                    chunk_id=chunk.metadata.chunk_id,
                    chunk_index=chunk.metadata.chunk_index,
                    entities_extracted=0,
                    relationships_extracted=0,
                    graph_stored=False,
                    error=str(e)
                ))
        
        return result
    
    def _create_file_node(self, file_id: str) -> bool:
        """
        Create or ensure file node exists in the graph.
        
        Args:
            file_id: File identifier
            
        Returns:
            True if node was created or already exists
        """
        try:
            # Create file node with File label
            query = """
            MERGE (f:File {id: $file_id})
            ON CREATE SET f.created_at = datetime()
            ON MATCH SET f.last_updated = datetime()
            RETURN f.id as file_id
            """
            
            result = self.neo4j_client.execute_query(query, {"file_id": file_id})
            return len(result) > 0
            
        except Exception as e:
            logger.error(f"Failed to create file node {file_id}: {e}")
            return False
    
    def _process_chunk(self, chunk: DocumentChunk, relationships: List[EntityRelationship]) -> ChunkGraphResult:
        """
        Process a single chunk and create its graph.
        
        Args:
            chunk: Document chunk to process
            relationships: Extracted relationships for this chunk
            
        Returns:
            ChunkGraphResult with processing details
        """
        chunk_id = chunk.metadata.chunk_id
        
        try:
            # Step 1: Create chunk node
            chunk_node_created = self._create_chunk_node(chunk)
            if not chunk_node_created:
                return ChunkGraphResult(
                    chunk_id=chunk_id,
                    chunk_index=chunk.metadata.chunk_index,
                    entities_extracted=0,
                    relationships_extracted=0,
                    graph_stored=False,
                    error="Failed to create chunk node"
                )
            
            # Step 2: Store relationships and connect to chunk
            entities_count = 0
            relationships_count = 0
            
            for relationship in relationships:
                try:
                    # Validate entity compatibility before creating relationship
                    if not self._validate_entity_relationship_compatibility(relationship):
                        logger.warning(f"Skipping incompatible relationship in chunk {chunk_id}: "
                                     f"{relationship.subject} ({relationship.subject_type}) "
                                     f"-> {relationship.object} ({relationship.object_type})")
                        continue

                    # Store the relationship in the graph
                    stored = self.neo4j_client.create_entity_relationship(relationship)
                    if stored:
                        relationships_count += 1
                        entities_count += 2  # Each relationship involves 2 entities

                        # Connect entities to chunk with type-safe matching
                        connected = self._connect_entities_to_chunk(relationship, chunk_id)
                        if not connected:
                            logger.warning(f"Failed to connect entities to chunk {chunk_id} for relationship: "
                                         f"{relationship.subject} -> {relationship.object}")

                except Exception as e:
                    logger.warning(f"Failed to store relationship in chunk {chunk_id}: {e}")
                    continue
            
            return ChunkGraphResult(
                chunk_id=chunk_id,
                chunk_index=chunk.metadata.chunk_index,
                entities_extracted=entities_count,
                relationships_extracted=relationships_count,
                graph_stored=True
            )
            
        except Exception as e:
            logger.error(f"Failed to process chunk {chunk_id}: {e}")
            return ChunkGraphResult(
                chunk_id=chunk_id,
                chunk_index=chunk.metadata.chunk_index,
                entities_extracted=0,
                relationships_extracted=0,
                graph_stored=False,
                error=str(e)
            )

    def _create_chunk_node(self, chunk: DocumentChunk) -> bool:
        """
        Create a chunk node in the graph.

        Args:
            chunk: Document chunk to create node for

        Returns:
            True if chunk node was created successfully
        """
        try:
            query = """
            MERGE (c:Chunk {id: $chunk_id})
            ON CREATE SET
                c.text = $text,
                c.chunk_index = $chunk_index,
                c.word_count = $word_count,
                c.sentence_count = $sentence_count,
                c.start_position = $start_position,
                c.end_position = $end_position,
                c.strategy_used = $strategy_used,
                c.created_at = datetime()
            ON MATCH SET c.last_updated = datetime()
            RETURN c.id as chunk_id
            """

            params = {
                "chunk_id": chunk.metadata.chunk_id,
                "text": chunk.text,
                "chunk_index": chunk.metadata.chunk_index,
                "word_count": chunk.metadata.word_count,
                "sentence_count": chunk.metadata.sentence_count,
                "start_position": chunk.metadata.start_position,
                "end_position": chunk.metadata.end_position,
                "strategy_used": chunk.metadata.strategy_used
            }

            result = self.neo4j_client.execute_query(query, params)
            return len(result) > 0

        except Exception as e:
            logger.error(f"Failed to create chunk node {chunk.metadata.chunk_id}: {e}")
            return False

    def _create_contains_relationship(self, file_id: str, chunk_id: str) -> bool:
        """
        Create CONTAINS relationship from file to chunk.

        Args:
            file_id: File identifier
            chunk_id: Chunk identifier

        Returns:
            True if relationship was created successfully
        """
        try:
            query = """
            MATCH (f:File {id: $file_id})
            MATCH (c:Chunk {id: $chunk_id})
            MERGE (f)-[r:CONTAINS]->(c)
            ON CREATE SET r.created_at = datetime()
            RETURN r
            """

            result = self.neo4j_client.execute_query(query, {
                "file_id": file_id,
                "chunk_id": chunk_id
            })
            return len(result) > 0

        except Exception as e:
            logger.error(f"Failed to create CONTAINS relationship {file_id} -> {chunk_id}: {e}")
            return False

    def _connect_entities_to_chunk(self, relationship: EntityRelationship, chunk_id: str) -> bool:
        """
        Connect entities in a relationship to the chunk they came from.
        Uses entity name + type for precise matching to avoid connecting wrong entities.

        Args:
            relationship: Entity relationship containing source and target entities
            chunk_id: Chunk identifier

        Returns:
            True if connections were created successfully
        """
        try:
            # Connect source entity to chunk - match by name AND entity_type for precision
            query_source = """
            MATCH (e {name: $entity_name})
            WHERE e.entity_type = $entity_type OR $entity_type IS NULL
            MATCH (c:Chunk {id: $chunk_id})
            MERGE (c)-[r:EXTRACTED_FROM]->(e)
            ON CREATE SET r.created_at = datetime()
            RETURN r, e.entity_type as matched_type
            """

            # Connect target entity to chunk - match by name AND entity_type for precision
            query_target = """
            MATCH (e {name: $entity_name})
            WHERE e.entity_type = $entity_type OR $entity_type IS NULL
            MATCH (c:Chunk {id: $chunk_id})
            MERGE (c)-[r:EXTRACTED_FROM]->(e)
            ON CREATE SET r.created_at = datetime()
            RETURN r, e.entity_type as matched_type
            """

            # Execute for source entity with type checking
            source_result = self.neo4j_client.execute_query(query_source, {
                "entity_name": relationship.subject,
                "entity_type": relationship.subject_type,
                "chunk_id": chunk_id
            })

            # Execute for target entity with type checking
            target_result = self.neo4j_client.execute_query(query_target, {
                "entity_name": relationship.object,
                "entity_type": relationship.object_type,
                "chunk_id": chunk_id
            })

            # Check if both connections were created
            source_connected = len(source_result) > 0
            target_connected = len(target_result) > 0

            if source_connected and target_connected:
                # Log the matched types for verification
                source_type = source_result[0].get('matched_type') if source_result else 'unknown'
                target_type = target_result[0].get('matched_type') if target_result else 'unknown'
                logger.debug(f"Connected entities to chunk {chunk_id}: "
                           f"'{relationship.subject}' ({source_type}) and "
                           f"'{relationship.object}' ({target_type})")
                return True
            else:
                logger.warning(f"Failed to connect some entities to chunk {chunk_id}: "
                             f"source='{relationship.subject}' ({relationship.subject_type}) connected={source_connected}, "
                             f"target='{relationship.object}' ({relationship.object_type}) connected={target_connected}")
                return False

        except Exception as e:
            logger.error(f"Failed to connect entities to chunk {chunk_id}: {e}")
            return False

    def _validate_entity_relationship_compatibility(self, relationship: EntityRelationship) -> bool:
        """
        Validate that entities in a relationship are compatible for connection.

        This ensures we don't create relationships between incompatible entity types
        (e.g., "Apple" the company vs "Apple" the fruit).

        Args:
            relationship: Entity relationship to validate

        Returns:
            True if entities are compatible for relationship
        """
        try:
            # If either entity type is missing, allow the relationship
            if not relationship.subject_type or not relationship.object_type:
                return True

            # Define compatible entity type groups
            compatible_groups = [
                # People and organizations - unified Person type
                {"Person", "Team", "Organization", "Company", "Department"},
                # Technology and systems
                {"Technology", "System", "Software", "Platform", "Tool", "Service", "API"},
                # Business concepts
                {"Goal", "Objective", "Initiative", "Project", "Process", "Policy", "Strategy"},
                # Documents and content
                {"Document", "Report", "File", "Content", "Data"},
                # Jira work items and management
                {"Issue", "Task", "Epic", "Story", "Subtask", "Bug", "Ticket", "Sprint", "Status", "Priority", "Resolution"},
                # Time and scheduling
                {"Deadline", "Milestone", "Phase", "Timeline", "Schedule", "Date"},
                # Metrics and measurements
                {"Story Points", "Priority", "Rating", "Score", "Metric"},
                # General entities (can connect to anything)
                {"Entity", "Concept", "Thing", "Object"}
            ]

            subject_type = relationship.subject_type.lower()
            object_type = relationship.object_type.lower()

            # Check if both types are in the same compatibility group
            for group in compatible_groups:
                group_lower = {t.lower() for t in group}
                if subject_type in group_lower and object_type in group_lower:
                    return True

            # Define cross-group compatibility rules for common relationships
            cross_group_compatible = [
                # People can be assigned to/work on work items
                ({"person", "employee", "manager", "executive"}, {"issue", "task", "epic", "story", "subtask", "bug", "ticket"}),
                # Work items belong to projects and sprints
                ({"issue", "task", "epic", "story", "subtask", "bug", "ticket"}, {"project", "sprint", "initiative"}),
                # Work items have statuses, priorities, and deadlines
                ({"issue", "task", "epic", "story", "subtask", "bug", "ticket"}, {"status", "priority", "deadline", "milestone"}),
                # Projects involve people and teams
                ({"project", "initiative", "goal"}, {"person", "employee", "manager", "team", "organization"}),
                # Technology used in projects and by people
                ({"technology", "system", "software", "platform", "tool"}, {"project", "person", "team"}),
                # Documents relate to projects and people
                ({"document", "report", "file"}, {"project", "person", "issue", "task"}),
            ]

            # Check cross-group compatibility
            for group1, group2 in cross_group_compatible:
                if ((subject_type in group1 and object_type in group2) or
                    (subject_type in group2 and object_type in group1)):
                    return True

            # Special case: Entity type can connect to anything
            if "entity" in subject_type or "entity" in object_type:
                return True

            # If no compatibility found, log debug info but allow (conservative approach)
            logger.debug(f"Cross-domain relationship (allowed): {relationship.subject} ({relationship.subject_type}) "
                        f"-> {relationship.object} ({relationship.object_type})")
            return True

        except Exception as e:
            logger.error(f"Error validating entity compatibility: {e}")
            return True  # Conservative: allow relationship if validation fails
