"""
Dynamic Prompt Generator

This module generates LLM prompts dynamically based on the constants
defined in the entities and relationships files. This ensures prompts
are always up-to-date with the latest entity and relationship types.
"""

from typing import List, Set, Dict, Any
from ..constants.entities import (
    get_all_entity_types,
    get_person_related_types,
    get_project_related_types,
    get_entity_category_mapping
)
from ..constants.relationships import (
    get_all_relationship_types,
    get_relationship_category_mapping,
    get_common_entity_relationship_patterns
)


class PromptGenerator:
    """
    Generates dynamic prompts for entity and relationship extraction
    based on the constants defined in the system.
    """

    def __init__(
        self,
        focus_entities: List[str] = None,
        focus_relationships: List[str] = None,
        use_all_constants: bool = True
    ):
        """
        Initialize the prompt generator.

        Args:
            focus_entities: Specific entity types to focus on
            focus_relationships: Specific relationship types to focus on
            use_all_constants: Whether to use all available constants
        """
        if use_all_constants:
            self.focus_entities = list(get_all_entity_types())
            self.focus_relationships = list(get_all_relationship_types())
        else:
            self.focus_entities = focus_entities or ["Person", "Project", "Company"]
            self.focus_relationships = focus_relationships or ["involved_in", "mentions"]

    def generate_relationship_extraction_prompt(self, content: str) -> str:
        """
        Generate a dynamic prompt for relationship extraction.

        Args:
            content: Document content to extract from

        Returns:
            Complete prompt for LLM
        """
        # Group entities by category for better organization
        entity_groups = self._group_entities_by_category()
        relationship_groups = self._group_relationships_by_category()

        # Generate entity types section
        entity_section = self._format_entity_types(entity_groups)

        # Generate relationship types section
        relationship_section = self._format_relationship_types(relationship_groups)

        # Generate examples based on common patterns
        examples_section = self._generate_examples()

        prompt = f"""
Please extract entity relationships from this enterprise document:

{content}

{entity_section}

{relationship_section}

EXTRACTION GUIDELINES:
1. Focus on business-relevant entities and relationships
2. PERSON ENTITIES: Use "Person" type for all people, regardless of their role:
   - Extract as "Person" type: employees, managers, executives, directors, etc.
   - Include role context in the entity name when clear: "John Smith (CEO)", "Sarah Johnson (Manager)"
   - The system will automatically extract role and persona properties from context
3. COREFERENCE RESOLUTION: When the same entity is mentioned multiple ways, use the most complete/formal name consistently:
   - "John Smith" and "John" and "Mr. Smith" → use "John Smith"
   - "IBM" and "International Business Machines" → use "International Business Machines"
   - "CRM System" and "the system" and "it" → use "CRM System"
4. PRONOUN RESOLUTION: Replace pronouns with their actual entity names:
   - "he" → the person's actual name
   - "she" → the person's actual name
   - "it" → the system/product/company name
   - "they" → the team/organization name
5. Use only the predefined relationship types listed above
6. Ensure subject and object are clear, specific entity names (not pronouns)
7. Extract relationships that are explicitly stated or clearly implied
8. Prioritize relationships between people, projects, organizations, and systems
9. CONSISTENCY: Use the same entity name throughout the document for the same real-world entity

{examples_section}

Extract all relevant relationships following this pattern.
"""
        return prompt

    def generate_entity_extraction_prompt(self, content: str) -> str:
        """
        Generate a dynamic prompt for entity-only extraction.

        Args:
            content: Document content to extract from

        Returns:
            Complete prompt for LLM
        """
        entity_groups = self._group_entities_by_category()
        entity_section = self._format_entity_types(entity_groups)

        prompt = f"""
Please extract all relevant entities from this enterprise document:

{content}

{entity_section}

EXTRACTION GUIDELINES:
1. Identify specific, named entities (not generic references)
2. COREFERENCE RESOLUTION: Use consistent naming for the same entity:
   - If "John Smith" appears later as "John" or "Mr. Smith", always use "John Smith"
   - If "IBM" appears as "International Business Machines", use the full name
   - Replace pronouns (he, she, it, they) with actual entity names
3. Use the most specific entity type available
4. Provide a brief description if the entity's role/purpose is clear
5. Focus on business-relevant entities
6. CONSISTENCY: Same real-world entity = same name in extraction

EXAMPLES:
- Entity(name="John Smith", entity_type="Person", description="Project Manager")
- Entity(name="CRM System", entity_type="System", description="Customer relationship management platform")
- Entity(name="Q4 Marketing Campaign", entity_type="Campaign", description="Fourth quarter marketing initiative")

Extract all relevant entities following this pattern.
"""
        return prompt

    def generate_summarization_prompt(self, content: str, document_type: str = None) -> str:
        """
        Generate a dynamic prompt for document summarization.

        Args:
            content: Document content to summarize
            document_type: Optional document type for specialized prompts

        Returns:
            Complete prompt for LLM
        """
        base_prompt = f"""
Please analyze this enterprise document and provide a comprehensive summary:

{content}

Focus on extracting the main title, key points, document type, and important topics discussed.
Be concise but thorough. If the document type is unclear, make your best assessment based on content and structure.
"""

        if document_type:
            specialized_instruction = self._get_specialized_summarization_instruction(document_type)
            base_prompt += f"\n\nSpecialized Instructions for {document_type}:\n{specialized_instruction}"

        return base_prompt

    def generate_feedback_sentiment_prompt(self, content: str) -> str:
        """
        Generate a specialized prompt for customer feedback sentiment analysis.

        Args:
            content: Customer feedback content to analyze

        Returns:
            Complete prompt for LLM with sentiment analysis focus
        """
        prompt = f"""
Please analyze this customer feedback and extract entities, relationships, and sentiment information:

{content}

FEEDBACK ANALYSIS REQUIREMENTS:

1. SENTIMENT CLASSIFICATION:
   - Overall sentiment: positive, negative, neutral, or mixed
   - Sentiment score: -1.0 (very negative) to 1.0 (very positive)
   - Confidence level: 0.0 to 1.0

2. ENTITY EXTRACTION (Focus on feedback-related entities):
   - Customer: The person providing feedback
   - Product/Service: What is being reviewed
   - Feedback: The feedback content itself
   - Sentiment: Emotional expressions
   - Complaint/Compliment: Specific issues or praise
   - Rating: Any numerical or categorical ratings

3. RELATIONSHIP EXTRACTION (Focus on feedback relationships):
   - provides_feedback: Customer provides feedback about product
   - expresses_sentiment: Feedback expresses specific sentiment
   - relates_to_product: Feedback relates to specific product/service
   - rates: Customer rates product/service
   - complains_about: Customer complains about specific issue
   - praises: Customer praises specific aspect
   - experiences: Customer experiences issue or benefit

4. KEY INSIGHTS:
   - Identify specific issues mentioned
   - Identify positive aspects highlighted
   - Determine if action is required
   - Assess priority level (low, medium, high, critical)

EXAMPLES:
- "John Smith" -> "provides_feedback" -> "CRM Software"
- "Feedback" -> "expresses_sentiment" -> "Positive"
- "Sarah Johnson" -> "praises" -> "Customer Support"
- "Bug Report" -> "relates_to_product" -> "Mobile App"

Extract all relevant entities and relationships following this pattern, with special attention to sentiment indicators and customer experience details.
"""
        return prompt

    def generate_coreference_aware_prompt(self, content: str, previous_entities: List[str] = None) -> str:
        """
        Generate a coreference-aware prompt that considers previously mentioned entities.

        Args:
            content: Document content to extract from
            previous_entities: List of entities mentioned in previous chunks

        Returns:
            Complete prompt for LLM with coreference resolution instructions
        """
        # Group entities by category for better organization
        entity_groups = self._group_entities_by_category()
        relationship_groups = self._group_relationships_by_category()

        # Generate entity types section
        entity_section = self._format_entity_types(entity_groups)

        # Generate relationship types section
        relationship_section = self._format_relationship_types(relationship_groups)

        # Generate examples based on common patterns
        examples_section = self._generate_examples()

        # Add previous entities context if available
        previous_entities_section = ""
        if previous_entities:
            previous_entities_section = f"""
PREVIOUSLY MENTIONED ENTITIES (use these exact names if referring to the same entities):
{', '.join(previous_entities[:20])}  # Show first 20 to avoid prompt bloat

"""

        prompt = f"""
Please extract entity relationships from this enterprise document with COREFERENCE RESOLUTION:

{content}

{previous_entities_section}{entity_section}

{relationship_section}

COREFERENCE RESOLUTION GUIDELINES:
1. PERSON ENTITIES: Use "Person" type for all people, regardless of their role:
   - Extract as "Person" type: employees, managers, executives, directors, etc.
   - Include role context in the entity name when clear: "John Smith (CEO)", "Sarah Johnson (Manager)"
   - The system will automatically extract role and persona properties from context

2. ENTITY CONSISTENCY: If the same real-world entity is mentioned multiple ways, use ONE consistent name:
   - "John Smith", "John", "Mr. Smith", "he" → ALL become "John Smith"
   - "IBM", "International Business Machines", "the company" → ALL become "International Business Machines"
   - "CRM System", "the system", "it" → ALL become "CRM System"

3. PRONOUN RESOLUTION: Replace ALL pronouns with actual entity names:
   - "John Smith manages the project. He is experienced." → "John Smith manages the project. John Smith is experienced."
   - "The system crashed. It needs repair." → "CRM System crashed. CRM System needs repair."

4. CROSS-REFERENCE CHECK: If an entity was mentioned in previous chunks, use the SAME name

5. NAME SELECTION PRIORITY:
   - Use the most complete/formal name (full name over nickname)
   - Use official names over informal references
   - Use specific names over generic terms

5. RELATIONSHIP EXTRACTION: Ensure both subject and object are specific entity names (no pronouns)

{examples_section}

IMPORTANT: Extract relationships with resolved entity names, not pronouns or variations.
"""
        return prompt

    def _group_entities_by_category(self) -> Dict[str, List[str]]:
        """Group entity types by category using dynamic mapping from constants."""
        # Get category mapping from constants - no hardcoded categories here!
        category_mapping = get_entity_category_mapping()

        # Initialize groups dynamically based on available categories
        groups = {category: [] for category in category_mapping.keys()}
        groups["Other"] = []  # Fallback category

        # Dynamic categorization - works for any number of categories
        for entity_type in self.focus_entities:
            categorized = False
            for category_name, getter_func in category_mapping.items():
                if entity_type in getter_func():
                    groups[category_name].append(entity_type)
                    categorized = True
                    break

            if not categorized:
                groups["Other"].append(entity_type)

        # Remove empty groups
        return {k: v for k, v in groups.items() if v}

    def _group_relationships_by_category(self) -> Dict[str, List[str]]:
        """Group relationship types by category using dynamic mapping from constants."""
        # Get category mapping from constants - no hardcoded categories here!
        category_mapping = get_relationship_category_mapping()

        # Initialize groups dynamically based on available categories
        groups = {category: [] for category in category_mapping.keys()}
        groups["Other Relationships"] = []  # Fallback category

        # Dynamic categorization - works for any number of categories
        for rel_type in self.focus_relationships:
            categorized = False
            for category_name, getter_func in category_mapping.items():
                if rel_type in getter_func():
                    groups[category_name].append(rel_type)
                    categorized = True
                    break

            if not categorized:
                groups["Other Relationships"].append(rel_type)

        # Remove empty groups
        return {k: v for k, v in groups.items() if v}

    def _format_entity_types(self, entity_groups: Dict[str, List[str]]) -> str:
        """Format entity types for the prompt."""
        sections = ["ENTITY TYPES to identify:"]

        for category, entities in entity_groups.items():
            if entities:
                sections.append(f"\n{category}:")
                sections.append(", ".join(entities))

        return "\n".join(sections)

    def _format_relationship_types(self, relationship_groups: Dict[str, List[str]]) -> str:
        """Format relationship types for the prompt."""
        sections = ["RELATIONSHIP TYPES to extract:"]

        for category, relationships in relationship_groups.items():
            if relationships:
                sections.append(f"\n{category}:")
                sections.append(", ".join(relationships))

        return "\n".join(sections)

    def _generate_examples(self) -> str:
        """Generate examples based on common patterns."""
        patterns = get_common_entity_relationship_patterns()

        examples = ["EXAMPLES:"]

        # Filter patterns to only include those in our focus types
        relevant_patterns = [
            pattern for pattern in patterns
            if pattern[1] in self.focus_relationships
        ]

        # Take first 5 relevant patterns
        for subject_type, rel_type, object_type in relevant_patterns[:5]:
            # Generate example names based on types
            subject_example = self._get_example_name(subject_type)
            object_example = self._get_example_name(object_type)

            examples.append(f'- "{subject_example}" -> "{rel_type}" -> "{object_example}"')

        return "\n".join(examples)

    def _get_example_name(self, entity_type: str) -> str:
        """Get example name for an entity type."""
        examples = {
            "Person": "John Smith (Manager)",  # Unified Person type with role context
            "Project": "Project Alpha",
            "Initiative": "Digital Transformation Initiative",
            "Program": "Customer Experience Program",
            "Company": "TechCorp",
            "Department": "Engineering Department",
            "Team": "Marketing Team",
            "System": "CRM System",
            "Application": "Customer Portal",
            "Platform": "ERP Platform",
            "Database": "Customer Database",
            "Document": "Project Report",
            "Report": "Status Report",
            "Proposal": "Budget Proposal"
        }

        return examples.get(entity_type, f"Example {entity_type}")

    def _get_specialized_summarization_instruction(self, document_type: str) -> str:
        """Get specialized instruction for document type."""
        instructions = {
            "report": "Focus on extracting the executive summary, key findings, recommendations, and methodology.",
            "proposal": "Focus on the proposed solution, objectives, timeline, budget considerations, and key stakeholders.",
            "email": "Focus on the main purpose, action items, decisions made, and people involved.",
            "contract": "Focus on the parties involved, main terms, obligations, deliverables, and timeline.",
            "policy": "Focus on the policy objectives, scope, key requirements, compliance aspects, and affected stakeholders.",
            "procedure": "Focus on the process steps, roles and responsibilities, inputs/outputs, and quality requirements.",
            "meeting_notes": "Focus on attendees, key decisions, action items, discussion topics, and next steps."
        }

        return instructions.get(document_type.lower(), "Analyze the document structure and content to identify key information.")

    def get_schema_description(self) -> str:
        """Get the schema description for structured output."""
        return """
[
    {
        "subject": "string - the source entity (specific name)",
        "predicate": "string - the relationship type",
        "object": "string - the target entity (specific name)",
        "subject_type": "string - entity type of subject (e.g., Company, Tool, Person)",
        "object_type": "string - entity type of object (e.g., Company, Tool, Person)",
        "confidence_score": "float - confidence in this relationship (0.0-1.0)",
        "context": "string - brief context where this was found",
        "source_sentence": "string - the sentence where this relationship was mentioned"
    }
]
"""

    def update_focus_types(
        self,
        focus_entities: List[str] = None,
        focus_relationships: List[str] = None
    ):
        """Update the focus types for prompt generation."""
        if focus_entities is not None:
            self.focus_entities = focus_entities
        if focus_relationships is not None:
            self.focus_relationships = focus_relationships


# Factory functions for common configurations
def create_full_prompt_generator() -> PromptGenerator:
    """Create a prompt generator with all available constants."""
    return PromptGenerator(use_all_constants=True)


def create_basic_prompt_generator() -> PromptGenerator:
    """Create a prompt generator with basic entity and relationship types."""
    basic_entities = ["Person", "Project", "Company", "Department", "System"]
    basic_relationships = ["involved_in", "mentions", "works_for", "manages", "reports_to"]

    return PromptGenerator(
        focus_entities=basic_entities,
        focus_relationships=basic_relationships,
        use_all_constants=False
    )


def create_project_focused_generator() -> PromptGenerator:
    """Create a prompt generator focused on project-related extraction."""
    project_entities = list(get_person_related_types()) + list(get_project_related_types())

    # Get project relationships from the category mapping
    category_mapping = get_relationship_category_mapping()
    project_relationships = list(category_mapping["Project Relationships"]())

    return PromptGenerator(
        focus_entities=project_entities,
        focus_relationships=project_relationships,
        use_all_constants=False
    )


def create_feedback_focused_generator() -> PromptGenerator:
    """Create a prompt generator focused on customer feedback and sentiment analysis."""
    from ..constants.entities import get_feedback_related_types
    from ..constants.relationships import get_feedback_relationships

    # Combine feedback entities with people (customers) and products
    feedback_entities = list(get_feedback_related_types()) + list(get_person_related_types())
    feedback_relationships = list(get_feedback_relationships())

    return PromptGenerator(
        focus_entities=feedback_entities,
        focus_relationships=feedback_relationships,
        use_all_constants=False
    )
