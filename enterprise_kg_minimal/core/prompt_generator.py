"""
Dynamic Prompt Generator

This module generates LLM prompts dynamically based on the constants
defined in the entities and relationships files. This ensures prompts
are always up-to-date with the latest entity and relationship types.
"""

from typing import List, Set, Dict, Any
from ..constants.entities import (
    get_all_entity_types,
    get_person_related_types,
    get_project_related_types,
    get_entity_category_mapping
)
from ..constants.relationships import (
    get_all_relationship_types,
    get_relationship_category_mapping,
    get_common_entity_relationship_patterns
)


class PromptGenerator:
    """
    Generates dynamic prompts for entity and relationship extraction
    based on the constants defined in the system.
    """

    def __init__(
        self,
        focus_entities: List[str] = None,
        focus_relationships: List[str] = None,
        use_all_constants: bool = True
    ):
        """
        Initialize the prompt generator.

        Args:
            focus_entities: Specific entity types to focus on
            focus_relationships: Specific relationship types to focus on
            use_all_constants: Whether to use all available constants
        """
        if use_all_constants:
            self.focus_entities = list(get_all_entity_types())
            self.focus_relationships = list(get_all_relationship_types())
        else:
            self.focus_entities = focus_entities or ["Person", "Project", "Company"]
            self.focus_relationships = focus_relationships or ["involved_in", "mentions"]

    def generate_relationship_extraction_prompt(self, content: str) -> str:
        """
        Generate a dynamic prompt for relationship extraction.

        Args:
            content: Document content to extract from

        Returns:
            Complete prompt for LLM
        """
        # Group entities by category for better organization
        entity_groups = self._group_entities_by_category()
        relationship_groups = self._group_relationships_by_category()

        # Generate entity types section
        entity_section = self._format_entity_types(entity_groups)

        # Generate relationship types section
        relationship_section = self._format_relationship_types(relationship_groups)

        # Generate examples based on common patterns
        examples_section = self._generate_examples()

        prompt = f"""
Please extract entity relationships from this enterprise document:

{content}

{entity_section}

{relationship_section}

EXTRACTION GUIDELINES:
1. Focus on business-relevant entities and relationships
2. PERSON ENTITIES: Use "Person" type for all people, regardless of their role:
   - Extract as "Person" type: employees, managers, executives, directors, etc.
   - Include role context in the entity name when clear: "John Smith (CEO)", "Sarah Johnson (Manager)"
   - The system will automatically extract role and persona properties from context
3. COREFERENCE RESOLUTION: When the same entity is mentioned multiple ways, use the most complete/formal name consistently:
   - "John Smith" and "John" and "Mr. Smith" → use "John Smith"
   - "IBM" and "International Business Machines" → use "International Business Machines"
   - "CRM System" and "the system" and "it" → use "CRM System"
4. PRONOUN RESOLUTION: Replace pronouns with their actual entity names:
   - "he" → the person's actual name
   - "she" → the person's actual name
   - "it" → the system/product/company name
   - "they" → the team/organization name
5. Use only the predefined relationship types listed above
6. Ensure subject and object are clear, specific entity names (not pronouns)
7. Extract relationships that are explicitly stated or clearly implied
8. Prioritize relationships between people, projects, organizations, and systems
9. CONSISTENCY: Use the same entity name throughout the document for the same real-world entity

{examples_section}

Extract all relevant relationships following this pattern.
"""
        return prompt

    def generate_entity_extraction_prompt(self, content: str) -> str:
        """
        Generate a dynamic prompt for entity-only extraction.

        Args:
            content: Document content to extract from

        Returns:
            Complete prompt for LLM
        """
        entity_groups = self._group_entities_by_category()
        entity_section = self._format_entity_types(entity_groups)

        prompt = f"""
Please extract all relevant entities from this enterprise document:

{content}

{entity_section}

EXTRACTION GUIDELINES:
1. Identify specific, named entities (not generic references)
2. COREFERENCE RESOLUTION: Use consistent naming for the same entity:
   - If "John Smith" appears later as "John" or "Mr. Smith", always use "John Smith"
   - If "IBM" appears as "International Business Machines", use the full name
   - Replace pronouns (he, she, it, they) with actual entity names
3. Use the most specific entity type available
4. Provide a brief description if the entity's role/purpose is clear
5. Focus on business-relevant entities
6. CONSISTENCY: Same real-world entity = same name in extraction

EXAMPLES:
- Entity(name="John Smith", entity_type="Person", description="Project Manager")
- Entity(name="CRM System", entity_type="System", description="Customer relationship management platform")
- Entity(name="Q4 Marketing Campaign", entity_type="Campaign", description="Fourth quarter marketing initiative")

Extract all relevant entities following this pattern.
"""
        return prompt

    def generate_summarization_prompt(self, content: str, document_type: str = None) -> str:
        """
        Generate a dynamic prompt for document summarization.

        Args:
            content: Document content to summarize
            document_type: Optional document type for specialized prompts

        Returns:
            Complete prompt for LLM
        """
        base_prompt = f"""
Please analyze this enterprise document and provide a comprehensive summary:

{content}

Focus on extracting the main title, key points, document type, and important topics discussed.
Be concise but thorough. If the document type is unclear, make your best assessment based on content and structure.
"""

        if document_type:
            specialized_instruction = self._get_specialized_summarization_instruction(document_type)
            base_prompt += f"\n\nSpecialized Instructions for {document_type}:\n{specialized_instruction}"

        return base_prompt

    def generate_feedback_sentiment_prompt(self, content: str) -> str:
        """
        Generate a specialized prompt for customer feedback sentiment analysis.

        Args:
            content: Customer feedback content to analyze

        Returns:
            Complete prompt for LLM with sentiment analysis focus
        """
        prompt = f"""
Please analyze this customer feedback and extract entities, relationships, and sentiment information:

{content}

FEEDBACK ANALYSIS REQUIREMENTS:

1. SENTIMENT CLASSIFICATION:
   - Overall sentiment: positive, negative, neutral, or mixed
   - Sentiment score: -1.0 (very negative) to 1.0 (very positive)
   - Confidence level: 0.0 to 1.0

2. ENTITY EXTRACTION (Focus on feedback-related entities):
   - Customer: The person providing feedback
   - Product/Service: What is being reviewed
   - Feedback: The feedback content itself
   - Sentiment: Emotional expressions
   - Complaint/Compliment: Specific issues or praise
   - Rating: Any numerical or categorical ratings

3. RELATIONSHIP EXTRACTION (Focus on feedback relationships):
   - provides_feedback: Customer provides feedback about product
   - expresses_sentiment: Feedback expresses specific sentiment
   - relates_to_product: Feedback relates to specific product/service
   - rates: Customer rates product/service
   - complains_about: Customer complains about specific issue
   - praises: Customer praises specific aspect
   - experiences: Customer experiences issue or benefit

4. KEY INSIGHTS:
   - Identify specific issues mentioned
   - Identify positive aspects highlighted
   - Determine if action is required
   - Assess priority level (low, medium, high, critical)

EXAMPLES:
- "John Smith" -> "provides_feedback" -> "CRM Software"
- "Feedback" -> "expresses_sentiment" -> "Positive"
- "Sarah Johnson" -> "praises" -> "Customer Support"
- "Bug Report" -> "relates_to_product" -> "Mobile App"

Extract all relevant entities and relationships following this pattern, with special attention to sentiment indicators and customer experience details.
"""
        return prompt

    def generate_coreference_aware_prompt(self, content: str, previous_entities: List[str] = None) -> str:
        """
        Generate a coreference-aware prompt that considers previously mentioned entities.

        Args:
            content: Document content to extract from
            previous_entities: List of entities mentioned in previous chunks

        Returns:
            Complete prompt for LLM with coreference resolution instructions
        """
        # Group entities by category for better organization
        entity_groups = self._group_entities_by_category()
        relationship_groups = self._group_relationships_by_category()

        # Generate entity types section
        entity_section = self._format_entity_types(entity_groups)

        # Generate relationship types section
        relationship_section = self._format_relationship_types(relationship_groups)

        # Generate examples based on common patterns
        examples_section = self._generate_examples()

        # Add previous entities context if available
        previous_entities_section = ""
        if previous_entities:
            previous_entities_section = f"""
PREVIOUSLY MENTIONED ENTITIES (use these exact names if referring to the same entities):
{', '.join(previous_entities[:20])}  # Show first 20 to avoid prompt bloat

"""

        prompt = f"""
Please extract entity relationships from this enterprise document with COREFERENCE RESOLUTION:

{content}

{previous_entities_section}{entity_section}

{relationship_section}

COREFERENCE RESOLUTION GUIDELINES:
1. PERSON ENTITIES: Use "Person" type for all people, regardless of their role:
   - Extract as "Person" type: employees, managers, executives, directors, etc.
   - Include role context in the entity name when clear: "John Smith (CEO)", "Sarah Johnson (Manager)"
   - The system will automatically extract role and persona properties from context

2. ENTITY CONSISTENCY: If the same real-world entity is mentioned multiple ways, use ONE consistent name:
   - "John Smith", "John", "Mr. Smith", "he" → ALL become "John Smith"
   - "IBM", "International Business Machines", "the company" → ALL become "International Business Machines"
   - "CRM System", "the system", "it" → ALL become "CRM System"

3. PRONOUN RESOLUTION: Replace ALL pronouns with actual entity names:
   - "John Smith manages the project. He is experienced." → "John Smith manages the project. John Smith is experienced."
   - "The system crashed. It needs repair." → "CRM System crashed. CRM System needs repair."

4. CROSS-REFERENCE CHECK: If an entity was mentioned in previous chunks, use the SAME name

5. NAME SELECTION PRIORITY:
   - Use the most complete/formal name (full name over nickname)
   - Use official names over informal references
   - Use specific names over generic terms

5. RELATIONSHIP EXTRACTION: Ensure both subject and object are specific entity names (no pronouns)

{examples_section}

IMPORTANT: Extract relationships with resolved entity names, not pronouns or variations.
"""
        return prompt

    def _group_entities_by_category(self) -> Dict[str, List[str]]:
        """Group entity types by category using dynamic mapping from constants."""
        # Get category mapping from constants - no hardcoded categories here!
        category_mapping = get_entity_category_mapping()

        # Initialize groups dynamically based on available categories
        groups = {category: [] for category in category_mapping.keys()}
        groups["Other"] = []  # Fallback category

        # Dynamic categorization - works for any number of categories
        for entity_type in self.focus_entities:
            categorized = False
            for category_name, getter_func in category_mapping.items():
                if entity_type in getter_func():
                    groups[category_name].append(entity_type)
                    categorized = True
                    break

            if not categorized:
                groups["Other"].append(entity_type)

        # Remove empty groups
        return {k: v for k, v in groups.items() if v}

    def _group_relationships_by_category(self) -> Dict[str, List[str]]:
        """Group relationship types by category using dynamic mapping from constants."""
        # Get category mapping from constants - no hardcoded categories here!
        category_mapping = get_relationship_category_mapping()

        # Initialize groups dynamically based on available categories
        groups = {category: [] for category in category_mapping.keys()}
        groups["Other Relationships"] = []  # Fallback category

        # Dynamic categorization - works for any number of categories
        for rel_type in self.focus_relationships:
            categorized = False
            for category_name, getter_func in category_mapping.items():
                if rel_type in getter_func():
                    groups[category_name].append(rel_type)
                    categorized = True
                    break

            if not categorized:
                groups["Other Relationships"].append(rel_type)

        # Remove empty groups
        return {k: v for k, v in groups.items() if v}

    def _format_entity_types(self, entity_groups: Dict[str, List[str]]) -> str:
        """Format entity types for the prompt."""
        sections = ["ENTITY TYPES to identify:"]

        for category, entities in entity_groups.items():
            if entities:
                sections.append(f"\n{category}:")
                sections.append(", ".join(entities))

        return "\n".join(sections)

    def _format_relationship_types(self, relationship_groups: Dict[str, List[str]]) -> str:
        """Format relationship types for the prompt."""
        sections = ["RELATIONSHIP TYPES to extract:"]

        for category, relationships in relationship_groups.items():
            if relationships:
                sections.append(f"\n{category}:")
                sections.append(", ".join(relationships))

        return "\n".join(sections)

    def _generate_examples(self) -> str:
        """Generate examples based on common patterns."""
        patterns = get_common_entity_relationship_patterns()

        examples = ["EXAMPLES:"]

        # Filter patterns to only include those in our focus types
        relevant_patterns = [
            pattern for pattern in patterns
            if pattern[1] in self.focus_relationships
        ]

        # Take first 5 relevant patterns
        for subject_type, rel_type, object_type in relevant_patterns[:5]:
            # Generate example names based on types
            subject_example = self._get_example_name(subject_type)
            object_example = self._get_example_name(object_type)

            examples.append(f'- "{subject_example}" -> "{rel_type}" -> "{object_example}"')

        return "\n".join(examples)

    def _get_example_name(self, entity_type: str) -> str:
        """Get example name for an entity type."""
        examples = {
            "Person": "John Smith (Manager)",  # Unified Person type with role context
            "Project": "Project Alpha",
            "Initiative": "Digital Transformation Initiative",
            "Program": "Customer Experience Program",
            "Company": "TechCorp",
            "Department": "Engineering Department",
            "Team": "Marketing Team",
            "System": "CRM System",
            "Application": "Customer Portal",
            "Platform": "ERP Platform",
            "Database": "Customer Database",
            "Document": "Project Report",
            "Report": "Status Report",
            "Proposal": "Budget Proposal"
        }

        return examples.get(entity_type, f"Example {entity_type}")

    def _get_specialized_summarization_instruction(self, document_type: str) -> str:
        """Get specialized instruction for document type."""
        instructions = {
            "report": "Focus on extracting the executive summary, key findings, recommendations, and methodology.",
            "proposal": "Focus on the proposed solution, objectives, timeline, budget considerations, and key stakeholders.",
            "email": "Focus on the main purpose, action items, decisions made, and people involved.",
            "contract": "Focus on the parties involved, main terms, obligations, deliverables, and timeline.",
            "policy": "Focus on the policy objectives, scope, key requirements, compliance aspects, and affected stakeholders.",
            "procedure": "Focus on the process steps, roles and responsibilities, inputs/outputs, and quality requirements.",
            "meeting_notes": "Focus on attendees, key decisions, action items, discussion topics, and next steps."
        }

        return instructions.get(document_type.lower(), "Analyze the document structure and content to identify key information.")

    def get_schema_description(self) -> str:
        """Get the schema description for structured output."""
        return """
[
    {
        "subject": "string - the source entity (specific name)",
        "predicate": "string - the relationship type",
        "object": "string - the target entity (specific name)",
        "subject_type": "string - entity type of subject (e.g., Company, Tool, Person)",
        "object_type": "string - entity type of object (e.g., Company, Tool, Person)",
        "confidence_score": "float - confidence in this relationship (0.0-1.0)",
        "context": "string - brief context where this was found",
        "source_sentence": "string - the sentence where this relationship was mentioned"
    }
]
"""

    def update_focus_types(
        self,
        focus_entities: List[str] = None,
        focus_relationships: List[str] = None
    ):
        """Update the focus types for prompt generation."""
        if focus_entities is not None:
            self.focus_entities = focus_entities
        if focus_relationships is not None:
            self.focus_relationships = focus_relationships

    def generate_csv_extraction_prompt(
        self,
        csv_content: str,
        headers: List[str],
        schema_inference: Dict[str, Any] = None
    ) -> str:
        """
        Generate a specialized prompt for CSV entity extraction.

        Args:
            csv_content: CSV content to extract from
            headers: Column headers
            schema_inference: Optional schema inference results

        Returns:
            Complete prompt for CSV entity extraction
        """
        # Group entities by category for better organization
        entity_groups = self._group_entities_by_category()
        relationship_groups = self._group_relationships_by_category()

        # Generate entity types section
        entity_section = self._format_entity_types(entity_groups)

        # Generate relationship types section
        relationship_section = self._format_relationship_types(relationship_groups)

        # Generate CSV-specific instructions
        csv_instructions = self._generate_csv_instructions(headers, schema_inference)

        # Generate CSV examples
        csv_examples = self._generate_csv_examples(headers)

        prompt = f"""
Please extract entity relationships from this CSV data:

{csv_content}

COLUMN HEADERS: {', '.join(headers)}

{csv_instructions}

{entity_section}

{relationship_section}

{csv_examples}

IMPORTANT CSV-SPECIFIC INSTRUCTIONS:
1. Each row represents a data record - extract entities and relationships from each row
2. Use column headers to understand the context and meaning of values
3. Treat each unique value in identifier columns as a separate entity
4. Create relationships between entities based on the row context
5. If a column appears to be a foreign key (ends with _id, _key, etc.), create relationships
6. Maintain data consistency - same values should map to same entities across rows

OUTPUT FORMAT:
For each row, extract relationships in this JSON format:
{{
  "row_index": <row_number>,
  "entities": [
    {{"name": "<entity_name>", "type": "<entity_type>"}},
    ...
  ],
  "relationships": [
    {{"subject": "<entity1>", "predicate": "<relationship>", "object": "<entity2>", "subject_type": "<type1>", "object_type": "<type2>"}},
    ...
  ]
}}

Extract all relevant relationships following this pattern.
"""
        return prompt

    def generate_csv_schema_analysis_prompt(self, headers: List[str], sample_data: List[List[str]]) -> str:
        """
        Generate a prompt for CSV schema analysis and entity type inference.

        Args:
            headers: Column headers
            sample_data: Sample rows of data

        Returns:
            Prompt for schema analysis
        """
        # Format sample data for display
        sample_display = []
        for i, row in enumerate(sample_data[:5]):  # Show first 5 rows
            sample_display.append(f"Row {i+1}: {dict(zip(headers, row))}")

        sample_text = '\n'.join(sample_display)

        prompt = f"""
Analyze this CSV data structure and suggest the best entity-relationship schema:

COLUMN HEADERS: {', '.join(headers)}

SAMPLE DATA:
{sample_text}

Please analyze each column and suggest:

1. COLUMN CLASSIFICATION:
   For each column, determine if it is:
   - Entity Identifier (unique IDs, primary keys, emails, etc.)
   - Entity Attribute (descriptions, names, properties, etc.)
   - Foreign Key (references to other entities)
   - Relationship Indicator (columns that suggest relationships)
   - Metadata (dates, timestamps, etc.)

2. ENTITY TYPE INFERENCE:
   Based on the column names and data patterns, suggest what entity types this CSV represents.
   Consider these available entity types: {', '.join(self.focus_entities)}

3. RELATIONSHIP SUGGESTIONS:
   Identify potential relationships between entities based on:
   - Foreign key columns
   - Column naming patterns
   - Data relationships

4. SCHEMA CONFIDENCE:
   Rate your confidence in this schema suggestion (0.0 to 1.0)

OUTPUT FORMAT:
{{
  "primary_entity_type": "<main_entity_type>",
  "column_analysis": {{
    "<column_name>": {{
      "classification": "<classification>",
      "inferred_entity_type": "<entity_type_or_null>",
      "is_identifier": <boolean>,
      "is_foreign_key": <boolean>,
      "suggested_relationship": "<relationship_or_null>"
    }},
    ...
  }},
  "suggested_relationships": [
    {{"subject_type": "<type1>", "predicate": "<relationship>", "object_type": "<type2>"}},
    ...
  ],
  "confidence_score": <0.0_to_1.0>
}}

Provide your analysis in this JSON format.
"""
        return prompt

    def _generate_csv_instructions(self, headers: List[str], schema_inference: Dict[str, Any] = None) -> str:
        """Generate CSV-specific extraction instructions."""
        instructions = ["CSV DATA PROCESSING INSTRUCTIONS:"]

        # Add header-specific guidance
        instructions.append(f"- This CSV has {len(headers)} columns: {', '.join(headers)}")

        # Add schema-based guidance if available
        if schema_inference:
            if schema_inference.get('primary_entity_type'):
                instructions.append(f"- Primary entity type appears to be: {schema_inference['primary_entity_type']}")

            if schema_inference.get('identifier_columns'):
                instructions.append(f"- Identifier columns: {', '.join(schema_inference['identifier_columns'])}")

            if schema_inference.get('relationship_columns'):
                instructions.append(f"- Relationship columns: {', '.join(schema_inference['relationship_columns'])}")

        # Add general CSV processing guidance
        instructions.extend([
            "- Each row represents a complete data record",
            "- Extract entities from identifier and name columns",
            "- Create relationships based on foreign key columns and row context",
            "- Maintain consistency across rows for the same entity values"
        ])

        return '\n'.join(instructions)

    def _generate_csv_examples(self, headers: List[str]) -> str:
        """Generate CSV-specific examples based on headers."""
        examples = ["CSV EXTRACTION EXAMPLES:"]

        # Generate examples based on common column patterns
        if any('employee' in h.lower() or 'user' in h.lower() for h in headers):
            examples.append('- If row has employee_id="E123", name="John Smith", department="Engineering"')
            examples.append('  Extract: Person entity "John Smith", Department entity "Engineering"')
            examples.append('  Relationship: "John Smith" -> "WORKS_IN" -> "Engineering"')

        if any('project' in h.lower() for h in headers):
            examples.append('- If row has project_id="P456", name="AI Initiative", manager_id="E123"')
            examples.append('  Extract: Project entity "AI Initiative", Person entity from manager_id')
            examples.append('  Relationship: "AI Initiative" -> "MANAGED_BY" -> [Person from manager_id]')

        if any('task' in h.lower() or 'ticket' in h.lower() for h in headers):
            examples.append('- If row has task_id="T789", title="Fix Bug", assignee="John Smith"')
            examples.append('  Extract: Task entity "Fix Bug", Person entity "John Smith"')
            examples.append('  Relationship: "Fix Bug" -> "ASSIGNED_TO" -> "John Smith"')

        return '\n'.join(examples)


# Factory functions for common configurations
def create_full_prompt_generator() -> PromptGenerator:
    """Create a prompt generator with all available constants."""
    return PromptGenerator(use_all_constants=True)


def create_basic_prompt_generator() -> PromptGenerator:
    """Create a prompt generator with basic entity and relationship types."""
    basic_entities = ["Person", "Project", "Company", "Department", "System"]
    basic_relationships = ["involved_in", "mentions", "works_for", "manages", "reports_to"]

    return PromptGenerator(
        focus_entities=basic_entities,
        focus_relationships=basic_relationships,
        use_all_constants=False
    )


def create_project_focused_generator() -> PromptGenerator:
    """Create a prompt generator focused on project-related extraction."""
    project_entities = list(get_person_related_types()) + list(get_project_related_types())

    # Get project relationships from the category mapping
    category_mapping = get_relationship_category_mapping()
    project_relationships = list(category_mapping["Project Relationships"]())

    return PromptGenerator(
        focus_entities=project_entities,
        focus_relationships=project_relationships,
        use_all_constants=False
    )


def create_feedback_focused_generator() -> PromptGenerator:
    """Create a prompt generator focused on customer feedback and sentiment analysis."""
    from ..constants.entities import get_feedback_related_types
    from ..constants.relationships import get_feedback_relationships

    # Combine feedback entities with people (customers) and products
    feedback_entities = list(get_feedback_related_types()) + list(get_person_related_types())
    feedback_relationships = list(get_feedback_relationships())

    return PromptGenerator(
        focus_entities=feedback_entities,
        focus_relationships=feedback_relationships,
        use_all_constants=False
    )


def create_csv_focused_generator() -> PromptGenerator:
    """Create a prompt generator optimized for CSV data extraction."""
    # CSV data often contains structured organizational and project data
    csv_entities = [
        "Person", "Project", "Company", "Department", "Team",
        "Task", "Issue", "System", "Product", "Service",
        "Client", "Vendor", "Partner", "Role", "Position"
    ]

    csv_relationships = [
        "WORKS_FOR", "MANAGES", "REPORTS_TO", "MEMBER_OF", "LEADS",
        "ASSIGNED_TO", "OWNS", "BELONGS_TO", "PART_OF", "RESPONSIBLE_FOR",
        "COLLABORATES_WITH", "DEPENDS_ON", "USES", "PROVIDES", "SUPPORTS"
    ]

    return PromptGenerator(
        focus_entities=csv_entities,
        focus_relationships=csv_relationships,
        use_all_constants=False
    )
