"""
Main Document Processor for Enterprise KG Minimal

This module provides the main importable function that processes documents
and creates chunk-based knowledge graphs.
"""

import logging
from typing import List, Dict, Any, Optional

from ..llm.client import LLMClient
from ..storage.neo4j_client import Neo4j<PERSON>lient, Neo4jConnection
from ..constants.schemas import <PERSON>tityRelationship, CSVRowChunk
from .prompt_generator import create_full_prompt_generator, create_feedback_focused_generator, create_csv_focused_generator
from .chunking_engine import ChunkingEngine, ChunkingStrategy
from .graph_builder import GraphBuilder
from .coreference_resolver import CoreferenceResolver
from .csv_analyzer import CSVAnalyzer
from .csv_schema_engine import CSVSchemaEngine
from .csv_graph_builder import CSVGraphBuilder

logger = logging.getLogger(__name__)


def detect_content_type(file_content: str, file_id: str) -> str:
    """
    Detect the type of content to determine appropriate processing strategy.

    Args:
        file_content: The document content to analyze
        file_id: The file identifier (may contain hints)

    Returns:
        Content type: 'csv', 'feedback', 'project', 'general'
    """
    content_lower = file_content.lower()
    file_id_lower = file_id.lower()

    # Check for CSV content first
    if is_csv_content(file_content, file_id):
        return 'csv'

    # Check for feedback-related keywords in file_id
    feedback_id_keywords = ['feedback', 'review', 'complaint', 'survey', 'rating', 'customer', 'satisfaction']
    if any(keyword in file_id_lower for keyword in feedback_id_keywords):
        return 'feedback'

    # Check for feedback-related keywords in content
    feedback_content_keywords = [
        'feedback', 'review', 'complaint', 'satisfied', 'disappointed', 'recommend',
        'customer service', 'support', 'rating', 'stars', 'experience', 'opinion',
        'terrible', 'excellent', 'love', 'hate', 'frustrated', 'happy',
        'issue', 'problem', 'bug', 'feature request', 'improvement'
    ]

    feedback_score = sum(1 for keyword in feedback_content_keywords if keyword in content_lower)

    # Check for project-related keywords
    project_keywords = [
        'project', 'initiative', 'milestone', 'deadline', 'deliverable',
        'stakeholder', 'requirements', 'scope', 'timeline', 'budget'
    ]

    project_score = sum(1 for keyword in project_keywords if keyword in content_lower)

    # Determine content type based on keyword density
    # If both scores are >= 3, choose the higher score
    if feedback_score >= 3 and project_score >= 3:
        if feedback_score > project_score:
            return 'feedback'
        else:
            return 'project'
    elif feedback_score >= 3:  # At least 3 feedback-related keywords
        return 'feedback'
    elif project_score >= 3:  # At least 3 project-related keywords
        return 'project'
    else:
        return 'general'


def is_csv_content(content: str, file_id: str) -> bool:
    """
    Determine if the content is CSV data.

    Args:
        content: Content to analyze
        file_id: File identifier

    Returns:
        True if content appears to be CSV
    """
    # Check file extension
    if file_id.lower().endswith('.csv'):
        return True

    # Check content patterns
    lines = content.strip().split('\n')
    if len(lines) < 2:
        return False

    # Check for CSV-like structure
    first_line = lines[0]
    delimiters = [',', ';', '\t', '|']

    for delimiter in delimiters:
        if delimiter in first_line:
            # Check if multiple lines have the same number of fields
            first_line_fields = len(first_line.split(delimiter))
            if first_line_fields > 1:
                # Check a few more lines
                matching_lines = 1
                for line in lines[1:min(6, len(lines))]:  # Check up to 5 more lines
                    if len(line.split(delimiter)) == first_line_fields:
                        matching_lines += 1

                # If most lines have the same field count, likely CSV
                if matching_lines >= min(3, len(lines)):
                    return True

    return False


def create_csv_chunks(
    file_content: str,
    file_id: str,
    schema_inference,
    chunking_strategy: str,
    chunk_size: int
) -> List[CSVRowChunk]:
    """
    Create CSV chunks from file content.

    Args:
        file_content: CSV content
        file_id: File identifier
        schema_inference: Schema inference results
        chunking_strategy: Chunking strategy to use
        chunk_size: Number of rows per chunk

    Returns:
        List of CSV row chunks
    """
    import csv
    from io import StringIO

    try:
        # Parse CSV content
        csv_file = StringIO(file_content)
        reader = csv.reader(csv_file)
        rows = list(reader)

        if len(rows) < 2:
            return []

        headers = rows[0]
        data_rows = rows[1:]

        chunks = []
        chunk_index = 0

        # Create chunks based on strategy
        if chunking_strategy == "csv_row_based":
            rows_per_chunk = 1
        else:  # csv_batch_based
            rows_per_chunk = chunk_size

        for i in range(0, len(data_rows), rows_per_chunk):
            chunk_rows = data_rows[i:i + rows_per_chunk]

            # Convert rows to dictionaries
            chunk_row_dicts = []
            for row in chunk_rows:
                # Pad row if it has fewer columns than headers
                padded_row = row + [''] * (len(headers) - len(row))
                row_dict = dict(zip(headers, padded_row))
                chunk_row_dicts.append(row_dict)

            # Create chunk
            chunk_id = f"{file_id}_csv_chunk_{chunk_index}"
            chunk = CSVRowChunk(
                chunk_id=chunk_id,
                file_id=file_id,
                start_row=i + 1,  # 1-based indexing
                end_row=i + len(chunk_rows),
                headers=headers,
                rows=chunk_row_dicts,
                schema_inference=schema_inference,
                chunk_index=chunk_index,
                total_chunks=0,  # Will be updated later
                processing_strategy=chunking_strategy
            )

            chunks.append(chunk)
            chunk_index += 1

        # Update total chunks count
        for chunk in chunks:
            chunk.total_chunks = len(chunks)

        return chunks

    except Exception as e:
        logger.error(f"Failed to create CSV chunks: {e}")
        return []


def process_csv_document(
    file_id: str,
    file_content: str,
    neo4j_uri: str = "bolt://localhost:7687",
    neo4j_user: str = "neo4j",
    neo4j_password: str = "password",
    neo4j_database: Optional[str] = None,
    llm_provider: str = "openai",
    llm_model: str = "gpt-4o",
    llm_api_key: Optional[str] = None,
    chunking_strategy: str = "csv_batch_based",
    chunk_size: int = 50,  # Number of rows per chunk
    use_llm_for_ambiguous: bool = True
) -> Dict[str, Any]:
    """
    Process a CSV document and create a knowledge graph.

    This function implements the hybrid CSV processing approach:
    1. Analyze CSV structure and infer schema
    2. Create row-based or batch-based chunks
    3. Extract entities and relationships using deterministic + LLM approach
    4. Build graph structure in Neo4j

    Args:
        file_id: Unique identifier for the CSV file
        file_content: CSV content to process
        neo4j_uri: Neo4j connection URI
        neo4j_user: Neo4j username
        neo4j_password: Neo4j password
        neo4j_database: Neo4j database name (optional)
        llm_provider: LLM provider for ambiguous data processing
        llm_model: LLM model name
        llm_api_key: LLM API key (optional)
        chunking_strategy: CSV chunking strategy (csv_row_based, csv_batch_based)
        chunk_size: Number of rows per chunk
        use_llm_for_ambiguous: Whether to use LLM for unclear patterns

    Returns:
        Dictionary containing processing results and statistics
    """
    logger.info(f"Processing CSV document {file_id} with {len(file_content)} characters")

    try:
        # Step 1: Initialize clients
        neo4j_conn = Neo4jConnection(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password,
            database=neo4j_database
        )
        neo4j_client = Neo4jClient(neo4j_conn)

        llm_client = LLMClient(
            provider=llm_provider,
            model=llm_model,
            api_key=llm_api_key
        ) if use_llm_for_ambiguous else None

        # Step 2: Analyze CSV structure
        logger.info("Analyzing CSV structure and inferring schema")
        csv_analyzer = CSVAnalyzer()
        schema_inference = csv_analyzer.analyze_csv_content(file_content, file_id)

        # Step 3: Enhance schema with template matching
        schema_engine = CSVSchemaEngine()
        enhanced_schema = schema_engine.suggest_schema(schema_inference)

        logger.info(f"Schema inference completed. Primary entity: {enhanced_schema.primary_entity_type}, "
                   f"Confidence: {enhanced_schema.schema_confidence:.2f}")

        # Step 4: Create CSV chunks
        logger.info(f"Creating CSV chunks using {chunking_strategy} strategy")
        csv_chunks = create_csv_chunks(file_content, file_id, enhanced_schema, chunking_strategy, chunk_size)

        if not csv_chunks:
            return {
                "success": False,
                "error": "No CSV chunks were created",
                "file_id": file_id,
                "chunks_created": 0
            }

        logger.info(f"Created {len(csv_chunks)} CSV chunks")

        # Step 5: Build graph using CSV graph builder
        logger.info("Building CSV knowledge graph")
        csv_graph_builder = CSVGraphBuilder(neo4j_client, llm_client, use_llm_for_ambiguous)
        graph_result = csv_graph_builder.build_csv_graph(file_id, csv_chunks, enhanced_schema)

        # Step 6: Prepare result
        result = {
            "success": graph_result.get("success", True),
            "file_id": file_id,
            "content_type": "csv",
            "schema_inference": {
                "primary_entity_type": enhanced_schema.primary_entity_type,
                "confidence": enhanced_schema.schema_confidence,
                "identifier_columns": enhanced_schema.identifier_columns,
                "relationship_columns": enhanced_schema.relationship_columns,
                "suggested_relationships": enhanced_schema.suggested_relationships
            },
            "chunks_created": len(csv_chunks),
            "chunks_processed": graph_result.get("chunks_processed", 0),
            "extractions_processed": graph_result.get("extractions_processed", 0),
            "total_entities": graph_result.get("entities_created", 0),
            "total_relationships": graph_result.get("relationships_created", 0),
            "processing_statistics": graph_result.get("statistics", {}),
            "csv_rows_processed": enhanced_schema.total_rows,
            "csv_columns": enhanced_schema.total_columns
        }

        if not graph_result.get("success", True):
            result["error"] = graph_result.get("error", "Unknown error in CSV processing")

        logger.info(f"CSV processing completed: {result['total_entities']} entities, "
                   f"{result['total_relationships']} relationships")
        return result

    except Exception as e:
        logger.error(f"Failed to process CSV document {file_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "file_id": file_id,
            "content_type": "csv",
            "chunks_created": 0
        }

    finally:
        # Clean up connections
        try:
            neo4j_client.close()
        except:
            pass


def process_document(
    file_id: str,
    file_content: str,
    neo4j_uri: str = "bolt://localhost:7687",
    neo4j_user: str = "neo4j",
    neo4j_password: str = "password",
    neo4j_database: Optional[str] = None,
    llm_provider: str = "openai",
    llm_model: str = "gpt-4o",
    llm_api_key: Optional[str] = None,
    chunking_strategy: str = "hybrid",
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    content_type: Optional[str] = None,
    enable_coreference_resolution: bool = True
) -> Dict[str, Any]:
    """
    Process a document and create a chunk-based knowledge graph.
    
    This function:
    1. Chunks the document content
    2. Extracts entities and relationships from each chunk
    3. Creates a graph structure: File → Chunks → Chunk Graphs
    4. Stores everything in Neo4j
    
    Args:
        file_id: Unique identifier for the file (will be used as node ID)
        file_content: Content of the document to process
        neo4j_uri: Neo4j connection URI
        neo4j_user: Neo4j username
        neo4j_password: Neo4j password
        neo4j_database: Neo4j database name (optional)
        llm_provider: LLM provider (openai, anthropic, etc.)
        llm_model: LLM model name
        llm_api_key: LLM API key (optional, will use environment variable)
        chunking_strategy: Chunking strategy (fixed_size, sentence_based, paragraph_based, semantic_based, hybrid)
        chunk_size: Target chunk size in characters
        chunk_overlap: Overlap between chunks in characters
        content_type: Manual override for content type ("csv", "feedback", "project", "general")
                     If None, will auto-detect based on content and file_id
        enable_coreference_resolution: Whether to enable coreference resolution across chunks

    Returns:
        Dictionary containing processing results, graph structure information,
        detected/used content type, and coreference resolution statistics
    """
    logger.info(f"Processing document {file_id} with {len(file_content)} characters")
    
    try:
        # Step 1: Initialize clients
        neo4j_conn = Neo4jConnection(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password,
            database=neo4j_database
        )
        neo4j_client = Neo4jClient(neo4j_conn)
        
        llm_client = LLMClient(
            provider=llm_provider,
            model=llm_model,
            api_key=llm_api_key
        )
        
        # Step 2: Initialize processing components
        chunking_engine = ChunkingEngine(
            strategy=ChunkingStrategy(chunking_strategy),
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        
        graph_builder = GraphBuilder(neo4j_client)

        # Step 2.5: Detect content type and choose appropriate processing pipeline
        if content_type is None:
            content_type = detect_content_type(file_content, file_id)
            logger.info(f"Auto-detected content type: {content_type}")
        else:
            logger.info(f"Using manual content type override: {content_type}")

        # Route CSV content to specialized CSV processing pipeline
        if content_type == 'csv':
            logger.info("Routing to CSV processing pipeline")
            return process_csv_document(
                file_id=file_id,
                file_content=file_content,
                neo4j_uri=neo4j_uri,
                neo4j_user=neo4j_user,
                neo4j_password=neo4j_password,
                neo4j_database=neo4j_database,
                llm_provider=llm_provider,
                llm_model=llm_model,
                llm_api_key=llm_api_key,
                chunking_strategy="csv_batch_based",  # Default CSV strategy
                chunk_size=min(chunk_size // 20, 50),  # Convert character size to row count
                use_llm_for_ambiguous=True
            )

        # Choose appropriate prompt generator for text content
        if content_type == 'feedback':
            prompt_generator = create_feedback_focused_generator()
            logger.info("Using feedback-focused prompt generator with sentiment analysis")
        else:
            prompt_generator = create_full_prompt_generator()
            logger.info("Using general prompt generator")
        
        # Step 3: Chunk the document
        logger.info(f"Chunking document using {chunking_strategy} strategy")
        chunks = chunking_engine.chunk_document(file_content, file_id)
        
        if not chunks:
            return {
                "success": False,
                "error": "No chunks were created from the document",
                "file_id": file_id,
                "chunks_created": 0
            }
        
        logger.info(f"Created {len(chunks)} chunks")
        
        # Step 4: Extract relationships from each chunk
        logger.info("Extracting entities and relationships from chunks")
        chunk_relationships = []

        # Initialize coreference resolver if enabled
        coreference_resolver = CoreferenceResolver() if enable_coreference_resolution else None
        previous_entities = []  # Track entities from previous chunks for coreference

        for i, chunk in enumerate(chunks):
            logger.info(f"Processing chunk {i+1}/{len(chunks)}: {chunk.metadata.chunk_id}")

            try:
                # Generate extraction prompt for this chunk based on content type
                if content_type == 'feedback':
                    prompt = prompt_generator.generate_feedback_sentiment_prompt(chunk.text)
                    logger.debug(f"Using feedback sentiment prompt for chunk {chunk.metadata.chunk_id}")
                elif enable_coreference_resolution and previous_entities:
                    # Use coreference-aware prompt with context from previous chunks
                    prompt = prompt_generator.generate_coreference_aware_prompt(chunk.text, previous_entities)
                    logger.debug(f"Using coreference-aware prompt for chunk {chunk.metadata.chunk_id}")
                else:
                    prompt = prompt_generator.generate_relationship_extraction_prompt(chunk.text)
                    logger.debug(f"Using general relationship extraction prompt for chunk {chunk.metadata.chunk_id}")

                schema_description = prompt_generator.get_schema_description()
                
                # Extract relationships using LLM
                response = llm_client.generate_structured_response(prompt, schema_description)
                
                # Parse relationships
                relationships = []
                if isinstance(response, list):
                    for rel_data in response:
                        try:
                            rel = EntityRelationship(**rel_data)
                            relationships.append(rel)
                        except Exception as e:
                            logger.warning(f"Failed to parse relationship in chunk {chunk.metadata.chunk_id}: {e}")
                
                chunk_relationships.append(relationships)
                logger.info(f"Extracted {len(relationships)} relationships from chunk {chunk.metadata.chunk_id}")

                # Update previous entities for coreference resolution
                if enable_coreference_resolution:
                    chunk_entities = set()
                    for rel in relationships:
                        chunk_entities.add(rel.subject)
                        chunk_entities.add(rel.object)
                    previous_entities.extend(list(chunk_entities))
                    # Keep only unique entities and limit size to prevent prompt bloat
                    previous_entities = list(set(previous_entities))[-50:]  # Keep last 50 unique entities

            except Exception as e:
                logger.error(f"Failed to extract relationships from chunk {chunk.metadata.chunk_id}: {e}")
                chunk_relationships.append([])  # Empty list for failed chunk
        
        # Step 5: Build the graph structure
        logger.info("Building graph structure in Neo4j")
        graph_result = graph_builder.build_document_graph(file_id, chunks, chunk_relationships)
        
        # Step 6: Perform coreference resolution if enabled
        coreference_stats = None
        if enable_coreference_resolution and coreference_resolver:
            logger.info("Performing coreference resolution across chunks")
            # Collect entities by chunk for resolution
            entities_by_chunk = {}
            for i, relationships in enumerate(chunk_relationships):
                chunk_id = chunks[i].metadata.chunk_id
                entities = []
                for rel in relationships:
                    entities.append({"name": rel.subject, "entity_type": rel.subject_type})
                    entities.append({"name": rel.object, "entity_type": rel.object_type})
                entities_by_chunk[chunk_id] = entities

            # Resolve coreferences
            resolution_result = coreference_resolver.resolve_entities(entities_by_chunk)
            coreference_stats = resolution_result['statistics']
            logger.info(f"Coreference resolution completed: {coreference_stats}")

        # Step 7: Prepare result
        result = {
            "success": True,
            "file_id": file_id,
            "content_type": content_type,
            "coreference_resolution_enabled": enable_coreference_resolution,
            "coreference_statistics": coreference_stats,
            "chunks_created": len(chunks),
            "chunks_processed": graph_result.successful_chunks,
            "chunks_failed": graph_result.failed_chunks,
            "total_entities": graph_result.total_entities,
            "total_relationships": graph_result.total_relationships,
            "file_node_created": graph_result.file_node_created,
            "contains_relationships_created": graph_result.contains_relationships_created,
            "chunk_details": [
                {
                    "chunk_id": result.chunk_id,
                    "chunk_index": result.chunk_index,
                    "entities_extracted": result.entities_extracted,
                    "relationships_extracted": result.relationships_extracted,
                    "graph_stored": result.graph_stored,
                    "error": result.error
                }
                for result in graph_result.chunk_results
            ]
        }
        
        logger.info(f"Document processing completed: {graph_result.successful_chunks}/{len(chunks)} chunks processed successfully")
        return result
        
    except Exception as e:
        logger.error(f"Failed to process document {file_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "file_id": file_id,
            "chunks_created": 0
        }
    
    finally:
        # Clean up connections
        try:
            neo4j_client.close()
        except:
            pass
