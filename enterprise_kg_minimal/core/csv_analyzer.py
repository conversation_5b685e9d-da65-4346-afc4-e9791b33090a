"""
CSV Column Profiling and Type Inference

This module provides functionality to analyze CSV files and infer
entity types, relationships, and data schemas for knowledge graph construction.
"""

import csv
import re
import logging
from typing import List, Dict, Any, Optional, Set, Tuple
from collections import Counter
from io import StringIO

from ..constants.csv_processing import (
    CSVColumnType, CSVEntityInferenceStrategy,
    get_csv_entity_patterns, get_csv_relationship_mappings,
    get_csv_value_patterns, get_csv_heuristics,
    normalize_column_name, match_column_pattern,
    infer_entity_type_from_column, get_relationship_from_column
)
from ..constants.schemas import CSVColumnProfile, CSVSchemaInference

logger = logging.getLogger(__name__)


class CSVAnalyzer:
    """
    Analyzes CSV files to infer entity types, relationships, and data schemas.
    
    This class implements the hybrid approach for CSV analysis:
    1. Column profiling and type inference
    2. Entity and relationship schema suggestion
    3. Statistical analysis for confidence scoring
    """
    
    def __init__(self, sample_size: int = 100, confidence_threshold: float = 0.7):
        """
        Initialize the CSV analyzer.
        
        Args:
            sample_size: Number of rows to sample for analysis
            confidence_threshold: Minimum confidence for schema suggestions
        """
        self.sample_size = sample_size
        self.confidence_threshold = confidence_threshold
        self.entity_patterns = get_csv_entity_patterns()
        self.relationship_mappings = get_csv_relationship_mappings()
        self.value_patterns = get_csv_value_patterns()
        self.heuristics = get_csv_heuristics()
    
    def analyze_csv_content(self, csv_content: str, file_name: str = "unknown.csv") -> CSVSchemaInference:
        """
        Analyze CSV content and infer schema.
        
        Args:
            csv_content: Raw CSV content as string
            file_name: Name of the CSV file
            
        Returns:
            CSVSchemaInference with complete analysis results
        """
        logger.info(f"Analyzing CSV file: {file_name}")
        
        # Parse CSV content
        csv_data = self._parse_csv_content(csv_content)
        if not csv_data:
            raise ValueError("Failed to parse CSV content")
        
        headers = csv_data['headers']
        rows = csv_data['rows']
        
        logger.info(f"CSV has {len(headers)} columns and {len(rows)} rows")
        
        # Profile each column
        column_profiles = []
        for i, header in enumerate(headers):
            profile = self._profile_column(header, i, rows)
            column_profiles.append(profile)
        
        # Infer overall schema
        schema_inference = self._infer_schema(
            file_name, headers, rows, column_profiles
        )
        
        logger.info(f"Schema inference completed with confidence: {schema_inference.schema_confidence:.2f}")
        return schema_inference
    
    def _parse_csv_content(self, csv_content: str) -> Optional[Dict[str, Any]]:
        """
        Parse CSV content into headers and rows.
        
        Args:
            csv_content: Raw CSV content
            
        Returns:
            Dictionary with headers and rows, or None if parsing fails
        """
        try:
            # Try different delimiters
            delimiters = [',', ';', '\t', '|']
            
            for delimiter in delimiters:
                try:
                    csv_file = StringIO(csv_content)
                    reader = csv.reader(csv_file, delimiter=delimiter)
                    
                    rows = list(reader)
                    if len(rows) < 2:  # Need at least header + 1 data row
                        continue
                    
                    headers = rows[0]
                    data_rows = rows[1:]
                    
                    # Check if this delimiter makes sense
                    if len(headers) > 1 and all(len(row) == len(headers) for row in data_rows[:10]):
                        logger.info(f"Successfully parsed CSV with delimiter: '{delimiter}'")
                        return {
                            'headers': headers,
                            'rows': data_rows,
                            'delimiter': delimiter
                        }
                        
                except Exception as e:
                    logger.debug(f"Failed to parse with delimiter '{delimiter}': {e}")
                    continue
            
            logger.error("Failed to parse CSV with any delimiter")
            return None
            
        except Exception as e:
            logger.error(f"Error parsing CSV content: {e}")
            return None
    
    def _profile_column(self, column_name: str, column_index: int, rows: List[List[str]]) -> CSVColumnProfile:
        """
        Profile a single column to determine its characteristics.
        
        Args:
            column_name: Name of the column
            column_index: Index of the column
            rows: All data rows
            
        Returns:
            CSVColumnProfile with analysis results
        """
        # Extract column values (limit to sample size)
        column_values = []
        for row in rows[:self.sample_size]:
            if column_index < len(row):
                value = row[column_index].strip()
                if value:  # Skip empty values
                    column_values.append(value)
        
        if not column_values:
            # Handle empty column
            return CSVColumnProfile(
                column_name=column_name,
                column_index=column_index,
                data_type="empty",
                column_type=CSVColumnType.UNKNOWN.value,
                total_values=0,
                unique_values=0,
                null_values=len(rows),
                uniqueness_ratio=0.0
            )
        
        # Calculate statistics
        total_values = len(column_values)
        unique_values = len(set(column_values))
        null_values = len(rows) - total_values
        uniqueness_ratio = unique_values / total_values if total_values > 0 else 0.0
        
        # Infer data type
        data_type = self._infer_data_type(column_values)
        
        # Classify column type
        column_type = self._classify_column_type(column_name, column_values, uniqueness_ratio)
        
        # Infer entity type and relationships
        inferred_entity_type = infer_entity_type_from_column(column_name)
        inferred_relationship = get_relationship_from_column(column_name)
        
        # Determine if it's an identifier or foreign key
        is_identifier = self._is_identifier_column(column_name, column_values, uniqueness_ratio)
        is_foreign_key = self._is_foreign_key_column(column_name, column_values)
        
        # Find matching patterns
        value_patterns = self._find_value_patterns(column_values)
        header_patterns = self._find_header_patterns(column_name)
        
        return CSVColumnProfile(
            column_name=column_name,
            column_index=column_index,
            data_type=data_type,
            column_type=column_type.value,
            total_values=total_values,
            unique_values=unique_values,
            null_values=null_values,
            uniqueness_ratio=uniqueness_ratio,
            sample_values=column_values[:10],  # Store first 10 values as samples
            inferred_entity_type=inferred_entity_type if inferred_entity_type != "Unknown" else None,
            inferred_relationship=inferred_relationship,
            is_identifier=is_identifier,
            is_foreign_key=is_foreign_key,
            value_patterns=value_patterns,
            header_patterns=header_patterns
        )
    
    def _infer_data_type(self, values: List[str]) -> str:
        """
        Infer the data type of column values.
        
        Args:
            values: List of column values
            
        Returns:
            Inferred data type
        """
        if not values:
            return "empty"
        
        # Count different type patterns
        type_counts = {
            'integer': 0,
            'float': 0,
            'date': 0,
            'datetime': 0,
            'email': 0,
            'url': 0,
            'phone': 0,
            'uuid': 0,
            'boolean': 0,
            'text': 0
        }
        
        for value in values[:50]:  # Sample first 50 values
            value = value.strip()
            
            # Check specific patterns
            if re.match(self.value_patterns['email'], value):
                type_counts['email'] += 1
            elif re.match(self.value_patterns['uuid'], value):
                type_counts['uuid'] += 1
            elif re.match(self.value_patterns['url'], value):
                type_counts['url'] += 1
            elif re.match(self.value_patterns['phone'], value):
                type_counts['phone'] += 1
            elif re.match(self.value_patterns['datetime'], value):
                type_counts['datetime'] += 1
            elif re.match(self.value_patterns['date'], value):
                type_counts['date'] += 1
            elif value.lower() in ['true', 'false', 'yes', 'no', '1', '0']:
                type_counts['boolean'] += 1
            elif re.match(r'^\d+$', value):
                type_counts['integer'] += 1
            elif re.match(r'^\d*\.\d+$', value):
                type_counts['float'] += 1
            else:
                type_counts['text'] += 1
        
        # Return the most common type
        return max(type_counts, key=type_counts.get)
    
    def _classify_column_type(self, column_name: str, values: List[str], uniqueness_ratio: float) -> CSVColumnType:
        """
        Classify the column type based on name and values.
        
        Args:
            column_name: Name of the column
            values: Column values
            uniqueness_ratio: Ratio of unique values
            
        Returns:
            CSVColumnType classification
        """
        normalized_name = normalize_column_name(column_name)
        
        # Check for identifier patterns
        if uniqueness_ratio >= self.heuristics['uniqueness_threshold']:
            return CSVColumnType.ENTITY_IDENTIFIER
        
        # Check for foreign key patterns
        if any(pattern in normalized_name for pattern in ['_id', '_key', '_ref']):
            return CSVColumnType.FOREIGN_KEY
        
        # Check for relationship indicators
        if get_relationship_from_column(column_name):
            return CSVColumnType.RELATIONSHIP_INDICATOR
        
        # Check for metadata patterns
        if any(pattern in normalized_name for pattern in ['created', 'updated', 'modified', 'timestamp']):
            return CSVColumnType.METADATA
        
        # Default to attribute
        return CSVColumnType.ENTITY_ATTRIBUTE

    def _is_identifier_column(self, column_name: str, values: List[str], uniqueness_ratio: float) -> bool:
        """
        Determine if column is an entity identifier.

        Args:
            column_name: Name of the column
            values: Column values
            uniqueness_ratio: Ratio of unique values

        Returns:
            True if column is likely an identifier
        """
        # High uniqueness suggests identifier
        if uniqueness_ratio >= self.heuristics['uniqueness_threshold']:
            return True

        # Check for identifier patterns in name
        normalized_name = normalize_column_name(column_name)
        identifier_indicators = ['id', 'key', 'identifier', 'email', 'username']

        return any(indicator in normalized_name for indicator in identifier_indicators)

    def _is_foreign_key_column(self, column_name: str, values: List[str]) -> bool:
        """
        Determine if column is a foreign key.

        Args:
            column_name: Name of the column
            values: Column values

        Returns:
            True if column is likely a foreign key
        """
        normalized_name = normalize_column_name(column_name)

        # Check for foreign key patterns
        fk_patterns = ['_id', '_key', '_ref', 'manager', 'lead', 'owner', 'parent']
        return any(pattern in normalized_name for pattern in fk_patterns)

    def _find_value_patterns(self, values: List[str]) -> List[str]:
        """
        Find matching value patterns in column data.

        Args:
            values: Column values

        Returns:
            List of matching pattern names
        """
        patterns = []
        sample_values = values[:20]  # Check first 20 values

        for pattern_name, pattern_regex in self.value_patterns.items():
            matches = sum(1 for value in sample_values if re.match(pattern_regex, value.strip()))
            if matches > len(sample_values) * 0.5:  # If >50% match
                patterns.append(pattern_name)

        return patterns

    def _find_header_patterns(self, column_name: str) -> List[str]:
        """
        Find matching header patterns for column name.

        Args:
            column_name: Name of the column

        Returns:
            List of matching pattern categories
        """
        patterns = []

        for entity_type, pattern_list in self.entity_patterns.items():
            if match_column_pattern(column_name, pattern_list):
                patterns.append(f"entity_{entity_type.lower()}")

        return patterns

    def _infer_schema(
        self,
        file_name: str,
        headers: List[str],
        rows: List[List[str]],
        column_profiles: List[CSVColumnProfile]
    ) -> CSVSchemaInference:
        """
        Infer the overall schema from column profiles.

        Args:
            file_name: Name of the CSV file
            headers: Column headers
            rows: Data rows
            column_profiles: Analysis results for each column

        Returns:
            Complete schema inference
        """
        # Categorize columns
        identifier_columns = []
        attribute_columns = []
        relationship_columns = []

        # Find primary entity type
        entity_type_votes = Counter()

        for profile in column_profiles:
            if profile.is_identifier:
                identifier_columns.append(profile.column_name)
                if profile.inferred_entity_type:
                    entity_type_votes[profile.inferred_entity_type] += 2  # Higher weight for identifiers
            elif profile.is_foreign_key or profile.inferred_relationship:
                relationship_columns.append(profile.column_name)
            else:
                attribute_columns.append(profile.column_name)

            # Vote for entity type based on all columns
            if profile.inferred_entity_type:
                entity_type_votes[profile.inferred_entity_type] += 1

        # Determine primary entity type
        primary_entity_type = entity_type_votes.most_common(1)[0][0] if entity_type_votes else "Entity"

        # Suggest relationships
        suggested_relationships = self._suggest_relationships(column_profiles, primary_entity_type)

        # Calculate confidence
        schema_confidence = self._calculate_schema_confidence(column_profiles, identifier_columns)

        return CSVSchemaInference(
            file_name=file_name,
            total_rows=len(rows),
            total_columns=len(headers),
            column_profiles=column_profiles,
            primary_entity_type=primary_entity_type,
            identifier_columns=identifier_columns,
            attribute_columns=attribute_columns,
            relationship_columns=relationship_columns,
            suggested_relationships=suggested_relationships,
            schema_confidence=schema_confidence,
            inference_strategy="hybrid"
        )

    def _suggest_relationships(self, column_profiles: List[CSVColumnProfile], primary_entity: str) -> List[Tuple[str, str, str]]:
        """
        Suggest relationships based on column analysis.

        Args:
            column_profiles: Column analysis results
            primary_entity: Primary entity type

        Returns:
            List of suggested relationships as (subject_type, predicate, object_type) tuples
        """
        relationships = []

        for profile in column_profiles:
            if profile.inferred_relationship:
                # Determine target entity type
                target_entity = profile.inferred_entity_type or "Entity"
                relationships.append((primary_entity, profile.inferred_relationship, target_entity))

        return relationships

    def _calculate_schema_confidence(self, column_profiles: List[CSVColumnProfile], identifier_columns: List[str]) -> float:
        """
        Calculate confidence score for the schema inference.

        Args:
            column_profiles: Column analysis results
            identifier_columns: List of identifier column names

        Returns:
            Confidence score between 0.0 and 1.0
        """
        if not column_profiles:
            return 0.0

        confidence_factors = []

        # Factor 1: Presence of clear identifiers
        if identifier_columns:
            confidence_factors.append(0.8)
        else:
            confidence_factors.append(0.3)

        # Factor 2: Clear entity type inference
        entity_inferences = sum(1 for p in column_profiles if p.inferred_entity_type)
        entity_confidence = entity_inferences / len(column_profiles)
        confidence_factors.append(entity_confidence)

        # Factor 3: Clear relationship patterns
        relationship_inferences = sum(1 for p in column_profiles if p.inferred_relationship)
        relationship_confidence = relationship_inferences / len(column_profiles) if relationship_inferences > 0 else 0.5
        confidence_factors.append(relationship_confidence)

        # Factor 4: Data quality (uniqueness ratios)
        avg_uniqueness = sum(p.uniqueness_ratio for p in column_profiles) / len(column_profiles)
        uniqueness_confidence = min(avg_uniqueness * 2, 1.0)  # Scale to 0-1
        confidence_factors.append(uniqueness_confidence)

        # Calculate weighted average
        return sum(confidence_factors) / len(confidence_factors)
