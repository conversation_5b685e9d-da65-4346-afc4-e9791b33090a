#!/usr/bin/env python3
"""
Inspect CSV Graph

Quick script to inspect the created CSV graph and validate its structure.
"""

import os
import sys

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enterprise_kg_minimal.storage.neo4j_client import Neo4jClient, Neo4jConnection


def load_config():
    """Load configuration from .env file."""
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass
    
    return {
        "neo4j_uri": os.getenv("NEO4J_URI"),
        "neo4j_user": os.getenv("NEO4J_USER"),
        "neo4j_password": os.getenv("NEO4J_PASSWORD"),
        "neo4j_database": os.getenv("NEO4J_DATABASE"),
    }


def inspect_graph():
    """Inspect the created CSV graph."""
    config = load_config()
    
    neo4j_conn = Neo4jConnection(
        uri=config["neo4j_uri"],
        user=config["neo4j_user"],
        password=config["neo4j_password"],
        database=config["neo4j_database"]
    )
    neo4j_client = Neo4jClient(neo4j_conn)
    
    print("🔍 Inspecting CSV Graph Structure")
    print("=" * 50)
    
    # 1. Check all node types
    print("\n📊 Node Types and Counts:")
    node_query = "MATCH (n) RETURN labels(n) as labels, count(n) as count ORDER BY count DESC"
    node_results = neo4j_client.execute_query(node_query)
    
    for result in node_results:
        labels = ', '.join(result['labels'])
        count = result['count']
        print(f"   {labels}: {count}")
    
    # 2. Check relationships
    print("\n🔗 Relationship Types and Counts:")
    rel_query = "MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count ORDER BY count DESC"
    rel_results = neo4j_client.execute_query(rel_query)
    
    for result in rel_results:
        rel_type = result['rel_type']
        count = result['count']
        print(f"   {rel_type}: {count}")
    
    # 3. Check DataSource
    print("\n📁 DataSource Information:")
    ds_query = "MATCH (ds:DataSource) WHERE ds.id CONTAINS 'blogs_tracker' RETURN ds"
    ds_results = neo4j_client.execute_query(ds_query)
    
    if ds_results:
        ds = ds_results[0]['ds']
        print(f"   File: {ds.get('name', 'N/A')}")
        print(f"   Type: {ds.get('type', 'N/A')}")
        print(f"   Rows: {ds.get('total_rows', 'N/A')}")
        print(f"   Columns: {ds.get('total_columns', 'N/A')}")
        print(f"   Primary Entity: {ds.get('primary_entity_type', 'N/A')}")
        print(f"   Confidence: {ds.get('schema_confidence', 'N/A')}")
    
    # 4. Sample entities
    print("\n👤 Sample Entities:")
    
    # Check for people
    person_query = "MATCH (p:Person) RETURN p.name as name LIMIT 5"
    person_results = neo4j_client.execute_query(person_query)
    if person_results:
        print("   People:")
        for person in person_results:
            print(f"     - {person['name']}")
    
    # Check for other entities
    entity_query = "MATCH (n) WHERE NOT n:DataSource AND NOT n:Chunk RETURN labels(n) as labels, n.name as name LIMIT 10"
    entity_results = neo4j_client.execute_query(entity_query)
    if entity_results:
        print("   Other Entities:")
        for entity in entity_results:
            labels = ', '.join(entity['labels'])
            name = entity['name']
            print(f"     - {name} ({labels})")
    
    # 5. Sample relationships
    print("\n🔗 Sample Relationships:")
    rel_sample_query = """
    MATCH (a)-[r]->(b) 
    WHERE NOT a:DataSource AND NOT a:Chunk AND NOT b:DataSource AND NOT b:Chunk
    RETURN a.name as source, type(r) as relationship, b.name as target 
    LIMIT 10
    """
    rel_sample_results = neo4j_client.execute_query(rel_sample_query)
    
    if rel_sample_results:
        for rel in rel_sample_results:
            source = rel['source']
            relationship = rel['relationship']
            target = rel['target']
            print(f"   {source} -> {relationship} -> {target}")
    
    # 6. Check chunks
    print("\n📦 Chunk Information:")
    chunk_query = "MATCH (c:Chunk) WHERE c.id CONTAINS 'blogs_tracker' RETURN count(c) as chunk_count"
    chunk_results = neo4j_client.execute_query(chunk_query)
    
    if chunk_results:
        chunk_count = chunk_results[0]['chunk_count']
        print(f"   Total Chunks: {chunk_count}")
        
        # Sample chunk details
        chunk_detail_query = "MATCH (c:Chunk) WHERE c.id CONTAINS 'blogs_tracker' RETURN c LIMIT 1"
        chunk_detail_results = neo4j_client.execute_query(chunk_detail_query)
        
        if chunk_detail_results:
            chunk = chunk_detail_results[0]['c']
            print(f"   Sample Chunk:")
            print(f"     Start Row: {chunk.get('start_row', 'N/A')}")
            print(f"     End Row: {chunk.get('end_row', 'N/A')}")
            print(f"     Row Count: {chunk.get('row_count', 'N/A')}")
            print(f"     Strategy: {chunk.get('processing_strategy', 'N/A')}")
    
    # 7. Graph connectivity
    print("\n🌐 Graph Connectivity:")
    connectivity_query = """
    MATCH (ds:DataSource)-[:CONTAINS]->(c:Chunk)-[:EXTRACTED_FROM]->(e)
    WHERE ds.id CONTAINS 'blogs_tracker'
    RETURN count(DISTINCT e) as connected_entities
    """
    connectivity_results = neo4j_client.execute_query(connectivity_query)
    
    if connectivity_results:
        connected_entities = connectivity_results[0]['connected_entities']
        print(f"   Entities connected to chunks: {connected_entities}")
    
    neo4j_client.close()
    
    print("\n✅ Graph inspection completed!")


if __name__ == "__main__":
    inspect_graph()
