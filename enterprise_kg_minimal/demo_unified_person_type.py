#!/usr/bin/env python3
"""
Demo script showing unified Person type implementation in action.

This script demonstrates:
1. Processing a document with various person roles
2. Creating Person entities with role/persona properties
3. Querying the knowledge graph for people with different roles
"""

import sys
import os
import logging

# Add the parent directory to the path so we can import the modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_sample_document():
    """Create a sample document with various person roles."""
    return """
    Project Alpha Status Report
    
    Executive Summary:
    <PERSON>, CEO of TechCorp, has approved the budget for Project Alpha. 
    The project is being led by <PERSON>, who serves as the Project Manager.
    
    Team Structure:
    - <PERSON> (Manager) oversees the entire project
    - <PERSON> (Senior Developer) leads the technical implementation
    - <PERSON> (CTO) provides strategic technical guidance
    - <PERSON> (Analyst) handles data analysis and reporting
    - <PERSON> (Designer) manages the user experience design
    
    Recent Updates:
    The CEO <PERSON> met with the CTO <PERSON> to discuss the project timeline.
    <PERSON> reported that <PERSON> has completed the core architecture.
    <PERSON> presented his analysis to the management team.
    <PERSON> collaborated with <PERSON> on the user interface design.
    
    Next Steps:
    <PERSON> will review the technical specifications.
    <PERSON> will coordinate with all team members for the next phase.
    <PERSON> expects a final presentation by the end of the month.
    """

def demo_document_processing():
    """Demonstrate document processing with unified Person types."""
    print("🚀 Demo: Unified Person Type Implementation")
    print("=" * 60)
    
    try:
        from enterprise_kg_minimal import process_document
        
        # Create sample document
        sample_doc = create_sample_document()
        file_id = "project_alpha_report_demo"
        
        print("📄 Processing sample document...")
        print("Document preview:")
        print(sample_doc[:200] + "...")
        print()
        
        # Process the document
        result = process_document(
            file_id=file_id,
            file_content=sample_doc,
            chunking_strategy="sentence",
            enable_coreference_resolution=True
        )
        
        if result["success"]:
            print("✅ Document processed successfully!")
            print(f"📊 Created {result['chunks_created']} chunks")
            print(f"📊 Extracted {result['total_entities']} entities")
            print(f"📊 Created {result['total_relationships']} relationships")
            print()
            
            return True
        else:
            print(f"❌ Document processing failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return False

def demo_person_queries():
    """Demonstrate querying for Person entities with different roles."""
    print("🔍 Querying Person entities...")
    
    try:
        from enterprise_kg_minimal.storage.neo4j_client import create_default_neo4j_client
        
        # Create Neo4j client
        client = create_default_neo4j_client()
        
        # Query all Person entities
        query = """
        MATCH (p:Person)
        RETURN p.name as name, 
               p.role as role, 
               p.persona as persona,
               p.entity_type as type
        ORDER BY p.name
        """
        
        results = client.execute_query(query)
        
        if results:
            print("👥 Found Person entities:")
            print("-" * 40)
            for record in results:
                name = record.get('name', 'Unknown')
                role = record.get('role', 'Not specified')
                persona = record.get('persona', 'Not specified')
                entity_type = record.get('type', 'Unknown')
                
                print(f"Name: {name}")
                print(f"  Type: {entity_type}")
                print(f"  Role: {role}")
                print(f"  Persona: {persona}")
                print()
        else:
            print("No Person entities found in the database.")
            
        # Query relationships involving Person entities
        print("🔗 Person relationships:")
        print("-" * 40)
        
        rel_query = """
        MATCH (p:Person)-[r]->(other)
        RETURN p.name as person_name, 
               p.role as person_role,
               type(r) as relationship_type, 
               other.name as other_name,
               labels(other)[0] as other_type
        ORDER BY p.name
        LIMIT 10
        """
        
        rel_results = client.execute_query(rel_query)
        
        for record in rel_results:
            person_name = record.get('person_name', 'Unknown')
            person_role = record.get('person_role', '')
            rel_type = record.get('relationship_type', 'Unknown')
            other_name = record.get('other_name', 'Unknown')
            other_type = record.get('other_type', 'Unknown')
            
            role_info = f" ({person_role})" if person_role else ""
            print(f"{person_name}{role_info} --{rel_type}--> {other_name} ({other_type})")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ Query demo failed: {e}")
        return False

def demo_search_with_person_filter():
    """Demonstrate search functionality with Person type filter."""
    print("\n🔍 Search Demo with Person Filter...")
    
    try:
        from enterprise_kg_minimal.search import create_hybrid_search_engine, SearchStrategy
        
        # Create search engine
        search_engine = create_hybrid_search_engine()
        
        # Dummy chunk indices (in real scenario, these come from vector search)
        chunk_indices = ["project_alpha_report_demo_chunk_0", "project_alpha_report_demo_chunk_1"]
        
        # Search with Person entity filter
        result = search_engine.search(
            chunk_indices=chunk_indices,
            query_text="Who are the key people involved in the project?",
            strategy=SearchStrategy.ENTITY_CENTRIC,
            entity_types={"Person", "Project"},  # Unified Person type
            relationship_types={"manages", "leads", "works_for", "involved_in"},
            max_results=20
        )
        
        print("📊 Search Results:")
        print(f"Entities found: {len(result.graph_context.entities)}")
        print(f"Relationships found: {len(result.graph_context.relationships)}")
        print(f"Entity types: {result.graph_context.entity_types_found}")
        print()
        
        # Show Person entities found
        person_entities = [e for e in result.graph_context.entities if e.entity_type == "Person"]
        if person_entities:
            print("👥 Person entities found:")
            for entity in person_entities[:5]:  # Show first 5
                print(f"- {entity.name} (Type: {entity.entity_type})")
        
        search_engine.neo4j_client.close()
        return True
        
    except Exception as e:
        print(f"❌ Search demo failed: {e}")
        return False

def cleanup_demo_data():
    """Clean up demo data from Neo4j."""
    print("\n🧹 Cleaning up demo data...")
    
    try:
        from enterprise_kg_minimal.storage.neo4j_client import create_default_neo4j_client
        
        client = create_default_neo4j_client()
        
        # Delete demo file and related data
        cleanup_query = """
        MATCH (f:File {id: 'project_alpha_report_demo'})
        OPTIONAL MATCH (f)-[:CONTAINS]->(c:Chunk)
        OPTIONAL MATCH (c)-[:EXTRACTED_FROM]->(e)
        DETACH DELETE f, c, e
        """
        
        client.execute_query(cleanup_query)
        client.close()
        
        print("✅ Demo data cleaned up")
        return True
        
    except Exception as e:
        print(f"❌ Cleanup failed: {e}")
        return False

def main():
    """Run the complete demo."""
    print("🎯 Unified Person Type Demo")
    print("This demo shows how the system now uses a single 'Person' entity type")
    print("with role and persona properties instead of separate entity types.")
    print()
    
    # Run demo steps
    steps = [
        ("Document Processing", demo_document_processing),
        ("Person Queries", demo_person_queries),
        ("Search with Person Filter", demo_search_with_person_filter),
        ("Cleanup", cleanup_demo_data)
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            success = step_func()
            if not success:
                print(f"⚠️  {step_name} completed with issues")
        except Exception as e:
            print(f"❌ {step_name} failed: {e}")
    
    print("\n" + "="*60)
    print("🎉 Demo completed!")
    print("\nKey improvements with unified Person type:")
    print("✅ Single 'Person' entity type for all people")
    print("✅ Role information stored as properties (CEO, Manager, Developer, etc.)")
    print("✅ Persona information extracted (leadership, technical, analytical, etc.)")
    print("✅ Simplified search and query logic")
    print("✅ Better entity consistency across documents")

if __name__ == "__main__":
    main()
