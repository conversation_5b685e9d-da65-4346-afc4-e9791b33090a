"""
Cluster Analysis Utilities for Coreference Resolution

This module provides tools to analyze and identify entity clusters
in the knowledge graph for improved search capabilities.
"""

import logging
from typing import List, Dict, Any, Set, Tuple
from collections import defaultdict
import re
from ..storage.neo4j_client import Neo4jClient, Neo4jConnection

logger = logging.getLogger(__name__)


class ClusterAnalyzer:
    """
    Analyzes entity clusters in the knowledge graph for search optimization.
    
    This class helps identify:
    - Entities that likely refer to the same real-world entity
    - Cross-chunk entity connections (evidence of coreference resolution)
    - Potential search aliases and synonyms
    """
    
    def __init__(self, neo4j_client: Neo4jClient):
        """Initialize the cluster analyzer."""
        self.client = neo4j_client
    
    def find_cross_chunk_entities(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Find entities that appear across multiple chunks (evidence of coreference resolution).
        
        Args:
            limit: Maximum number of entities to return
            
        Returns:
            List of entities with their chunk counts and details
        """
        query = """
        MATCH (c:Chunk)-[:EXTRACTED_FROM]->(e:Entity)
        WITH e, collect(DISTINCT c) as chunks, count(DISTINCT c) as chunk_count
        WHERE chunk_count > 1
        RETURN e.name as entity_name, 
               e.entity_type as entity_type,
               chunk_count,
               [chunk IN chunks | chunk.id] as chunk_ids,
               [chunk IN chunks | chunk.file_id] as file_ids
        ORDER BY chunk_count DESC
        LIMIT $limit
        """
        
        results = self.client.execute_query(query, {"limit": limit})
        return [dict(record) for record in results]
    
    def find_potential_name_clusters(self, entity_type: str = None) -> List[Dict[str, Any]]:
        """
        Find entities that might be name variations of the same entity.
        
        Args:
            entity_type: Optional filter by entity type (e.g., "Person", "Company")
            
        Returns:
            List of potential clusters based on name similarity
        """
        # Get all entities of the specified type
        if entity_type:
            query = """
            MATCH (e:Entity)
            WHERE e.entity_type = $entity_type
            RETURN e.name as name, e.entity_type as entity_type
            ORDER BY e.name
            """
            params = {"entity_type": entity_type}
        else:
            query = """
            MATCH (e:Entity)
            RETURN e.name as name, e.entity_type as entity_type
            ORDER BY e.name
            """
            params = {}
        
        results = self.client.execute_query(query, params)
        entities = [dict(record) for record in results]
        
        # Group by entity type and find potential clusters
        clusters = []
        entities_by_type = defaultdict(list)
        
        for entity in entities:
            entities_by_type[entity['entity_type']].append(entity['name'])
        
        for etype, names in entities_by_type.items():
            type_clusters = self._find_name_clusters(names, etype)
            for cluster in type_clusters:
                clusters.append({
                    'entity_type': etype,
                    'potential_cluster': cluster,
                    'cluster_size': len(cluster)
                })
        
        return clusters
    
    def _find_name_clusters(self, names: List[str], entity_type: str) -> List[List[str]]:
        """Find potential clusters within a list of names."""
        clusters = []
        processed = set()
        
        for i, name1 in enumerate(names):
            if name1 in processed:
                continue
                
            cluster = [name1]
            processed.add(name1)
            
            for j, name2 in enumerate(names[i+1:], i+1):
                if name2 in processed:
                    continue
                    
                if self._are_names_similar(name1, name2, entity_type):
                    cluster.append(name2)
                    processed.add(name2)
            
            if len(cluster) > 1:  # Only include actual clusters
                clusters.append(cluster)
        
        return clusters
    
    def _are_names_similar(self, name1: str, name2: str, entity_type: str) -> bool:
        """Check if two names might refer to the same entity."""
        name1_clean = name1.lower().strip()
        name2_clean = name2.lower().strip()
        
        # Exact match
        if name1_clean == name2_clean:
            return True
        
        # Handle person names
        if entity_type in ['Person', 'Employee', 'Manager', 'Executive']:
            return self._are_person_names_similar(name1_clean, name2_clean)
        
        # Handle company names
        if entity_type in ['Company', 'Organization', 'Department']:
            return self._are_company_names_similar(name1_clean, name2_clean)
        
        # Handle system/technology names
        if entity_type in ['System', 'Technology', 'Tool', 'Platform']:
            return self._are_system_names_similar(name1_clean, name2_clean)
        
        return False
    
    def _are_person_names_similar(self, name1: str, name2: str) -> bool:
        """Check if two person names might refer to the same person."""
        # Remove titles and honorifics
        titles = ['dr.', 'mr.', 'ms.', 'mrs.', 'prof.', 'professor', 'doctor']
        
        for title in titles:
            name1 = re.sub(rf'\b{title}\s*', '', name1)
            name2 = re.sub(rf'\b{title}\s*', '', name2)
        
        words1 = set(name1.split())
        words2 = set(name2.split())
        
        # Check if one name is a subset of another (e.g., "John Smith" vs "John")
        if words1.issubset(words2) or words2.issubset(words1):
            return True
        
        # Check for common name patterns
        if len(words1) >= 2 and len(words2) >= 2:
            # Check if they share first and last name
            if words1 & words2:  # Have common words
                return len(words1 & words2) >= 2
        
        return False
    
    def _are_company_names_similar(self, name1: str, name2: str) -> bool:
        """Check if two company names might refer to the same company."""
        # Remove common company suffixes
        suffixes = ['inc.', 'inc', 'corp.', 'corp', 'ltd.', 'ltd', 'llc', 'company', 'co.']
        
        for suffix in suffixes:
            name1 = re.sub(rf'\s*{suffix}$', '', name1)
            name2 = re.sub(rf'\s*{suffix}$', '', name2)
        
        # Check for abbreviations (e.g., "IBM" vs "International Business Machines")
        if len(name1) <= 5 and len(name2) > 10:
            # Check if short name could be acronym of long name
            words = name2.split()
            if len(words) >= len(name1):
                acronym = ''.join(word[0] for word in words[:len(name1)])
                if acronym.lower() == name1.lower():
                    return True
        
        return name1 == name2
    
    def _are_system_names_similar(self, name1: str, name2: str) -> bool:
        """Check if two system names might refer to the same system."""
        # Handle common abbreviations
        abbreviations = {
            'crm': 'customer relationship management',
            'erp': 'enterprise resource planning',
            'ai': 'artificial intelligence',
            'ml': 'machine learning',
            'api': 'application programming interface'
        }
        
        # Expand abbreviations
        expanded1 = abbreviations.get(name1.lower(), name1.lower())
        expanded2 = abbreviations.get(name2.lower(), name2.lower())
        
        if expanded1 == expanded2:
            return True
        
        # Check if one contains the other
        if expanded1 in expanded2 or expanded2 in expanded1:
            return True
        
        return False
    
    def generate_search_aliases(self, entity_name: str, entity_type: str) -> List[str]:
        """
        Generate potential search aliases for an entity.
        
        Args:
            entity_name: The entity name to generate aliases for
            entity_type: The type of entity
            
        Returns:
            List of potential aliases for search
        """
        aliases = [entity_name]  # Include original name
        
        if entity_type in ['Person', 'Employee', 'Manager', 'Executive']:
            aliases.extend(self._generate_person_aliases(entity_name))
        elif entity_type in ['Company', 'Organization']:
            aliases.extend(self._generate_company_aliases(entity_name))
        elif entity_type in ['System', 'Technology', 'Tool']:
            aliases.extend(self._generate_system_aliases(entity_name))
        
        return list(set(aliases))  # Remove duplicates
    
    def _generate_person_aliases(self, name: str) -> List[str]:
        """Generate aliases for person names."""
        aliases = []
        words = name.split()
        
        if len(words) >= 2:
            # First name only
            aliases.append(words[0])
            # Last name only
            aliases.append(words[-1])
            # First + Last (skip middle names)
            if len(words) > 2:
                aliases.append(f"{words[0]} {words[-1]}")
        
        return aliases
    
    def _generate_company_aliases(self, name: str) -> List[str]:
        """Generate aliases for company names."""
        aliases = []
        
        # Remove common suffixes for search
        suffixes = ['Inc.', 'Corp.', 'Ltd.', 'LLC', 'Company']
        clean_name = name
        for suffix in suffixes:
            clean_name = re.sub(rf'\s*{suffix}$', '', clean_name, flags=re.IGNORECASE)
        
        if clean_name != name:
            aliases.append(clean_name)
        
        return aliases
    
    def _generate_system_aliases(self, name: str) -> List[str]:
        """Generate aliases for system names."""
        aliases = []
        
        # Common system abbreviations
        if 'customer relationship management' in name.lower():
            aliases.append('CRM')
        if 'enterprise resource planning' in name.lower():
            aliases.append('ERP')
        if 'artificial intelligence' in name.lower():
            aliases.append('AI')
        
        return aliases
