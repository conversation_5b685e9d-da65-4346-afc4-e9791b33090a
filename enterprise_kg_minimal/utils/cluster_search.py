"""
Cluster-Aware Search Utilities

This module provides search capabilities that leverage coreference resolution
clusters to improve search accuracy and recall.
"""

import logging
from typing import List, Dict, Any, Set, Optional
from ..storage.neo4j_client import Neo4jClient, Neo4jConnection
from .cluster_analyzer import ClusterAnalyzer

logger = logging.getLogger(__name__)


class ClusterAwareSearch:
    """
    Search engine that uses coreference resolution clusters for improved results.
    
    Features:
    - Expands search queries to include entity aliases
    - Finds related entities through cluster connections
    - Provides context-aware search results
    """
    
    def __init__(self, neo4j_client: Neo4jClient):
        """Initialize the cluster-aware search."""
        self.client = neo4j_client
        self.analyzer = ClusterAnalyzer(neo4j_client)
    
    def search_entity_with_clusters(self, query: str, entity_type: str = None, limit: int = 20) -> Dict[str, Any]:
        """
        Search for entities using cluster-aware expansion.
        
        Args:
            query: Search query (entity name or partial name)
            entity_type: Optional filter by entity type
            limit: Maximum number of results
            
        Returns:
            Dictionary with search results and cluster information
        """
        # Step 1: Find direct matches
        direct_matches = self._find_direct_matches(query, entity_type, limit)
        
        # Step 2: Find potential cluster matches
        cluster_matches = self._find_cluster_matches(query, entity_type, limit)
        
        # Step 3: Find cross-chunk entities (high confidence clusters)
        cross_chunk_matches = self._find_cross_chunk_matches(query, entity_type, limit)
        
        # Step 4: Combine and rank results
        all_results = self._combine_and_rank_results(direct_matches, cluster_matches, cross_chunk_matches)
        
        return {
            'query': query,
            'total_results': len(all_results),
            'results': all_results[:limit],
            'search_strategy': {
                'direct_matches': len(direct_matches),
                'cluster_matches': len(cluster_matches),
                'cross_chunk_matches': len(cross_chunk_matches)
            }
        }
    
    def _find_direct_matches(self, query: str, entity_type: str = None, limit: int = 20) -> List[Dict[str, Any]]:
        """Find entities that directly match the query."""
        where_clause = "WHERE e.name CONTAINS $query"
        params = {"query": query, "limit": limit}
        
        if entity_type:
            where_clause += " AND e.entity_type = $entity_type"
            params["entity_type"] = entity_type
        
        cypher_query = f"""
        MATCH (e:Entity)
        {where_clause}
        RETURN e.name as entity_name,
               e.entity_type as entity_type,
               properties(e) as properties,
               'direct' as match_type,
               1.0 as confidence_score
        ORDER BY e.name
        LIMIT $limit
        """
        
        results = self.client.execute_query(cypher_query, params)
        return [dict(record) for record in results]
    
    def _find_cluster_matches(self, query: str, entity_type: str = None, limit: int = 20) -> List[Dict[str, Any]]:
        """Find entities through potential cluster relationships."""
        # This is a simplified approach - in a full implementation,
        # you'd want to store cluster information in the graph
        
        # For now, we'll use name similarity to find potential matches
        all_entities_query = """
        MATCH (e:Entity)
        WHERE ($entity_type IS NULL OR e.entity_type = $entity_type)
        RETURN e.name as entity_name, e.entity_type as entity_type, properties(e) as properties
        """
        
        params = {"entity_type": entity_type}
        all_entities = self.client.execute_query(all_entities_query, params)
        
        cluster_matches = []
        for entity in all_entities:
            # Generate aliases and check if query matches any
            aliases = self.analyzer.generate_search_aliases(entity['entity_name'], entity['entity_type'])
            
            for alias in aliases:
                if query.lower() in alias.lower() and alias.lower() != entity['entity_name'].lower():
                    cluster_matches.append({
                        'entity_name': entity['entity_name'],
                        'entity_type': entity['entity_type'],
                        'properties': entity['properties'],
                        'match_type': 'cluster_alias',
                        'matched_alias': alias,
                        'confidence_score': 0.8
                    })
                    break  # Only add once per entity
        
        return cluster_matches[:limit]
    
    def _find_cross_chunk_matches(self, query: str, entity_type: str = None, limit: int = 20) -> List[Dict[str, Any]]:
        """Find entities that appear across multiple chunks (high confidence clusters)."""
        where_clause = "WHERE e.name CONTAINS $query"
        params = {"query": query, "limit": limit}
        
        if entity_type:
            where_clause += " AND e.entity_type = $entity_type"
            params["entity_type"] = entity_type
        
        cypher_query = f"""
        MATCH (c:Chunk)-[:EXTRACTED_FROM]->(e:Entity)
        {where_clause}
        WITH e, count(DISTINCT c) as chunk_count
        WHERE chunk_count > 1
        RETURN e.name as entity_name,
               e.entity_type as entity_type,
               properties(e) as properties,
               'cross_chunk' as match_type,
               chunk_count,
               0.9 as confidence_score
        ORDER BY chunk_count DESC, e.name
        LIMIT $limit
        """
        
        results = self.client.execute_query(cypher_query, params)
        return [dict(record) for record in results]
    
    def _combine_and_rank_results(self, *result_lists) -> List[Dict[str, Any]]:
        """Combine and rank search results by confidence and relevance."""
        all_results = []
        seen_entities = set()
        
        # Combine all results, avoiding duplicates
        for result_list in result_lists:
            for result in result_list:
                entity_key = (result['entity_name'], result['entity_type'])
                if entity_key not in seen_entities:
                    all_results.append(result)
                    seen_entities.add(entity_key)
        
        # Sort by confidence score (descending) then by name
        all_results.sort(key=lambda x: (-x['confidence_score'], x['entity_name']))
        
        return all_results
    
    def find_related_entities(self, entity_name: str, entity_type: str = None, max_depth: int = 2) -> Dict[str, Any]:
        """
        Find entities related to the given entity through relationships.
        
        Args:
            entity_name: Name of the entity to find relations for
            entity_type: Optional entity type filter
            max_depth: Maximum relationship depth to traverse
            
        Returns:
            Dictionary with related entities and relationship paths
        """
        where_clause = "WHERE e.name = $entity_name"
        params = {"entity_name": entity_name, "max_depth": max_depth}
        
        if entity_type:
            where_clause += " AND e.entity_type = $entity_type"
            params["entity_type"] = entity_type
        
        # Find directly related entities
        cypher_query = f"""
        MATCH (e:Entity)
        {where_clause}
        MATCH path = (e)-[*1..$max_depth]-(related:Entity)
        WHERE related <> e
        RETURN related.name as related_entity,
               related.entity_type as related_type,
               length(path) as relationship_distance,
               [rel in relationships(path) | type(rel)] as relationship_types
        ORDER BY relationship_distance, related.name
        LIMIT 50
        """
        
        results = self.client.execute_query(cypher_query, params)
        
        # Group by relationship distance
        related_by_distance = {}
        for record in results:
            distance = record['relationship_distance']
            if distance not in related_by_distance:
                related_by_distance[distance] = []
            
            related_by_distance[distance].append({
                'entity_name': record['related_entity'],
                'entity_type': record['related_type'],
                'relationship_types': record['relationship_types']
            })
        
        return {
            'source_entity': entity_name,
            'source_type': entity_type,
            'related_entities': related_by_distance,
            'total_related': sum(len(entities) for entities in related_by_distance.values())
        }
    
    def search_by_relationship(self, relationship_type: str, entity_name: str = None, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Search for entities connected by a specific relationship type.
        
        Args:
            relationship_type: Type of relationship to search for
            entity_name: Optional entity name to start from
            limit: Maximum number of results
            
        Returns:
            List of relationship matches
        """
        if entity_name:
            cypher_query = f"""
            MATCH (e1:Entity)-[r:{relationship_type.upper()}]->(e2:Entity)
            WHERE e1.name CONTAINS $entity_name OR e2.name CONTAINS $entity_name
            RETURN e1.name as source_entity,
                   e1.entity_type as source_type,
                   type(r) as relationship_type,
                   e2.name as target_entity,
                   e2.entity_type as target_type,
                   properties(r) as relationship_properties
            ORDER BY e1.name, e2.name
            LIMIT $limit
            """
            params = {"entity_name": entity_name, "limit": limit}
        else:
            cypher_query = f"""
            MATCH (e1:Entity)-[r:{relationship_type.upper()}]->(e2:Entity)
            RETURN e1.name as source_entity,
                   e1.entity_type as source_type,
                   type(r) as relationship_type,
                   e2.name as target_entity,
                   e2.entity_type as target_type,
                   properties(r) as relationship_properties
            ORDER BY e1.name, e2.name
            LIMIT $limit
            """
            params = {"limit": limit}
        
        results = self.client.execute_query(cypher_query, params)
        return [dict(record) for record in results]
