#!/usr/bin/env python3
"""
Test script to validate Query Analyzer improvements and bias fixes.

This script tests the improved query analyzer against various query types
to ensure proper routing decisions and reduced bias.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enterprise_kg_minimal.search import (
    QueryAnalyzer, 
    SearchRouter, 
    VectorMetadataAnalyzer,
    QueryComplexity,
    RoutingDecision
)

def test_query_analyzer_improvements():
    """Test the improved query analyzer with comprehensive test cases."""
    
    print("=" * 80)
    print("QUERY ANALYZER IMPROVEMENTS VALIDATION")
    print("=" * 80)
    
    # Initialize components
    query_analyzer = QueryAnalyzer()
    search_router = SearchRouter()
    metadata_analyzer = VectorMetadataAnalyzer()
    
    # Comprehensive test cases covering different query types
    test_cases = [
        # Simple factual queries - should prefer semantic search
        {
            "query": "What is the company leave policy?",
            "expected_complexity": QueryComplexity.SIMPLE,
            "expected_routing": RoutingDecision.SEMANTIC_ONLY,
            "category": "Simple Factual"
        },
        {
            "query": "Contact information for HR department",
            "expected_complexity": QueryComplexity.SIMPLE,
            "expected_routing": RoutingDecision.SEMANTIC_ONLY,
            "category": "Simple Contact"
        },
        {
            "query": "What is the definition of remote work policy?",
            "expected_complexity": QueryComplexity.SIMPLE,
            "expected_routing": RoutingDecision.SEMANTIC_ONLY,
            "category": "Simple Definition"
        },
        
        # Relational queries - should use graph traversal
        {
            "query": "How are employees connected to their managers?",
            "expected_complexity": QueryComplexity.COMPLEX,
            "expected_routing": RoutingDecision.FULL_GRAPH,
            "category": "Relational Connection"
        },
        {
            "query": "What is the relationship between IT department and security policies?",
            "expected_complexity": QueryComplexity.COMPLEX,
            "expected_routing": RoutingDecision.FULL_GRAPH,
            "category": "Relational Department"
        },
        {
            "query": "How do projects relate to their stakeholders?",
            "expected_complexity": QueryComplexity.COMPLEX,
            "expected_routing": RoutingDecision.FULL_GRAPH,
            "category": "Relational Project"
        },
        
        # Hierarchical queries - should use full graph with hierarchical strategy
        {
            "query": "Who reports to the CEO and what are their responsibilities?",
            "expected_complexity": QueryComplexity.COMPLEX,
            "expected_routing": RoutingDecision.FULL_GRAPH,
            "category": "Hierarchical CEO"
        },
        {
            "query": "Who manages the engineering team?",
            "expected_complexity": QueryComplexity.COMPLEX,
            "expected_routing": RoutingDecision.FULL_GRAPH,
            "category": "Hierarchical Management"
        },
        {
            "query": "What is the organizational structure of the marketing department?",
            "expected_complexity": QueryComplexity.COMPLEX,
            "expected_routing": RoutingDecision.FULL_GRAPH,
            "category": "Hierarchical Structure"
        },
        
        # Moderate complexity queries - should use light graph or adaptive
        {
            "query": "Which projects involve the data science team?",
            "expected_complexity": QueryComplexity.MODERATE,
            "expected_routing": [RoutingDecision.LIGHT_GRAPH, RoutingDecision.FULL_GRAPH],
            "category": "Moderate Project"
        },
        {
            "query": "What systems are used by the finance department?",
            "expected_complexity": QueryComplexity.MODERATE,
            "expected_routing": [RoutingDecision.LIGHT_GRAPH, RoutingDecision.FULL_GRAPH],
            "category": "Moderate System"
        }
    ]
    
    # Track results
    results = {
        "total_tests": len(test_cases),
        "complexity_correct": 0,
        "routing_correct": 0,
        "bias_issues": [],
        "improvements": []
    }
    
    print(f"\nTesting {len(test_cases)} query scenarios...\n")
    
    for i, test_case in enumerate(test_cases, 1):
        query = test_case["query"]
        expected_complexity = test_case["expected_complexity"]
        expected_routing = test_case["expected_routing"]
        category = test_case["category"]
        
        print(f"{i:2d}. {category}: {query}")
        
        # Analyze query
        analysis = query_analyzer.analyze_query(query)
        
        # Create dummy metadata and route
        dummy_metadata = metadata_analyzer.create_dummy_metadata(
            [f"chunk_{i}_1", f"chunk_{i}_2"], query
        )
        routing_result = search_router.route_query(query, [f"chunk_{i}_1", f"chunk_{i}_2"], dummy_metadata)
        
        # Check complexity
        complexity_correct = analysis.query_complexity == expected_complexity
        if complexity_correct:
            results["complexity_correct"] += 1
        else:
            results["bias_issues"].append(f"Query {i}: Expected {expected_complexity.value}, got {analysis.query_complexity.value}")
        
        # Check routing (handle multiple acceptable routings)
        if isinstance(expected_routing, list):
            routing_correct = routing_result.decision in expected_routing
        else:
            routing_correct = routing_result.decision == expected_routing
        
        if routing_correct:
            results["routing_correct"] += 1
        else:
            expected_str = expected_routing.value if not isinstance(expected_routing, list) else [r.value for r in expected_routing]
            results["bias_issues"].append(f"Query {i}: Expected {expected_str}, got {routing_result.decision.value}")
        
        # Print results
        complexity_status = "✅" if complexity_correct else "❌"
        routing_status = "✅" if routing_correct else "❌"
        
        print(f"    Complexity: {analysis.query_complexity.value} {complexity_status}")
        print(f"    Routing: {routing_result.decision.value} {routing_status}")
        print(f"    Confidence: {routing_result.confidence:.2f}")
        print(f"    Score: {analysis.complexity_score:.2f}")
        print(f"    Entities: {analysis.detected_entities}")
        print(f"    Relationships: {analysis.detected_relationships}")
        print()
    
    # Print summary
    print("=" * 80)
    print("VALIDATION SUMMARY")
    print("=" * 80)
    
    complexity_accuracy = (results["complexity_correct"] / results["total_tests"]) * 100
    routing_accuracy = (results["routing_correct"] / results["total_tests"]) * 100
    
    print(f"Total Tests: {results['total_tests']}")
    print(f"Complexity Classification Accuracy: {complexity_accuracy:.1f}% ({results['complexity_correct']}/{results['total_tests']})")
    print(f"Routing Decision Accuracy: {routing_accuracy:.1f}% ({results['routing_correct']}/{results['total_tests']})")
    
    if results["bias_issues"]:
        print(f"\nRemaining Issues ({len(results['bias_issues'])}):")
        for issue in results["bias_issues"]:
            print(f"  - {issue}")
    else:
        print("\n✅ No bias issues detected!")
    
    # Overall assessment
    if complexity_accuracy >= 80 and routing_accuracy >= 80:
        print("\n🎉 VALIDATION PASSED: Query analyzer improvements are working well!")
    elif complexity_accuracy >= 70 and routing_accuracy >= 70:
        print("\n⚠️  VALIDATION PARTIAL: Some improvements needed but generally working.")
    else:
        print("\n❌ VALIDATION FAILED: Significant bias issues remain.")
    
    return results

if __name__ == "__main__":
    test_query_analyzer_improvements()
